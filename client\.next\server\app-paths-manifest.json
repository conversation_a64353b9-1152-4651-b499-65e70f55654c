{"/(auth)/sign-in/page": "app-edge-has-no-entrypoint", "/(dashboard)/dashboard/admin/page": "app-edge-has-no-entrypoint", "/(dashboard)/dashboard/client/favorites/page": "app-edge-has-no-entrypoint", "/(dashboard)/dashboard/client/page": "app-edge-has-no-entrypoint", "/(dashboard)/dashboard/client/search/page": "app-edge-has-no-entrypoint", "/(dashboard)/dashboard/host/page": "app-edge-has-no-entrypoint", "/(dashboard)/dashboard/host/settings/page": "app-edge-has-no-entrypoint", "/(dashboard)/dashboard/page": "app-edge-has-no-entrypoint", "/(root)/page": "app-edge-has-no-entrypoint", "/(root)/vehicles/[id]/page": "app-edge-has-no-entrypoint", "/(root)/vehicles/page": "app-edge-has-no-entrypoint", "/_not-found/page": "app-edge-has-no-entrypoint", "/api/proxy/[...path]/route": "app-edge-has-no-entrypoint", "/api/set-private-token/route": "app-edge-has-no-entrypoint", "/favicon.ico/route": "app/favicon.ico/route.js"}