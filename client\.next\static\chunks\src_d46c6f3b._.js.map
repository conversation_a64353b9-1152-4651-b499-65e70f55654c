{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/api/favorites.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\nimport { Vehicle } from './vehicles.api';\n\nexport interface FavoriteResponse {\n  success: boolean;\n  message?: string;\n  data?: Vehicle[];\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nexport interface FavoriteStatusResponse {\n  success: boolean;\n  message?: string;\n  isFavorite: boolean;\n}\n\nexport interface MultipleFavoriteStatusResponse {\n  success: boolean;\n  message?: string;\n  data: Record<string, boolean>;\n}\n\nexport interface AddFavoriteResponse {\n  success: boolean;\n  message?: string;\n  data?: any;\n}\n\nexport const favoritesApi = {\n  // Obtener favoritos del usuario con paginación\n  getFavorites: async (params: { page: number; limit: number }) => {\n    const result = await apiService.get<FavoriteResponse>('/client/favorites', { params });\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Agregar vehículo a favoritos\n  addToFavorites: async (vehicleId: string) => {\n    const result = await apiService.post<AddFavoriteResponse>(`/client/favorites/${vehicleId}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Remover vehículo de favoritos\n  removeFromFavorites: async (vehicleId: string) => {\n    const result = await apiService.delete<{ success: boolean; message?: string }>(`/client/favorites/${vehicleId}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Verificar si un vehículo está en favoritos\n  isFavorite: async (vehicleId: string) => {\n    const result = await apiService.get<FavoriteStatusResponse>(`/client/favorites/${vehicleId}/status`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Obtener estados de favoritos para múltiples vehículos\n  getFavoritesStatus: async (vehicleIds: string[]) => {\n    const result = await apiService.post<MultipleFavoriteStatusResponse>('/client/favorites/status', {\n      vehicleIds\n    });\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Toggle favorito (agregar si no está, remover si está)\n  toggleFavorite: async (vehicleId: string) => {\n    try {\n      // Primero verificar el estado actual\n      const statusResult = await favoritesApi.isFavorite(vehicleId);\n      \n      if (statusResult.isFavorite) {\n        // Si está en favoritos, remover\n        return await favoritesApi.removeFromFavorites(vehicleId);\n      } else {\n        // Si no está en favoritos, agregar\n        return await favoritesApi.addToFavorites(vehicleId);\n      }\n    } catch (error: any) {\n      throw new Error(error.message || 'Error al cambiar estado de favorito');\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AAmCO,MAAM,eAAe;IAC1B,+CAA+C;IAC/C,cAAc,OAAO;QACnB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAmB,qBAAqB;YAAE;QAAO;QACpF,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,+BAA+B;IAC/B,gBAAgB,OAAO;QACrB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAsB,CAAC,kBAAkB,EAAE,WAAW;QAC1F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,gCAAgC;IAChC,qBAAqB,OAAO;QAC1B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,MAAM,CAAyC,CAAC,kBAAkB,EAAE,WAAW;QAC/G,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6CAA6C;IAC7C,YAAY,OAAO;QACjB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAyB,CAAC,kBAAkB,EAAE,UAAU,OAAO,CAAC;QACnG,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,wDAAwD;IACxD,oBAAoB,OAAO;QACzB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAiC,4BAA4B;YAC/F;QACF;QACA,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,wDAAwD;IACxD,gBAAgB,OAAO;QACrB,IAAI;YACF,qCAAqC;YACrC,MAAM,eAAe,MAAM,aAAa,UAAU,CAAC;YAEnD,IAAI,aAAa,UAAU,EAAE;gBAC3B,gCAAgC;gBAChC,OAAO,MAAM,aAAa,mBAAmB,CAAC;YAChD,OAAO;gBACL,mCAAmC;gBACnC,OAAO,MAAM,aAAa,cAAc,CAAC;YAC3C;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;IACF;AACF", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/vehicles/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/vehicles/favorite-button.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useContext } from \"react\"\nimport { Heart } from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\nimport { favoritesApi } from \"@/lib/api/favorites.api\"\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\"\nimport { UserContext } from \"@/context/user-context\"\nimport toast from \"react-hot-toast\"\nimport { cn } from \"@/lib/utils\"\n\n// Hook personalizado que no falla si no hay UserProvider\nfunction useSafeUser() {\n  const context = useContext(UserContext)\n  return context || { user: null, setUser: () => { }, session: null, setSession: () => { } }\n}\n\ninterface FavoriteButtonProps {\n  vehicleId: string\n  className?: string\n  size?: \"sm\" | \"default\" | \"lg\" | \"icon\"\n  variant?: \"default\" | \"secondary\" | \"ghost\" | \"outline\"\n  showToast?: boolean\n}\n\nexport default function FavoriteButton({\n  vehicleId,\n  className,\n  size = \"icon\",\n  variant = \"secondary\",\n  showToast = true\n}: FavoriteButtonProps) {\n  const { user } = useSafeUser()\n  const queryClient = useQueryClient()\n  const [isOptimistic, setIsOptimistic] = useState(false)\n\n  // Solo mostrar el botón si el usuario está autenticado y es de tipo client\n  const shouldShow = Boolean(user && user.userType === 'client')\n\n  // Query para verificar si el vehículo está en favoritos\n  const { data: favoriteStatus, isLoading } = useQuery({\n    queryKey: ['favorite-status', vehicleId],\n    queryFn: () => favoritesApi.isFavorite(vehicleId),\n    enabled: shouldShow,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n\n  // Mutation para toggle favorito\n  const toggleMutation = useMutation({\n    mutationFn: () => favoritesApi.toggleFavorite(vehicleId),\n    onMutate: async () => {\n      // Optimistic update\n      setIsOptimistic(true)\n      await queryClient.cancelQueries({ queryKey: ['favorite-status', vehicleId] })\n\n      const previousStatus = queryClient.getQueryData(['favorite-status', vehicleId])\n\n      // Actualizar optimísticamente\n      queryClient.setQueryData(['favorite-status', vehicleId], (old: any) => ({\n        ...old,\n        isFavorite: !old?.isFavorite\n      }))\n\n      return { previousStatus }\n    },\n    onError: (err, variables, context) => {\n      // Revertir en caso de error\n      if (context?.previousStatus) {\n        queryClient.setQueryData(['favorite-status', vehicleId], context.previousStatus)\n      }\n      if (showToast) {\n        toast.error('Error al actualizar favorito')\n      }\n      console.error('Error toggling favorite:', err)\n    },\n    onSuccess: (data) => {\n      // Invalidar queries relacionadas\n      queryClient.invalidateQueries({ queryKey: ['favorite-status', vehicleId] })\n      queryClient.invalidateQueries({ queryKey: ['favorites'] })\n\n      if (showToast && data?.success) {\n        toast.success(data.message || 'Favorito actualizado')\n      }\n    },\n    onSettled: () => {\n      setIsOptimistic(false)\n    }\n  })\n\n  const handleToggle = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n\n    if (!shouldShow) {\n      if (showToast) {\n        toast.error('Debes iniciar sesión como cliente para usar favoritos')\n      }\n      return\n    }\n\n    toggleMutation.mutate()\n  }\n\n  // No mostrar el botón si el usuario no está autenticado o no es client\n  if (!shouldShow) {\n    return null\n  }\n\n  const isFavorite = favoriteStatus?.isFavorite || false\n  const isProcessing = isLoading || toggleMutation.isPending || isOptimistic\n\n  return (\n    <Button\n      size={size}\n      variant={variant}\n      className={cn(\n        \"relative transition-all duration-200\",\n        isFavorite && \"bg-red-50 hover:bg-red-200 border-red-300\",\n        className\n      )}\n      onClick={handleToggle}\n      disabled={isProcessing}\n    >\n      <Heart\n        className={cn(\n          \"transition-all duration-200\",\n          size === \"sm\" ? \"h-3 w-3\" : size === \"lg\" ? \"h-6 w-6\" : \"h-4 w-4\",\n          isFavorite ? \"fill-red-500 text-red-500\" : \"text-gray-600\",\n          isProcessing && \"animate-pulse\"\n        )}\n      />\n      {isProcessing && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n    </Button>\n  )\n}\n\n// Hook personalizado para usar en otros componentes\nexport function useFavoriteStatus(vehicleId: string) {\n  const { user } = useSafeUser()\n  const shouldFetch = Boolean(user && user.userType === 'client')\n\n  return useQuery({\n    queryKey: ['favorite-status', vehicleId],\n    queryFn: () => favoritesApi.isFavorite(vehicleId),\n    enabled: shouldFetch,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n}\n\n// Hook para obtener múltiples estados de favoritos\nexport function useFavoritesStatus(vehicleIds: string[]) {\n  const { user } = useSafeUser()\n  const shouldFetch = Boolean(user && user.userType === 'client' && vehicleIds.length > 0)\n\n  return useQuery({\n    queryKey: ['favorites-status', vehicleIds],\n    queryFn: () => favoritesApi.getFavoritesStatus(vehicleIds),\n    enabled: shouldFetch,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n}\n"], "names": ["c", "_c", "useState", "useContext", "Heart", "<PERSON><PERSON>", "favoritesApi", "useQuery", "useMutation", "useQueryClient", "UserContext", "toast", "cn", "useSafeUser", "$", "$i", "Symbol", "for", "context", "t0", "user", "setUser", "_temp", "session", "setSession", "_temp2", "FavoriteButton", "vehicleId", "className", "size", "t1", "variant", "t2", "showToast", "t3", "undefined", "queryClient", "isOptimistic", "setIsOptimistic", "shouldShow", "Boolean", "userType", "t4", "t5", "isFavorite", "t6", "query<PERSON><PERSON>", "queryFn", "enabled", "staleTime", "data", "favoriteStatus", "isLoading", "t7", "toggleFavorite", "t8", "cancelQueries", "previousStatus", "getQueryData", "setQueryData", "_temp3", "t10", "t9", "err", "variables", "error", "console", "invalidateQueries", "success", "message", "t11", "t12", "mutationFn", "onMutate", "onError", "onSuccess", "onSettled", "toggleMutation", "t13", "e", "preventDefault", "stopPropagation", "mutate", "handleToggle", "isProcessing", "isPending", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "old", "useFavoriteStatus", "shouldFetch", "useFavoritesStatus", "vehicleIds", "length", "getFavoritesStatus"], "mappings": ";;;;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC5C,SAASC,KAAK,QAAQ,cAAc;AACpC,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,uBAAuB;;;AAC7E,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,EAAE,QAAQ,aAAa;;;AAThC,YAAY;;;;;;;;;;AAWZ,yDAAA;AACA;;IAAA,MAAAE,CAAA,mLAAAb,IAAA,AAAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACE,MAAAI,OAAA,GAAgBf,+KAAAA,AAAA,wIAAAO,cAAsB,CAAC;IAAA,IAAAS,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAI,OAAA,EAAA;QAChCC,EAAA,GAAAD,OAAO,IAAA;YAAAE,IAAA,EAAA;YAAAC,OAAA,EAAAC,KAAA;YAAAC,OAAA,EAAA;YAAAC,UAAA,EAAAC;QAAA,CAA4E;QAAAX,CAAA,CAAA,EAAA,GAAAI,OAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,OAAnFK,EAAmF;AAAA;GAF5FN,YAAA;AAAA,SAAAY,OAAA,GAAA;AAAA,SAAAH,MAAA,GAAA;AAae,wBAAAH,EAAA;;IAAA,MAAAL,CAAA,mLAAAb,IAAAA,AAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAwB,MAAA,EAAAa,SAAA,EAAAC,SAAA,EAAAC,IAAA,EAAAC,EAAA,EAAAC,OAAA,EAAAC,EAAA,EAAAC,SAAA,EAAAC,EAAAA,EAAA,GAAAf,EAMjB;IAHpB,MAAAU,IAAA,GAAAC,EAAa,KAAAK,SAAA,GAAN,MAAM,GAAbL,EAAa;IACb,MAAAC,OAAA,GAAAC,EAAqB,KAAAG,SAAA,GAAX,WAAW,GAArBH,EAAqB;IACrB,MAAAC,SAAA,GAAAC,EAAgB,KAAAC,SAAA,GAAA,OAAhBD,EAAgB;IAEhB,MAAA,EAAAd,IAAAA,EAAA;IACA,MAAAgB,WAAA,kNAAoB3B;IACpB,MAAA,CAAA4B,YAAA,EAAAC,eAAA,CAAA,qKAAwCpC,WAAAA,AAAA,EAAA,KAAc,CAAC;IAGvD,MAAAqC,UAAA,GAAmBC,OAAA,CAAQpB,IAAI,IAAIA,IAAI,CAAAqB,QAAA,KAAc,QAAQ,CAAC;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAA7B,CAAA,CAAA,EAAA,KAAAa,SAAA,EAAA;QAIlDe,EAAA,GAAA;YAAC,iBAAiB;YAAEf,SAAS;SAAA;QAC9BgB,EAAA,GAAAA,CAAA,2IAAMrC,gBAAA,CAAAsC,UAAA,CAAwBjB,SAAS,CAAC;QAAAb,CAAA,CAAA,EAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,EAAA,GAAA4B,EAAA;QAAA5B,CAAA,CAAA,EAAA,GAAA6B,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAA5B,CAAA,CAAA,EAAA;QAAA6B,EAAA,GAAA7B,CAAA,CAAA,EAAA;IAAA;IAAA,IAAA+B,EAAA;IAAA,IAAA/B,CAAA,CAAA,EAAA,KAAAyB,UAAA,IAAAzB,CAAA,CAAA,EAAA,KAAA4B,EAAA,IAAA5B,CAAA,CAAA,EAAA,KAAA6B,EAAA,EAAA;QAFEE,EAAA,GAAA;YAAAC,QAAA,EACzCJ,EAA8B;YAAAK,OAAA,EAC/BJ,EAAwC;YAAAK,OAAA,EACxCT,UAAU;YAAAU,SAAA,EAAA;QAAA;QAEpBnC,CAAA,CAAA,EAAA,GAAAyB,UAAA;QAAAzB,CAAA,CAAA,EAAA,GAAA4B,EAAA;QAAA5B,CAAA,CAAA,EAAA,GAAA6B,EAAA;QAAA7B,CAAA,CAAA,EAAA,GAAA+B,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA/B,CAAA,CAAA,EAAA;IAAA;IALD,MAAA,EAAAoC,IAAA,EAAAC,cAAA,EAAAC,SAAAA,EAAA,iMAA4C7C,EAASsC,EAKpD,CAAC;IAAA,IAAAQ,EAAA;IAAA,IAAAvC,CAAA,CAAA,EAAA,KAAAa,SAAA,EAAA;QAIY0B,EAAA,GAAAA,CAAA,4IAAM/C,eAAA,CAAAgD,cAAA,CAA4B3B,SAAS,CAAC;QAAAb,CAAA,CAAA,EAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,EAAA,GAAAuC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAvC,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAyC,EAAA;IAAA,IAAAzC,CAAA,CAAA,GAAA,KAAAsB,WAAA,IAAAtB,CAAA,CAAA,GAAA,KAAAa,SAAA,EAAA;QAC9C4B,EAAA,GAAA,MAAAA,CAAA;YAERjB,eAAe,CAAA,IAAK,CAAC;YAAA,MACfF,WAAW,CAAAoB,aAAA,CAAA;gBAAAV,QAAA,EAAA;oBAA4B,iBAAiB;oBAAEnB,SAAS;iBAAA;YAAA,CAAG,CAAC;YAE7E,MAAA8B,cAAA,GAAuBrB,WAAW,CAAAsB,YAAA,CAAA;gBAAe,iBAAiB;gBAAE/B,SAAS;aAAC,CAAC;YAG/ES,WAAW,CAAAuB,YAAA,CAAA;gBAAe,iBAAiB;gBAAEhC,SAAS;aAAA,EAAAiC,MAGpD,CAAC;YAAA,OAAA;gBAAAH;YAAA;QAAA;QAGJ3C,CAAA,CAAA,GAAA,GAAAsB,WAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,GAAA,GAAAyC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAzC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+C,GAAA;IAAA,IAAAC,EAAA;IAAA,IAAAhD,CAAA,CAAA,GAAA,KAAAsB,WAAA,IAAAtB,CAAA,CAAA,GAAA,KAAAmB,SAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAa,SAAA,EAAA;QACQmC,EAAA,GAAAA,CAAAC,GAAA,EAAAC,SAAA,EAAA9C,OAAA;YAAA,IAEHA,OAAO,EAAAuC,cAAA,EAAA;gBACTrB,WAAW,CAAAuB,YAAA,CAAA;oBAAe,iBAAiB;oBAAEhC,SAAS;iBAAA,EAAGT,OAAO,CAAAuC,cAAe,CAAC;YAAA;YAAA,IAE9ExB,SAAS,EAAA;2KACXtB,UAAA,CAAAsD,KAAA,CAAY,8BAA8B,CAAC;YAAA;YAE7CC,OAAA,CAAAD,KAAA,CAAc,0BAA0B,EAAEF,GAAG,CAAC;QAAA;QAErCF,GAAA,GAAAX,IAAA;YAETd,WAAW,CAAA+B,iBAAA,CAAA;gBAAArB,QAAA,EAAA;oBAAgC,iBAAiB;oBAAEnB,SAAS;iBAAA;YAAA,CAAG,CAAC;YAC3ES,WAAW,CAAA+B,iBAAA,CAAA;gBAAArB,QAAA,EAAA;oBAAgC,WAAW;iBAAA;YAAA,CAAG,CAAC;YAAA,IAEtDb,SAAS,IAAIiB,IAAI,EAAAkB,OAAS,EAAA;2KAC5BzD,UAAA,CAAAyD,OAAA,CAAclB,IAAI,CAAAmB,OAAA,IAAY,sBAAsB,CAAC;YAAA;QAAA;QAExDvD,CAAA,CAAA,GAAA,GAAAsB,WAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAmB,SAAA;QAAAnB,CAAA,CAAA,GAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,GAAA,GAAA+C,GAAA;QAAA/C,CAAA,CAAA,GAAA,GAAAgD,EAAA;IAAA,OAAA;QAAAD,GAAA,GAAA/C,CAAA,CAAA,GAAA;QAAAgD,EAAA,GAAAhD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwD,GAAA;IAAA,IAAAxD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACUqD,GAAA,GAAAA,CAAA;YACThC,eAAe,CAAA,KAAM,CAAC;QAAA;QACvBxB,CAAA,CAAA,GAAA,GAAAwD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAxD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyD,GAAA;IAAA,IAAAzD,CAAA,CAAA,GAAA,KAAA+C,GAAA,IAAA/C,CAAA,CAAA,GAAA,KAAAuC,EAAA,IAAAvC,CAAA,CAAA,GAAA,KAAAyC,EAAA,IAAAzC,CAAA,CAAA,GAAA,KAAAgD,EAAA,EAAA;QAtCgCS,GAAA,GAAA;YAAAC,UAAA,EACrBnB,EAA4C;YAAAoB,QAAA,EAC9ClB,EAcT;YAAAmB,OAAA,EACQZ,EASR;YAAAa,SAAA,EACUd,GAQV;YAAAe,SAAA,EACUN;QAEV;QACFxD,CAAA,CAAA,GAAA,GAAA+C,GAAA;QAAA/C,CAAA,CAAA,GAAA,GAAAuC,EAAA;QAAAvC,CAAA,CAAA,GAAA,GAAAyC,EAAA;QAAAzC,CAAA,CAAA,GAAA,GAAAgD,EAAA;QAAAhD,CAAA,CAAA,GAAA,GAAAyD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzD,CAAA,CAAA,GAAA;IAAA;IAvCD,MAAA+D,cAAA,uMAAuBrE,EAAY+D,GAuClC,CAAC;IAAA,IAAAO,GAAA;IAAA,IAAAhE,CAAA,CAAA,GAAA,KAAAyB,UAAA,IAAAzB,CAAA,CAAA,GAAA,KAAAmB,SAAA,IAAAnB,CAAA,CAAA,GAAA,KAAA+D,cAAA,EAAA;QAEmBC,GAAA,IAAAC,CAAA;YACnBA,CAAC,CAAAC,cAAA,CAAgB,CAAC;YAClBD,CAAC,CAAAE,eAAA,CAAiB,CAAC;YAAA,IAAA,CAEd1C,UAAU,EAAA;gBAAA,IACTN,SAAS,EAAA;+KACXtB,UAAA,CAAAsD,KAAA,CAAY,0DAAuD,CAAC;gBAAA;gBAAA;YAAA;YAKxEY,cAAc,CAAAK,MAAA,CAAQ,CAAC;QAAA;QACxBpE,CAAA,CAAA,GAAA,GAAAyB,UAAA;QAAAzB,CAAA,CAAA,GAAA,GAAAmB,SAAA;QAAAnB,CAAA,CAAA,GAAA,GAAA+D,cAAA;QAAA/D,CAAA,CAAA,GAAA,GAAAgE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhE,CAAA,CAAA,GAAA;IAAA;IAZD,MAAAqE,YAAA,GAAqBL,GAYpB;IAAA,IAAA,CAGIvC,UAAU,EAAA;QAAA,OAAA;IAAA;IAIf,MAAAK,UAAA,GAAmBO,cAAc,EAAAP,UAAA,IAAA,KAAqB;IACtD,MAAAwC,YAAA,GAAqBhC,SAAS,IAAIyB,cAAc,CAAAQ,SAAU,IAAIhD,YAAY;IAQpE,MAAAiD,GAAA,GAAA1C,UAAU,IAAI,2CAA2C;IAAA,IAAA2C,GAAA;IAAA,IAAAzE,CAAA,CAAA,GAAA,KAAAc,SAAA,IAAAd,CAAA,CAAA,GAAA,KAAAwE,GAAA,EAAA;QAFhDC,GAAA,8HAAA3E,KAAAA,AAAA,EACT,sCAAsC,EACtC0E,GAAyD,EACzD1D,SACF,CAAC;QAAAd,CAAA,CAAA,GAAA,GAAAc,SAAA;QAAAd,CAAA,CAAA,GAAA,GAAAwE,GAAA;QAAAxE,CAAA,CAAA,GAAA,GAAAyE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzE,CAAA,CAAA,GAAA;IAAA;IAOG,MAAA0E,GAAA,GAAA3D,IAAI,KAAK,IAAI,GAAG,SAAS,GAAGA,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG,SAAS;IACjE,MAAA4D,GAAA,GAAA7C,UAAU,GAAG,2BAA2B,GAAG,eAAe;IAC1D,MAAA8C,GAAA,GAAAN,YAAY,IAAI,eAAe;IAAA,IAAAO,GAAA;IAAA,IAAA7E,CAAA,CAAA,GAAA,KAAA0E,GAAA,IAAA1E,CAAA,CAAA,GAAA,KAAA2E,GAAA,IAAA3E,CAAA,CAAA,GAAA,KAAA4E,GAAA,EAAA;QAJtBC,GAAA,6HAAA/E,MAAAA,AAAA,EACT,6BAA6B,EAC7B4E,GAAiE,EACjEC,GAA0D,EAC1DC,GACF,CAAC;QAAA5E,CAAA,CAAA,GAAA,GAAA0E,GAAA;QAAA1E,CAAA,CAAA,GAAA,GAAA2E,GAAA;QAAA3E,CAAA,CAAA,GAAA,GAAA4E,GAAA;QAAA5E,CAAA,CAAA,GAAA,GAAA6E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA7E,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA8E,GAAA;IAAA,IAAA9E,CAAA,CAAA,GAAA,KAAA6E,GAAA,EAAA;QANHC,GAAA,iBAAA,6LAAC,gNAAK;YACO,SAKV,CALU,CAAAD,GAKV,IACD;;;;;;QAAA7E,CAAA,CAAA,GAAA,GAAA6E,GAAA;QAAA7E,CAAA,CAAA,GAAA,GAAA8E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9E,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+E,GAAA;IAAA,IAAA/E,CAAA,CAAA,GAAA,KAAAsE,YAAA,EAAA;QACDS,GAAA,GAAAT,YAAY,kBACX,6LAAA,GAEM;YAFS,SAAmD,EAAnD,mDAAmD;oCAChE,6LAAA,GAAiG;gBAAlF,SAA+E,EAA/E,+EAA+E,GAChG,EAFA,GAEM,CACP;;;;;;;;;;;QAAAtE,CAAA,CAAA,GAAA,GAAAsE,YAAA;QAAAtE,CAAA,CAAA,GAAA,GAAA+E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/E,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgF,GAAA;IAAA,IAAAhF,CAAA,CAAA,GAAA,KAAAqE,YAAA,IAAArE,CAAA,CAAA,GAAA,KAAAsE,YAAA,IAAAtE,CAAA,CAAA,GAAA,KAAAe,IAAA,IAAAf,CAAA,CAAA,GAAA,KAAAyE,GAAA,IAAAzE,CAAA,CAAA,GAAA,KAAA8E,GAAA,IAAA9E,CAAA,CAAA,GAAA,KAAA+E,GAAA,IAAA/E,CAAA,CAAA,GAAA,KAAAiB,OAAA,EAAA;QAvBH+D,GAAA,iBAAA,kUAAC,UAAM;YACCjE,IAAI,CAAJA,CAAAA,IAAI;YACDE,OAAO,CAAPA,CAAAA,OAAO;YACL,SAIV,CAJU,CAAAwD,GAIV;YACQJ,OAAY,CAAZA,CAAAA,YAAY;YACXC,QAAY,CAAZA,CAAAA,YAAY,EAEtB;;gBAAAQ,GAOE,CACD;gBAAAC,GAIA,CACH,EAxBC,MAAM,CAwBE;;;;;;;QAAA/E,CAAA,CAAA,GAAA,GAAAqE,YAAA;QAAArE,CAAA,CAAA,GAAA,GAAAsE,YAAA;QAAAtE,CAAA,CAAA,GAAA,GAAAe,IAAA;QAAAf,CAAA,CAAA,GAAA,GAAAyE,GAAA;QAAAzE,CAAA,CAAA,GAAA,GAAA8E,GAAA;QAAA9E,CAAA,CAAA,GAAA,GAAA+E,GAAA;QAAA/E,CAAA,CAAA,GAAA,GAAAiB,OAAA;QAAAjB,CAAA,CAAA,GAAA,GAAAgF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhF,CAAA,CAAA,GAAA;IAAA;IAAA,OAxBTgF,GAwBS;AAAA;;;QAxGMjF,WAAA,CAAY,CAAC;kMACVJ,iBAAA,CAAe,CAAC;sLAOQF,YAAA;0LAQrBC,cAAA;;;KAvBVkB;AAmHf,oDAAA;AAnHe,SAAAkC,OAAAmC,GAAA;IAAA,OAAA;QAAA,GAkCJA,GAAG;QAAAnD,UAAA,EAAA,CACOmD,GAAG,EAAAnD;IAAA;AAAA;AAiFjB,2BAAAjB,SAAA;;IAAA,MAAAb,CAAA,kLAAAb,KAAAA,AAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,EAAAM,IAAAA,EAAA;IACA,MAAA6E,WAAA,GAAoBzD,OAAA,CAAQpB,IAAI,IAAIA,IAAI,CAAAqB,QAAA,KAAc,QAAQ,CAAC;IAAA,IAAAtB,EAAA;IAAA,IAAAW,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAa,SAAA,EAAA;QAGnDR,EAAA,GAAA;YAAC,iBAAiB;YAAEQ,SAAS;SAAA;QAC9BG,EAAA,GAAAA,CAAA,4IAAMxB,eAAA,CAAAsC,UAAA,CAAwBjB,SAAS,CAAC;QAAAb,CAAA,CAAA,EAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAX,EAAA,GAAAL,CAAA,CAAA,EAAA;QAAAgB,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAkB,EAAA;IAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAmF,WAAA,IAAAnF,CAAA,CAAA,EAAA,KAAAK,EAAA,IAAAL,CAAA,CAAA,EAAA,KAAAgB,EAAA,EAAA;QAFnCE,EAAA,GAAA;YAAAc,QAAA,EACJ3B,EAA8B;YAAA4B,OAAA,EAC/BjB,EAAwC;YAAAkB,OAAA,EACxCiD,WAAW;YAAAhD,SAAA,EAAA;QAAA;QAErBnC,CAAA,CAAA,EAAA,GAAAmF,WAAA;QAAAnF,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAgB,EAAA;QAAAhB,CAAA,CAAA,EAAA,GAAAkB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAlB,CAAA,CAAA,EAAA;IAAA;IAAA,yLALMP,YAAAA,AAAA,EAASyB,EAKf,CAAC;AAAA;IATGgE;;QACYnF,WAAA,CAAY,CAAC;;;;AAYzB,4BAAAsF,UAAA;;IAAA,MAAArF,CAAA,kLAAAb,KAAAA,AAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,EAAAM,IAAAA,EAAA;IACA,MAAA6E,WAAA,GAAoBzD,OAAA,CAAQpB,IAAI,IAAIA,IAAI,CAAAqB,QAAA,KAAc,QAAQ,IAAI0D,UAAU,CAAAC,MAAA,GAAA,CAAW,CAAC;IAAA,IAAAjF,EAAA;IAAA,IAAAW,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAqF,UAAA,EAAA;QAG5EhF,EAAA,GAAA;YAAC,kBAAkB;YAAEgF,UAAU;SAAA;QAChCrE,EAAA,GAAAA,CAAA,4IAAMxB,eAAA,CAAA+F,kBAAA,CAAgCF,UAAU,CAAC;QAAArF,CAAA,CAAA,EAAA,GAAAqF,UAAA;QAAArF,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAX,EAAA,GAAAL,CAAA,CAAA,EAAA;QAAAgB,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAkB,EAAA;IAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAmF,WAAA,IAAAnF,CAAA,CAAA,EAAA,KAAAK,EAAA,IAAAL,CAAA,CAAA,EAAA,KAAAgB,EAAA,EAAA;QAF5CE,EAAA,GAAA;YAAAc,QAAA,EACJ3B,EAAgC;YAAA4B,OAAA,EACjCjB,EAAiD;YAAAkB,OAAA,EACjDiD,WAAW;YAAAhD,SAAA,EAAA;QAAA;QAErBnC,CAAA,CAAA,EAAA,GAAAmF,WAAA;QAAAnF,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAgB,EAAA;QAAAhB,CAAA,CAAA,EAAA,GAAAkB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAlB,CAAA,CAAA,EAAA;IAAA;IAAA,qMALMP,EAASyB,EAKf,CAAC;AAAA;IATGkE;;QACYrF,WAAA,CAAY,CAAC;uLAGvBN,WAAA", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { ButtonProps, buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\r\n  <nav\r\n    role=\"navigation\"\r\n    aria-label=\"pagination\"\r\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n    {...props}\r\n  />\r\n)\r\nPagination.displayName = \"Pagination\"\r\n\r\nconst PaginationContent = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    className={cn(\"flex flex-row items-center gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nPaginationContent.displayName = \"PaginationContent\"\r\n\r\nconst PaginationItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li ref={ref} className={cn(\"\", className)} {...props} />\r\n))\r\nPaginationItem.displayName = \"PaginationItem\"\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<ButtonProps, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nconst PaginationLink = ({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) => (\r\n  <a\r\n    aria-current={isActive ? \"page\" : undefined}\r\n    className={cn(\r\n      buttonVariants({\r\n        variant: isActive ? \"outline\" : \"ghost\",\r\n        size,\r\n      }),\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nPaginationLink.displayName = \"PaginationLink\"\r\n\r\nconst PaginationPrevious = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to previous page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pl-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronLeft className=\"h-4 w-4\" />\r\n    <span>Previous</span>\r\n  </PaginationLink>\r\n)\r\nPaginationPrevious.displayName = \"PaginationPrevious\"\r\n\r\nconst PaginationNext = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to next page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pr-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <span>Next</span>\r\n    <ChevronRight className=\"h-4 w-4\" />\r\n  </PaginationLink>\r\n)\r\nPaginationNext.displayName = \"PaginationNext\"\r\n\r\nconst PaginationEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"h-4 w-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n)\r\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n}\r\n"], "names": ["React", "ChevronLeft", "ChevronRight", "MoreHorizontal", "cn", "buttonVariants", "Pagination", "className", "props", "displayName", "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "t0", "ref", "$", "_c", "$i", "Symbol", "for", "t1", "t2", "PaginationItem", "PaginationLink", "isActive", "size", "undefined", "variant", "PaginationPrevious", "PaginationNext", "PaginationEllipsis"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,YAAY,EAAEC,cAAc,QAAQ,cAAc;;;AAExE,SAASC,EAAE,QAAQ,aAAa;AAChC,SAAsBC,cAAc,QAAQ,wBAAwB;;;;;;;AAEpE,MAAMC,UAAU,GAAGA,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAoC,iBACtE,6LAAC,GAAG;QACF,IAAI,EAAC,YAAY;QACjB,UAAU,IAAC,YAAY;QACvB,SAAS,CAAC,4HAACJ,KAAAA,AAAE,EAAC,oCAAoC,EAAEG,SAAS,CAAC,CAAC,CAC/D;QAAA,GAAIC,KAAK,CAAC,GAEb;;;;;;;AACDF,UAAU,CAACG,WAAW,GAAG,YAAY;AAErC,MAAMC,iBAAiB,mLAAGV,KAAK,CAACW,OAAAA,AAAU,QAGxC,CAAAC,EAAA,EAAAC,GAAA;IAAA,MAAAC,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAP,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAM,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAL,SAAA,EAAA,GAAAC,OAAA,GAAAI,EAAuB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAP,SAAA;QAAAO,CAAA,CAAA,EAAA,GAAAN,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAO,CAAA,CAAA,EAAA;QAAAN,KAAA,GAAAM,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAK,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAP,SAAA,EAAA;QAGXY,EAAA,8HAAAf,KAAAA,AAAA,EAAG,kCAAkC,EAAEG,SAAS,CAAC;QAAAO,CAAA,CAAA,EAAA,GAAAP,SAAA;QAAAO,CAAA,CAAA,EAAA,GAAAK,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAN,KAAA,IAAAM,CAAA,CAAA,EAAA,KAAAD,GAAA,IAAAC,CAAA,CAAA,EAAA,KAAAK,EAAA,EAAA;QAF9DC,EAAA,iBAAA,6LAAA,EAIE;YAHKP,GAAG,CAAHA,CAAAA,GAAG;YACG,SAAiD,CAAjD,CAAAM,EAAiD;YAAA,GACxDX,KAAK,IACT;;;;;;QAAAM,CAAA,CAAA,EAAA,GAAAN,KAAA;QAAAM,CAAA,CAAA,EAAA,GAAAD,GAAA;QAAAC,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,OAJFM,EAIE;AAAA,CACH,CAAC;;AACFV,iBAAiB,CAACD,WAAW,GAAG,mBAAmB;AAEnD,MAAMY,cAAc,qBAAGrB,KAAK,CAACW,qKAAAA,AAAU,QAGrC,CAAAC,EAAA,EAAAC,GAAA;IAAA,MAAAC,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAP,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAM,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAL,SAAA,EAAA,GAAAC,OAAA,GAAAI,EAAuB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAP,SAAA;QAAAO,CAAA,CAAA,EAAA,GAAAN,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAO,CAAA,CAAA,EAAA;QAAAN,KAAA,GAAAM,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAK,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAP,SAAA,EAAA;QACCY,EAAA,GAAAf,gIAAAA,AAAA,EAAG,EAAE,EAAEG,SAAS,CAAC;QAAAO,CAAA,CAAA,EAAA,GAAAP,SAAA;QAAAO,CAAA,CAAA,EAAA,GAAAK,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAN,KAAA,IAAAM,CAAA,CAAA,EAAA,KAAAD,GAAA,IAAAC,CAAA,CAAA,EAAA,KAAAK,EAAA,EAAA;QAA1CC,EAAA,iBAAA,6LAAA,EAAyD;YAAhDP,GAAG,CAAHA,CAAAA,GAAG;YAAa,SAAiB,CAAjB,CAAAM,EAAiB;YAAA,GAAMX,KAAK,IAAI;;;;;;QAAAM,CAAA,CAAA,EAAA,GAAAN,KAAA;QAAAM,CAAA,CAAA,EAAA,GAAAD,GAAA;QAAAC,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,OAAzDM,EAAyD;AAAA,CAC1D,CAAC;;AACFC,cAAc,CAACZ,WAAW,GAAG,gBAAgB;AAO7C,uBAAuBa,CAAC,EACtBf,SAAS,EACTgB,QAAQ,EACRC,IAAI,GAAG,MAAM,EACb,GAAGhB,OACiB,iBACpB,6LAAC,CAAC;QACA,YAAY,CAAC,GAACe,QAAQ,GAAG,MAAM,GAAGE,SAAS,CAAC;QAC5C,SAAS,CAAC,4HAACrB,KAAAA,AAAE,MACXC,uJAAAA,AAAc,EAAC;YACbqB,OAAO,EAAEH,QAAQ,GAAG,SAAS,GAAG,OAAO;YACvCC;QACF,CAAC,CAAC,EACFjB,SACF,CAAC,CAAC,CACF;QAAA,GAAIC,KAAK,CAAC,GAEb;;;;;;MAjBKc,cAAc;AAkBpBA,cAAc,CAACb,WAAW,GAAG,gBAAgB;AAE7C,2BAA2BkB,CAAC,EAC1BpB,SAAS,EACT,GAAGC,OACyC,iBAC5C,6LAAC,cAAc;QACb,UAAU,IAAC,qBAAqB;QAChC,IAAI,EAAC,SAAS;QACd,SAAS,CAAC,4HAACJ,KAAE,AAAFA,EAAG,cAAc,EAAEG,SAAS,CAAC,CAAC,CACzC;QAAA,GAAIC,KAAK,CAAC,CACX;;0BACC,qZAAC,cAAW;gBAAC,SAAS,EAAC,SAAS,GAAG;;;;;;0BACnC,6LAAC,IAAI;0BAAC,QAAQ,EAAE,IAAI,CAAC;;;;;;;;;;;;MAXnBmB,kBAAkB;AAcxBA,kBAAkB,CAAClB,WAAW,GAAG,oBAAoB;AAErD,uBAAuBmB,CAAC,EACtBrB,SAAS,EACT,GAAGC,OACyC,iBAC5C,6LAAC,cAAc;QACb,UAAU,IAAC,iBAAiB;QAC5B,IAAI,EAAC,SAAS;QACd,SAAS,CAAC,4HAACJ,KAAAA,AAAE,EAAC,cAAc,EAAEG,SAAS,CAAC,CAAC,CACzC;QAAA,GAAIC,KAAK,CAAC,CACX;;0BACC,6LAAC,IAAI;0BAAC,IAAI,EAAE,IAAI,CAAC;;;;;;0BACjB,uZAAC,eAAY;gBAAC,SAAS,EAAC,SAAS,GAAG;;;;;;;;;;;;MAXlCoB,cAAc;AAcpBA,cAAc,CAACnB,WAAW,GAAG,gBAAgB;AAE7C,2BAA2BoB,CAAC,EAC1BtB,SAAS,EACT,GAAGC,OAC0B,iBAC7B,6LAAC,IAAI;QACH,aAAW;QACX,SAAS,CAAC,4HAACJ,KAAAA,AAAE,EAAC,0CAA0C,EAAEG,SAAS,CAAC,CAAC,CACrE;QAAA,GAAIC,KAAK,CAAC,CACX;;0BACC,iZAAC,iBAAc;gBAAC,SAAS,EAAC,SAAS,GAAG;;;;;;0BACtC,6LAAC,IAAI;gBAAC,SAAS,EAAC,SAAS;0BAAC,UAAU,EAAE,IAAI,CAAC;;;;;;;;;;;;MAVzCqB,kBAAkB;AAaxBA,kBAAkB,CAACpB,WAAW,GAAG,oBAAoB", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/pagination-control.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { usePathname, useSearchParams } from \"next/navigation\"\nimport {\n  Pagination,\n  PaginationContent,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n  PaginationEllipsis\n} from \"@/components/ui/pagination\"\n\ninterface PaginationControlProps {\n  currentPage: number\n  totalPages: number\n  baseUrl?: string\n  className?: string\n  onPageChange?: (page: number) => void\n}\n\nexport function PaginationControl({\n  currentPage,\n  totalPages,\n  baseUrl,\n  className = \"\",\n  onPageChange\n}: PaginationControlProps) {\n  const pathname = usePathname()\n  const searchParams = useSearchParams()\n  const [windowWidth, setWindowWidth] = useState(\n    typeof window !== 'undefined' ? window.innerWidth : 0\n  )\n\n  // Actualizar el ancho de la ventana cuando cambia el tamaño\n  useEffect(() => {\n    const handleResize = () => setWindowWidth(window.innerWidth)\n    if (typeof window !== 'undefined') {\n      window.addEventListener('resize', handleResize)\n      return () => window.removeEventListener('resize', handleResize)\n    }\n  }, [])\n\n  // Función para crear una nueva URL con parámetros de búsqueda actualizados\n  const createPageUrl = (pageNumber: number) => {\n    if (onPageChange) return \"#\" // Si se usa onPageChange, el href es solo un placeholder\n\n    const params = new URLSearchParams(searchParams.toString())\n    params.set('page', pageNumber.toString())\n\n    if (baseUrl) {\n      return `${baseUrl}?${params.toString()}`\n    }\n\n    return `${pathname}?${params.toString()}`\n  }\n\n  // Calcular cuántos números de página mostrar basado en el ancho de la ventana\n  const getVisiblePageNumbers = () => {\n    if (totalPages <= 1) return [1]\n\n    let maxVisible = 1 // Mínimo siempre mostrar al menos 1\n\n    if (windowWidth > 640) maxVisible = 3 // sm\n    if (windowWidth > 768) maxVisible = 5 // md\n    if (windowWidth > 1024) maxVisible = 7 // lg\n\n    // Asegurarse de no mostrar más páginas de las que existen\n    maxVisible = Math.min(maxVisible, totalPages)\n\n    // Calcular el rango de páginas a mostrar\n    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2))\n    const endPage = Math.min(totalPages, startPage + maxVisible - 1)\n\n    // Ajustar si estamos cerca del final\n    if (endPage - startPage + 1 < maxVisible) {\n      startPage = Math.max(1, endPage - maxVisible + 1)\n    }\n\n    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i)\n  }\n\n  const handleClick = (pageNumber: number, e: React.MouseEvent) => {\n    if (onPageChange) {\n      e.preventDefault()\n      onPageChange(pageNumber)\n    }\n  }\n\n  const visiblePages = getVisiblePageNumbers()\n  const showLeftEllipsis = totalPages > 0 && visiblePages[0] > 1\n  const showRightEllipsis = totalPages > 0 && visiblePages[visiblePages.length - 1] < totalPages\n\n  if (totalPages <= 1) {\n    return null\n  }\n\n  return (\n    <Pagination className={className}>\n      <PaginationContent>\n        {currentPage > 1 && (\n          <PaginationItem>\n            <PaginationPrevious\n              href={createPageUrl(currentPage - 1)}\n              onClick={(e) => handleClick(currentPage - 1, e)}\n            />\n          </PaginationItem>\n        )}\n\n        {showLeftEllipsis && (\n          <>\n            <PaginationItem>\n              <PaginationLink\n                href={createPageUrl(1)}\n                onClick={(e) => handleClick(1, e)}\n              >\n                1\n              </PaginationLink>\n            </PaginationItem>\n            <PaginationItem>\n              <PaginationEllipsis />\n            </PaginationItem>\n          </>\n        )}\n\n        {visiblePages.map((pageNumber) => (\n          <PaginationItem key={pageNumber}>\n            <PaginationLink\n              href={createPageUrl(pageNumber)}\n              isActive={pageNumber === currentPage}\n              onClick={(e) => handleClick(pageNumber, e)}\n            >\n              {pageNumber}\n            </PaginationLink>\n          </PaginationItem>\n        ))}\n\n        {showRightEllipsis && (\n          <>\n            <PaginationItem>\n              <PaginationEllipsis />\n            </PaginationItem>\n            <PaginationItem>\n              <PaginationLink\n                href={createPageUrl(totalPages)}\n                onClick={(e) => handleClick(totalPages, e)}\n              >\n                {totalPages}\n              </PaginationLink>\n            </PaginationItem>\n          </>\n        )}\n\n        {currentPage < totalPages && (\n          <PaginationItem>\n            <PaginationNext\n              href={createPageUrl(currentPage + 1)}\n              onClick={(e) => handleClick(currentPage + 1, e)}\n            />\n          </PaginationItem>\n        )}\n      </PaginationContent>\n    </Pagination>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAsBO,SAAS,kBAAkB,EAChC,WAAW,EACX,UAAU,EACV,OAAO,EACP,YAAY,EAAE,EACd,YAAY,EACW;;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3C,uCAAgC,OAAO,UAAU;IAGnD,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;4DAAe,IAAM,eAAe,OAAO,UAAU;;YAC3D,wCAAmC;gBACjC,OAAO,gBAAgB,CAAC,UAAU;gBAClC;mDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;YACpD;QACF;sCAAG,EAAE;IAEL,2EAA2E;IAC3E,MAAM,gBAAgB,CAAC;QACrB,IAAI,cAAc,OAAO,IAAI,yDAAyD;;QAEtF,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;QACxD,OAAO,GAAG,CAAC,QAAQ,WAAW,QAAQ;QAEtC,IAAI,SAAS;YACX,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;QAC1C;QAEA,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC3C;IAEA,8EAA8E;IAC9E,MAAM,wBAAwB;QAC5B,IAAI,cAAc,GAAG,OAAO;YAAC;SAAE;QAE/B,IAAI,aAAa,EAAE,oCAAoC;;QAEvD,IAAI,cAAc,KAAK,aAAa,EAAE,KAAK;;QAC3C,IAAI,cAAc,KAAK,aAAa,EAAE,KAAK;;QAC3C,IAAI,cAAc,MAAM,aAAa,EAAE,KAAK;;QAE5C,0DAA0D;QAC1D,aAAa,KAAK,GAAG,CAAC,YAAY;QAElC,yCAAyC;QACzC,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,aAAa;QAClE,MAAM,UAAU,KAAK,GAAG,CAAC,YAAY,YAAY,aAAa;QAE9D,qCAAqC;QACrC,IAAI,UAAU,YAAY,IAAI,YAAY;YACxC,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,aAAa;QACjD;QAEA,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU,YAAY;QAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAC/E;IAEA,MAAM,cAAc,CAAC,YAAoB;QACvC,IAAI,cAAc;YAChB,EAAE,cAAc;YAChB,aAAa;QACf;IACF;IAEA,MAAM,eAAe;IACrB,MAAM,mBAAmB,aAAa,KAAK,YAAY,CAAC,EAAE,GAAG;IAC7D,MAAM,oBAAoB,aAAa,KAAK,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,GAAG;IAEpF,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IAEA,qBACE,6LAAC,yIAAA,CAAA,aAAU;QAAC,WAAW;kBACrB,cAAA,6LAAC,yIAAA,CAAA,oBAAiB;;gBACf,cAAc,mBACb,6LAAC,yIAAA,CAAA,iBAAc;8BACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;wBACjB,MAAM,cAAc,cAAc;wBAClC,SAAS,CAAC,IAAM,YAAY,cAAc,GAAG;;;;;;;;;;;gBAKlD,kCACC;;sCACE,6LAAC,yIAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;gCACb,MAAM,cAAc;gCACpB,SAAS,CAAC,IAAM,YAAY,GAAG;0CAChC;;;;;;;;;;;sCAIH,6LAAC,yIAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;;;;;;;;;;;;gBAKxB,aAAa,GAAG,CAAC,CAAC,2BACjB,6LAAC,yIAAA,CAAA,iBAAc;kCACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;4BACb,MAAM,cAAc;4BACpB,UAAU,eAAe;4BACzB,SAAS,CAAC,IAAM,YAAY,YAAY;sCAEvC;;;;;;uBANgB;;;;;gBAWtB,mCACC;;sCACE,6LAAC,yIAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;;;;;;;;;;sCAErB,6LAAC,yIAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;gCACb,MAAM,cAAc;gCACpB,SAAS,CAAC,IAAM,YAAY,YAAY;0CAEvC;;;;;;;;;;;;;gBAMR,cAAc,4BACb,6LAAC,yIAAA,CAAA,iBAAc;8BACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;wBACb,MAAM,cAAc,cAAc;wBAClC,SAAS,CAAC,IAAM,YAAY,cAAc,GAAG;;;;;;;;;;;;;;;;;;;;;;AAO3D;GA/IgB;;QAOG,qIAAA,CAAA,cAAW;QACP,qIAAA,CAAA,kBAAe;;;KARtB", "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28dashboard%29/dashboard/client/favorites/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28dashboard%29/dashboard/client/favorites/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Heart, Search, Star, MapPin, Grid, List, Loader2 } from \"lucide-react\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { useQuery } from \"@tanstack/react-query\"\nimport { favoritesApi } from \"@/lib/api/favorites.api\"\nimport { useUser } from \"@/context/user-context\"\nimport FavoriteButton from \"@/components/vehicles/favorite-button\"\nimport { PaginationControl } from \"@/components/ui/pagination-control\"\nimport { useRouter, useSearchParams } from \"next/navigation\"\n\n// Mapeo de tipos de carrocería en español\nconst bodyTypeLabels: Record<string, string> = {\n  sedan: \"Sedan\",\n  suv: \"SUV\",\n  hatchback: \"Hatchback\",\n  pickup: \"Pickup\",\n  coupe: \"Coupe\",\n  convertible: \"Convertible\",\n  wagon: \"Wagon\",\n  van: \"Van\",\n  minivan: \"Minivan\",\n  targa: \"Targa\",\n  doublecab: \"Doble Cabina\",\n  truck: \"Camioneta\"\n};\n\nexport default function FavoritesPage() {\n  const { user } = useUser()\n  const router = useRouter()\n  const searchParams = useSearchParams()\n\n  // Estados para filtros y vista\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [selectedCategory, setSelectedCategory] = useState(\"all\")\n  const [selectedAvailability, setSelectedAvailability] = useState(\"all\")\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\")\n\n  // Paginación\n  const currentPage = parseInt(searchParams.get('page') || '1')\n  const limit = 12\n\n  // Verificar que el usuario sea de tipo client\n  if (!user || user.userType !== 'client') {\n    return (\n      <div className=\"flex flex-col items-center justify-center min-h-[400px] text-center\">\n        <Heart className=\"h-16 w-16 text-gray-300 mb-4\" />\n        <h2 className=\"text-2xl font-semibold mb-2\">Acceso restringido</h2>\n        <p className=\"text-gray-600 mb-4\">\n          Solo los usuarios de tipo cliente pueden acceder a la página de favoritos.\n        </p>\n        <Button onClick={() => router.push('/dashboard')}>\n          Volver al dashboard\n        </Button>\n      </div>\n    )\n  }\n\n  // Query para obtener favoritos\n  const { data: favoritesData, isLoading, error } = useQuery({\n    queryKey: ['favorites', currentPage, limit],\n    queryFn: () => favoritesApi.getFavorites({ page: currentPage, limit }),\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n\n  const vehicles = favoritesData?.data || []\n  const pagination = favoritesData?.pagination\n\n  // Filtrar vehículos localmente\n  const filteredVehicles = vehicles.filter((vehicle: any) => {\n    const matchesSearch = \n      vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      vehicle.features?.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      vehicle.host?.name?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    const vehicleCategory = bodyTypeLabels[vehicle.bodyType] || vehicle.bodyType\n    const matchesCategory = selectedCategory === \"all\" || vehicleCategory === selectedCategory\n\n    const isAvailable = vehicle.status === \"active\"\n    const matchesAvailability = selectedAvailability === \"all\" ||\n      (selectedAvailability === \"available\" && isAvailable) ||\n      (selectedAvailability === \"unavailable\" && !isAvailable)\n\n    return matchesSearch && matchesCategory && matchesAvailability\n  })\n\n  const VehicleCard = ({ vehicle, isListView = false }: { vehicle: any; isListView?: boolean }) => (\n    <Link href={`/vehicles/${vehicle.id}`} key={vehicle.id}>\n      <Card className={`overflow-hidden hover:shadow-lg transition-shadow cursor-pointer ${isListView ? \"flex\" : \"\"}`}>\n        <div className={`relative ${isListView ? \"w-48 flex-shrink-0\" : \"\"}`}>\n          <Image\n            src={vehicle.images?.[0] || \"/placeholder.svg\"}\n            alt={`${vehicle.make} ${vehicle.model}`}\n            width={300}\n            height={200}\n            className={`object-cover ${isListView ? \"h-full w-full\" : \"w-full h-48\"}`}\n            unoptimized={true}\n          />\n          <div className=\"absolute top-2 right-2 z-10\" onClick={(e) => e.preventDefault()}>\n            <FavoriteButton vehicleId={vehicle.id} className=\"bg-white hover:bg-gray-50\" />\n          </div>\n          {vehicle.status !== \"active\" && (\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n              <Badge variant=\"secondary\" className=\"bg-white text-black\">\n                No disponible\n              </Badge>\n            </div>\n          )}\n        </div>\n        <CardContent className={`p-4 ${isListView ? \"flex-1\" : \"\"}`}>\n          <div className=\"flex justify-between items-start mb-2\">\n            <h3 className=\"font-semibold text-lg\">\n              {vehicle.make} {vehicle.model} {vehicle.year}\n            </h3>\n            <div className=\"text-right\">\n              <div className=\"text-xl font-bold\">${vehicle.price}</div>\n              <div className=\"text-sm text-gray-500\">por día</div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center mb-2\">\n            <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400 mr-1\" />\n            <span className=\"text-sm font-medium\">{vehicle.rating || 0}</span>\n            <span className=\"text-sm text-gray-500 ml-1\">({vehicle.reviews || 0} reseñas)</span>\n          </div>\n\n          <div className=\"flex items-center text-sm text-gray-600 mb-3\">\n            <MapPin className=\"h-4 w-4 mr-1\" />\n            {vehicle.features?.location || 'Ubicación no especificada'}\n          </div>\n\n          <div className=\"text-sm text-gray-600 mb-3\">\n            {vehicle.engineSize}L • {vehicle.transmission === 'automatic' ? 'Automático' : 'Manual'} • {bodyTypeLabels[vehicle.bodyType] || vehicle.bodyType}\n          </div>\n\n          <div className=\"flex flex-wrap gap-1 mb-3\">\n            {vehicle.amenities?.slice(0, 3).map((amenity: string, index: number) => (\n              <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                {amenity}\n              </Badge>\n            ))}\n            {vehicle.amenities?.length > 3 && (\n              <Badge variant=\"outline\" className=\"text-xs\">\n                +{vehicle.amenities.length - 3} más\n              </Badge>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </Link>\n  )\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-[400px]\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex flex-col items-center justify-center min-h-[400px] text-center\">\n        <Heart className=\"h-16 w-16 text-gray-300 mb-4\" />\n        <h2 className=\"text-2xl font-semibold mb-2\">Error al cargar favoritos</h2>\n        <p className=\"text-gray-600 mb-4\">\n          No se pudieron cargar tus vehículos favoritos. Por favor, intenta de nuevo.\n        </p>\n        <Button onClick={() => window.location.reload()}>\n          Reintentar\n        </Button>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-3xl font-bold tracking-tight\">Favoritos</h1>\n        <p className=\"text-muted-foreground\">\n          Tus vehículos guardados para futuras reservas ({pagination?.total || 0} vehículos)\n        </p>\n      </div>\n\n      {/* Filtros y controles */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">Filtros</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-col md:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Buscar por marca, modelo, ubicación...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n\n            <Select value={selectedCategory} onValueChange={setSelectedCategory}>\n              <SelectTrigger className=\"w-full md:w-48\">\n                <SelectValue placeholder=\"Categoría\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">Todas las categorías</SelectItem>\n                {Object.entries(bodyTypeLabels).map(([key, label]) => (\n                  <SelectItem key={key} value={label}>{label}</SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n\n            <Select value={selectedAvailability} onValueChange={setSelectedAvailability}>\n              <SelectTrigger className=\"w-full md:w-48\">\n                <SelectValue placeholder=\"Disponibilidad\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">Todos</SelectItem>\n                <SelectItem value=\"available\">Disponibles</SelectItem>\n                <SelectItem value=\"unavailable\">No disponibles</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <div className=\"flex gap-2\">\n              <Button\n                variant={viewMode === \"grid\" ? \"default\" : \"outline\"}\n                size=\"icon\"\n                onClick={() => setViewMode(\"grid\")}\n              >\n                <Grid className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant={viewMode === \"list\" ? \"default\" : \"outline\"}\n                size=\"icon\"\n                onClick={() => setViewMode(\"list\")}\n              >\n                <List className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Lista de vehículos */}\n      <div>\n        {filteredVehicles.length === 0 ? (\n          <Card>\n            <CardContent className=\"flex flex-col items-center justify-center py-12\">\n              <Heart className=\"h-12 w-12 text-muted-foreground mb-4\" />\n              <h3 className=\"text-lg font-semibold mb-2\">No se encontraron favoritos</h3>\n              <p className=\"text-muted-foreground text-center\">\n                {vehicles.length === 0\n                  ? \"Aún no tienes vehículos favoritos. Explora nuestro catálogo y guarda los que más te gusten.\"\n                  : \"No tienes vehículos favoritos que coincidan con los filtros seleccionados.\"\n                }\n              </p>\n              {vehicles.length === 0 && (\n                <Button className=\"mt-4\" onClick={() => router.push('/vehicles')}>\n                  Explorar vehículos\n                </Button>\n              )}\n            </CardContent>\n          </Card>\n        ) : (\n          <div className={viewMode === \"grid\" ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\" : \"space-y-4\"}>\n            {filteredVehicles.map((vehicle) => (\n              <VehicleCard key={vehicle.id} vehicle={vehicle} isListView={viewMode === \"list\"} />\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Paginación */}\n      {pagination && pagination.totalPages > 1 && (\n        <div className=\"flex justify-center\">\n          <PaginationControl\n            currentPage={pagination.page}\n            totalPages={pagination.totalPages}\n            baseUrl=\"/dashboard/client/favorites\"\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": ["useState", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "Input", "Badge", "Heart", "Search", "Star", "MapPin", "Grid", "List", "Loader2", "Select", "SelectContent", "SelectItem", "SelectTrigger", "SelectValue", "Image", "Link", "useQuery", "favoritesApi", "useUser", "FavoriteButton", "PaginationControl", "useRouter", "useSearchParams", "bodyTypeLabels", "sedan", "suv", "hatchback", "pickup", "coupe", "convertible", "wagon", "van", "minivan", "targa", "doublecab", "truck", "FavoritesPage", "user", "router", "searchParams", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedAvailability", "setSelectedAvailability", "viewMode", "setViewMode", "currentPage", "parseInt", "get", "limit", "userType", "push", "data", "favoritesData", "isLoading", "error", "query<PERSON><PERSON>", "queryFn", "getFavorites", "page", "staleTime", "vehicles", "pagination", "filteredVehicles", "filter", "vehicle", "matchesSearch", "make", "toLowerCase", "includes", "model", "features", "location", "host", "name", "vehicleCategory", "bodyType", "matchesCategory", "isAvailable", "status", "matchesAvailability", "VehicleCard", "isListView", "id", "images", "e", "preventDefault", "year", "price", "rating", "reviews", "engineSize", "transmission", "amenities", "slice", "map", "amenity", "index", "length", "window", "reload", "total", "target", "value", "Object", "entries", "key", "label", "totalPages"], "mappings": ";;;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,QAAQ,cAAc;;;;;;;AAC/E,SAASC,MAAM,EAAEC,aAAa,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,wBAAwB;AACtG,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,IAAI,MAAM,WAAW;AAC5B,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,SAAS,EAAEC,eAAe,QAAQ,iBAAiB;;;AAhB5D,YAAY;;;;;;;;;;;;;;;;AAkBZ,0CAAA;AACA,MAAMC,cAAsC,GAAG;IAC7CC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,WAAW;IACtBC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE;AACT,CAAC;AAEc;;IACb,MAAM,EAAEE,IAAAA,EAAM,uJAAGnB;IACjB,MAAMoB,MAAM,yJAAGjB,AAAS,CAAC,CAAC;IAC1B,MAAMkB,YAAY,+JAAGjB;IAErB,+BAAA;IACA,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAG/C,6KAAQ,AAARA,EAAS,EAAE,CAAC;IAChD,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,qKAAGjD,WAAAA,AAAQ,EAAC,KAAK,CAAC;IAC/D,MAAM,CAACkD,oBAAoB,EAAEC,uBAAuB,CAAC,qKAAGnD,WAAAA,AAAQ,EAAC,KAAK,CAAC;IACvE,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,qKAAGrD,WAAQ,AAARA,EAA0B,MAAM,CAAC;IAEjE,aAAA;IACA,MAAMsD,WAAW,GAAGC,QAAQ,CAACV,YAAY,CAACW,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;IAC7D,MAAMC,KAAK,GAAG,EAAE;IAEhB,8CAAA;IACA,IAAI,CAACd,IAAI,IAAIA,IAAI,CAACe,QAAQ,KAAK,QAAQ,EAAE;QACvC,qBACE,6LAAC,GAAG;YAAC,SAAS,EAAC,qEAAqE;;8BAClF,qYAAC,QAAK;oBAAC,SAAS,EAAC,8BAA8B;;;;;;8BAC/C,6LAAC,EAAE;oBAAC,SAAS,EAAC,6BAA6B;8BAAC,kBAAkB,EAAE,EAAE;;;;;;8BAClE,6LAAC,CAAC;oBAAC,SAAS,EAAC,oBAAoB;8BAAA;;;;;;8BAGjC,mUAAC,SAAM;oBAAC,OAAO,CAAC,CAAC,IAAMd,MAAM,CAACe,IAAI,CAAC,YAAY,CAAC,CAAC;8BAAA;;;;;;;;;;;;IAKvD;IAEA,+BAAA;IACA,MAAM,EAAEC,IAAI,EAAEC,aAAa,EAAEC,SAAS,EAAEC,KAAAA,EAAO,iMAAGzC,EAAS;QACzD0C,QAAQ,EAAE;YAAC,WAAW;YAAEV,WAAW;YAAEG,KAAK;SAAC;QAC3CQ,OAAO;sCAAEA,CAAA,4IAAM1C,eAAY,CAAC2C,YAAY,CAAC;oBAAEC,IAAI,EAAEb,WAAW;oBAAEG;gBAAM,CAAC,CAAC;;QACtEW,SAAS,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC,CAAE,YAAA;IAC5B,CAAC,CAAC;IAEF,MAAMC,QAAQ,GAAGR,aAAa,EAAED,IAAI,IAAI,EAAE;IAC1C,MAAMU,UAAU,GAAGT,aAAa,EAAES,UAAU;IAE5C,+BAAA;IACA,MAAMC,gBAAgB,GAAGF,QAAQ,CAACG,MAAM,EAAEC,OAAY,IAAK;QACzD,MAAMC,aAAa,GACjBD,OAAO,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,IAC7DH,OAAO,CAACK,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,IAC9DH,OAAO,CAACM,QAAQ,EAAEC,QAAQ,EAAEJ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,IAC5EH,OAAO,CAACQ,IAAI,EAAEC,IAAI,EAAEN,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC;QAEtE,MAAMO,eAAe,GAAGtD,cAAc,CAAC4C,OAAO,CAACW,QAAQ,CAAC,IAAIX,OAAO,CAACW,QAAQ;QAC5E,MAAMC,eAAe,GAAGrC,gBAAgB,KAAK,KAAK,IAAImC,eAAe,KAAKnC,gBAAgB;QAE1F,MAAMsC,WAAW,GAAGb,OAAO,CAACc,MAAM,KAAK,QAAQ;QAC/C,MAAMC,mBAAmB,GAAGtC,oBAAoB,KAAK,KAAK,IACvDA,oBAAoB,KAAK,WAAW,IAAIoC,WAAY,IACpDpC,oBAAoB,KAAK,aAAa,IAAI,CAACoC,WAAY;QAE1D,OAAOZ,aAAa,IAAIW,eAAe,IAAIG,mBAAmB;IAChE,CAAC,CAAC;IAEF,MAAMC,WAAW,GAAGA,CAAC,EAAEhB,OAAO,EAAPA,SAAO,EAAEiB,UAAU,GAAG,KAAA,EAA+C,iBAC1F,6VAAC,UAAI;YAAC,IAAI,CAAC,CAAE,CAAA,UAAA,EAAYjB,SAAO,CAACkB,EAAG,EAAC,CAAC,CAAC,GAAG,CAAC;oCACzC,iUAAC,OAAI;gBAAC,SAAS,CAAC,CAAE,CAAA,iEAAA,EAAmED,UAAU,GAAG,MAAM,GAAG,EAAG,EAAC,CAAC;;kCAC9G,6LAAC,GAAG;wBAAC,SAAS,CAAC,CAAE,CAAA,SAAA,EAAWA,UAAU,GAAG,oBAAoB,GAAG,EAAG,EAAC,CAAC;;0CACnE,8TAAC,UAAK;gCACJ,GAAG,CAAC,CAACjB,SAAO,CAACmB,MAAM,EAAA,CAAG,CAAC,CAAC,IAAI,kBAAkB,CAAC;gCAC/C,GAAG,CAAC,CAAE,GAAEnB,SAAO,CAACE,IAAK,CAAA,CAAA,EAAGF,SAAO,CAACK,KAAM,EAAC,CAAC;gCACxC,KAAK,CAAC,CAAC,GAAG,CAAC;gCACX,MAAM,CAAC,CAAC,GAAG,CAAC;gCACZ,SAAS,CAAC,CAAE,CAAA,aAAA,EAAeY,UAAU,GAAG,eAAe,GAAG,aAAc,EAAC,CAAC;gCAC1E,WAAW,CAAC,CAAC,IAAI,CAAC;;;;;;0CAEpB,6LAAC,GAAG;gCAAC,SAAS,EAAC,6BAA6B;gCAAC,OAAO,CAAC,EAAEG,CAAC,GAAKA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC;0CAC9E,mWAAC,UAAc;oCAAC,SAAS,CAAC,CAACrB,SAAO,CAACkB,EAAE,CAAC;oCAAC,SAAS,EAAC,2BAA2B;;;;;;;;;;;4BAE7ElB,SAAO,CAACc,MAAM,KAAK,QAAQ,kBAC1B,6LAAC,GAAG;gCAAC,SAAS,EAAC,0EAA0E;wDACvF,iUAAC,SAAK;oCAAC,OAAO,EAAC,WAAW;oCAAC,SAAS,EAAC,qBAAqB;8CAAA;;;;;;;;;;;;;;;;;kCAMhE,iUAAC,cAAW;wBAAC,SAAS,CAAC,CAAE,CAAA,IAAA,EAAMG,UAAU,GAAG,QAAQ,GAAG,EAAG,EAAC,CAAC;;0CAC1D,6LAAC,GAAG;gCAAC,SAAS,EAAC,uCAAuC;;kDACpD,6LAAC,EAAE;wCAAC,SAAS,EAAC,uBAAuB;;4CAClCjB,SAAO,CAACE,IAAI;4CAAC,CAAC;4CAACF,SAAO,CAACK,KAAK;4CAAC,CAAC;4CAACL,SAAO,CAACsB,IAAI;;;;;;;kDAE9C,6LAAC,GAAG;wCAAC,SAAS,EAAC,YAAY;;0DACzB,6LAAC,GAAG;gDAAC,SAAS,EAAC,mBAAmB;;oDAAC,CAAC;oDAACtB,SAAO,CAACuB,KAAK,CAAC,EAAE,GAAG;;;;;;;0DACxD,6LAAC,GAAG;gDAAC,SAAS,EAAC,uBAAuB;0DAAC,OAAO,EAAE,GAAG;;;;;;;;;;;;;;;;;;0CAIvD,6LAAC,GAAG;gCAAC,SAAS,EAAC,wBAAwB;;kDACrC,mYAAC,OAAI;wCAAC,SAAS,EAAC,8CAA8C;;;;;;kDAC9D,6LAAC,IAAI;wCAAC,SAAS,EAAC,qBAAqB,CAAC;kDAACvB,SAAO,CAACwB,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI;;;;;;kDACjE,6LAAC,IAAI;wCAAC,SAAS,EAAC,4BAA4B;;4CAAC,CAAC;4CAACxB,SAAO,CAACyB,OAAO,IAAI,CAAC;4CAAC,SAAS,EAAE,IAAI;;;;;;;;;;;;;0CAGrF,6LAAC,GAAG;gCAAC,SAAS,EAAC,8CAA8C;;kDAC3D,2YAAC,SAAM;wCAAC,SAAS,EAAC,cAAc;;;;;;oCAC/BzB,SAAO,CAACM,QAAQ,EAAEC,QAAQ,IAAI,2BAA2B;;;;;;;0CAG5D,6LAAC,GAAG;gCAAC,SAAS,EAAC,4BAA4B;;oCACxCP,SAAO,CAAC0B,UAAU;oCAAC,IAAI;oCAAC1B,SAAO,CAAC2B,YAAY,KAAK,WAAW,GAAG,YAAY,GAAG,QAAQ;oCAAC,GAAG;oCAACvE,cAAc,CAAC4C,SAAO,CAACW,QAAQ,CAAC,IAAIX,SAAO,CAACW,QAAQ;;;;;;;0CAGlJ,6LAAC,GAAG;gCAAC,SAAS,EAAC,2BAA2B;;oCACvCX,SAAO,CAAC4B,SAAS,EAAEC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,OAAe,EAAEC,KAAa,iBACjE,kUAAC,QAAK,CAAC,GAAG,CAAC;4CAAQ,OAAO,EAAC,SAAS;4CAAC,SAAS,EAAC,SAAS;sDACrDD,OAAO;2CADEC,KAAK,CAAC;;;;;oCAInBhC,SAAO,CAAC4B,SAAS,EAAEK,MAAM,GAAG,CAAC,kBAC5B,kUAAC,QAAK;wCAAC,OAAO,EAAC,SAAS;wCAAC,SAAS,EAAC,SAAS;;4CAAA;4CACxCjC,SAAO,CAAC4B,SAAS,CAACK,MAAM,GAAG,CAAC;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;WAxDCjC,SAAO,CAACkB,EAAE,CAAC;;;;;IAiEzD,IAAI7B,SAAS,EAAE;QACb,qBACE,6LAAC,GAAG;YAAC,SAAS,EAAC,gDAAgD;oCAC7D,kZAAC,UAAO;gBAAC,SAAS,EAAC,sBAAsB;;;;;;;;;;;IAG/C;IAEA,IAAIC,KAAK,EAAE;QACT,qBACE,6LAAC,GAAG;YAAC,SAAS,EAAC,qEAAqE;;8BAClF,6LAAC,gNAAK;oBAAC,SAAS,EAAC,8BAA8B;;;;;;8BAC/C,6LAAC,EAAE;oBAAC,SAAS,EAAC,6BAA6B;8BAAC,yBAAyB,EAAE,EAAE;;;;;;8BACzE,6LAAC,CAAC;oBAAC,SAAS,EAAC,oBAAoB;8BAAA;;;;;;8BAGjC,mUAAC,SAAM;oBAAC,OAAO,CAAC,CAAC,IAAM4C,MAAM,CAAC3B,QAAQ,CAAC4B,MAAM,CAAC,CAAC,CAAC;8BAAA;;;;;;;;;;;;IAKtD;IAEA,qBACE,6LAAC,GAAG;QAAC,SAAS,EAAC,WAAW;;0BACxB,6LAAC,GAAG;;kCACF,6LAAC,EAAE;wBAAC,SAAS,EAAC,mCAAmC;kCAAC,SAAS,EAAE,EAAE;;;;;;kCAC/D,6LAAC,CAAC;wBAAC,SAAS,EAAC,uBAAuB;;4BAAA;4BACctC,UAAU,EAAEuC,KAAK,IAAI,CAAC;4BAAC;;;;;;;;;;;;;0BAK3E,iUAAC,OAAI;;kCACH,iUAAC,aAAU;gDACT,iUAAC,YAAS;4BAAC,SAAS,EAAC,SAAS;sCAAC,OAAO,EAAE,SAAS;;;;;;;;;;;kCAEnD,iUAAC,cAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,iCAAiC;;8CAC9C,6LAAC,GAAG;oCAAC,SAAS,EAAC,QAAQ;4DACrB,6LAAC,GAAG;wCAAC,SAAS,EAAC,UAAU;;0DACvB,sYAAC,UAAM;gDAAC,SAAS,EAAC,0EAA0E;;;;;;0DAC5F,kUAAC,QAAK;gDACJ,WAAW,EAAC,wCAAwC;gDACpD,KAAK,CAAC,CAAC/D,UAAU,CAAC;gDAClB,QAAQ,CAAC,EAAE+C,GAAC,GAAK9C,aAAa,CAAC8C,GAAC,CAACiB,MAAM,CAACC,KAAK,CAAC,CAAC;gDAC/C,SAAS,EAAC,OAAO;;;;;;;;;;;;;;;;;8CAKvB,mUAAC,SAAM;oCAAC,KAAK,CAAC,CAAC/D,gBAAgB,CAAC;oCAAC,aAAa,CAAC,CAACC,mBAAmB,CAAC;;sDAClE,mUAAC,gBAAa;4CAAC,SAAS,EAAC,gBAAgB;oEACvC,mUAAC,cAAW;gDAAC,WAAW,EAAC,WAAW;;;;;;;;;;;sDAEtC,6LAAC,sJAAa;;8DACZ,mUAAC,aAAU;oDAAC,KAAK,EAAC,KAAK;8DAAC,oBAAoB,EAAE,UAAU;;;;;;gDACvD+D,MAAM,CAACC,OAAO,CAACpF,cAAc,CAAC,CAAC0E,GAAG,CAAC,CAAC,CAACW,GAAG,EAAEC,KAAK,CAAC,iBAC/C,mUAAC,aAAU,CAAC,GAAG,CAAC;wDAAM,KAAK,CAAC,CAACA,KAAK,CAAC,CAAC;kEAACA,KAAK,CAAC,EAAE,UAAU,CACxD,CAAC;uDADiBD,GAAG,CAAC;;;;;;;;;;;;;;;;;8CAK3B,mUAAC,SAAM;oCAAC,KAAK,CAAC,CAAChE,oBAAoB,CAAC;oCAAC,aAAa,CAAC,CAACC,uBAAuB,CAAC;;sDAC1E,mUAAC,gBAAa;4CAAC,SAAS,EAAC,gBAAgB;oEACvC,mUAAC,cAAW;gDAAC,WAAW,EAAC,gBAAgB;;;;;;;;;;;sDAE3C,mUAAC,gBAAa;;8DACZ,mUAAC,aAAU;oDAAC,KAAK,EAAC,KAAK;8DAAC,KAAK,EAAE,UAAU;;;;;;8DACzC,mUAAC,aAAU;oDAAC,KAAK,EAAC,WAAW;8DAAC,WAAW,EAAE,UAAU;;;;;;8DACrD,mUAAC,aAAU;oDAAC,KAAK,EAAC,aAAa;8DAAC,cAAc,EAAE,UAAU;;;;;;;;;;;;;;;;;;8CAI9D,6LAAC,GAAG;oCAAC,SAAS,EAAC,YAAY;;sDACzB,mUAAC,SAAM;4CACL,OAAO,CAAC,CAACC,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,CAAC;4CACrD,IAAI,EAAC,MAAM;4CACX,OAAO,CAAC,CAAC,IAAMC,WAAW,CAAC,MAAM,CAAC,CAAC;sDAEnC,wZAAC,OAAI;gDAAC,SAAS,EAAC,SAAS;;;;;;;;;;;sDAE3B,mUAAC,SAAM;4CACL,OAAO,CAAC,CAACD,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,CAAC;4CACrD,IAAI,EAAC,MAAM;4CACX,OAAO,CAAC,CAAC,IAAMC,WAAW,CAAC,MAAM,CAAC,CAAC;sDAEnC,iZAAC,OAAI;gDAAC,SAAS,EAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnC,6LAAC,GAAG;0BACDkB,gBAAgB,CAACmC,MAAM,KAAK,CAAC,iBAC5B,iUAAC,OAAI;4CACH,iUAAC,cAAW;wBAAC,SAAS,EAAC,iDAAiD;;0CACtE,qYAAC,QAAK;gCAAC,SAAS,EAAC,sCAAsC;;;;;;0CACvD,6LAAC,EAAE;gCAAC,SAAS,EAAC,4BAA4B;0CAAC,2BAA2B,EAAE,EAAE;;;;;;0CAC1E,6LAAC,CAAC;gCAAC,SAAS,EAAC,mCAAmC;0CAC7CrC,QAAQ,CAACqC,MAAM,KAAK,CAAC,GAClB,6FAA6F,GAC7F,4EAA4E;;;;;;4BAGjFrC,QAAQ,CAACqC,MAAM,KAAK,CAAC,kBACpB,mUAAC,SAAM;gCAAC,SAAS,EAAC,MAAM;gCAAC,OAAO,CAAC,CAAC,IAAM9D,MAAM,CAACe,IAAI,CAAC,WAAW,CAAC,CAAC;0CAAA;;;;;;;;;;;;;;;;yCAOvE,6LAAC,GAAG;oBAAC,SAAS,CAAC,CAACP,QAAQ,KAAK,MAAM,GAAG,sDAAsD,GAAG,WAAW,CAAC;8BACxGmB,gBAAgB,CAACgC,GAAG,EAAE9B,SAAO,iBAC5B,6LAAC,WAAW,CAAC,GAAG,CAAC;4BAAa,OAAO,CAAC,CAACA,SAAO,CAAC;4BAAC,UAAU,CAAC,CAACrB,QAAQ,KAAK,MAAM,CAAC,GACjF,CAAC;2BADkBqB,SAAO,CAACkB,EAAE,CAAC;;;;;;;;;;;;;;;YAOpCrB,UAAU,IAAIA,UAAU,CAAC8C,UAAU,GAAG,CAAC,kBACtC,6LAAC,GAAG;gBAAC,SAAS,EAAC,qBAAqB;wCAClC,kVAAC,oBAAiB;oBAChB,WAAW,CAAC,CAAC9C,UAAU,CAACH,IAAI,CAAC;oBAC7B,UAAU,CAAC,CAACG,UAAU,CAAC8C,UAAU,CAAC;oBAClC,OAAO,EAAC,6BAA6B;;;;;;;;;;;;;;;;;AAMjD;;;8IApQmB5F,UAAO,CAAC,CAAC;8IACXG;8IACMC,kBAAe,CAAC,CAAC;uLA6BYN,WAAQ;;;KAhCpCoB,aAAaA,CAAA,EAAG", "debugId": null}}]}