{"sorted_middleware": ["/api/proxy/[...path]/route"], "middleware": {}, "instrumentation": null, "functions": {"/api/proxy/[...path]/route": {"files": ["server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/api/proxy/[...path]/route_client-reference-manifest.js", "server/edge/chunks/_next-internal_server_app_api_proxy_[___path]_route_actions_9ac85da9.js", "server/edge/chunks/_next-internal_server_app_api_proxy_[___path]_route_actions_0fab52f9.js", "server/edge/chunks/node_modules_next_dist_esm_59f9a33d._.js", "server/edge/chunks/node_modules_next_dist_compiled_41abe2bb._.js", "server/edge/chunks/node_modules_next_dist_9f2bc3b3._.js", "server/edge/chunks/edge-wrapper_4850fcba.js", "server/edge/chunks/node_modules_axios_lib_a7ce741a._.js", "server/edge/chunks/node_modules_zod_lib_index_mjs_02a82a6a._.js", "server/edge/chunks/node_modules_91d0b7c3._.js", "server/edge/chunks/[root-of-the-server]__7ea9a262._.js", "server/edge/chunks/edge-wrapper_7bea9196.js", "server/app/api/proxy/[...path]/route/react-loadable-manifest.js"], "name": "/api/proxy/[...path]", "page": "/api/proxy/[...path]/route", "matchers": [{"regexp": "^/api/proxy/(?P<nxtPpath>.+?)(?:/)?$", "originalSource": "/api/proxy/[...path]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "+KMTG3juMS67G8EHA0iwvAqQA7vFRf/YWxYXm8iQ2oo=", "__NEXT_PREVIEW_MODE_ID": "ecd3903b11165ecbecddf9772fb99e24", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "029f7edfd7bf482b69f740a0153773cb37cd69175fcaf4170dec5a30cf8b6684", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "50869fd5120f4a225436157cf39daebf36d5cb931fab8b6d71193054ec125c11"}}}}