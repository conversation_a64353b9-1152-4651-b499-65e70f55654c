{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/env.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const envSchema = z.object({\r\n  NEXT_PUBLIC_API_URL: z.string().url(),\r\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\r\n  IS_DEV: z.string().optional().transform((val) => val === 'true'),\r\n});\r\n\r\nexport type Env = z.infer<typeof envSchema>;\r\n\r\n// create the same object but with process.env\r\nconst env = envSchema.parse({\r\n  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n  NODE_ENV: process.env.NODE_ENV,\r\n  IS_DEV: process.env.IS_DEV,\r\n});\r\n\r\nexport default env;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,YAAY,8IAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,qBAAqB,8IAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IACnC,UAAU,8IAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAChE,QAAQ,8IAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,MAAQ,QAAQ;AAC3D;AAIA,8CAA8C;AAC9C,MAAM,MAAM,UAAU,KAAK,CAAC;IAC1B,mBAAmB;IACnB,QAAQ;IACR,QAAQ,QAAQ,GAAG,CAAC,MAAM;AAC5B;uCAEe"}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/index.ts"], "sourcesContent": ["export const BEARER_COOKIE_NAME = \"bearer_token\";\r\nexport const PENDING_INVITATION_COOKIE = 'pending_invitation';\r\nexport const PENDING_INVITATION_EMAIL_COOKIE = 'pending_invitation_email';\r\nexport const PENDING_INVITATION_ORG_ID_COOKIE = 'pending_invitation_org_id';"], "names": [], "mappings": ";;;;;;AAAO,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAClC,MAAM,kCAAkC;AACxC,MAAM,mCAAmC"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/cookies.ts"], "sourcesContent": ["'use server';\nimport { cookies } from \"next/headers\";\nimport { getCookie, setCookie, deleteCookie } from \"cookies-next/server\";\n\n\nexport const getCookieServerByName = async ({\n  name,\n}: {\n  name: string;\n}) => {\n  const cookie = getCookie(name, { cookies });\n  return cookie;\n};\n\nexport const setCookieServerByName = async ({\n  name,\n  value\n}: {\n  name: string;\n  value: string;\n}) => {\n  console.log('setCookieServerByName', name, value);\n  await setCookie(name, value, { cookies });\n};\n\nexport const deleteCookieServerByName = async ({\n  name\n}: {\n  name: string;\n}) => {\n  await deleteCookie(name, { cookies });\n};"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AACA;;;;;;AAGO,MAAM,wBAAwB,OAAO,EAC1C,IAAI,EAGL;IACC,MAAM,SAAS,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,MAAM;QAAE,SAAA,6KAAA,CAAA,UAAO;IAAC;IACzC,OAAO;AACT;AAEO,MAAM,wBAAwB,OAAO,EAC1C,IAAI,EACJ,KAAK,EAIN;IACC,QAAQ,GAAG,CAAC,yBAAyB,MAAM;IAC3C,MAAM,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO;QAAE,SAAA,6KAAA,CAAA,UAAO;IAAC;AACzC;AAEO,MAAM,2BAA2B,OAAO,EAC7C,IAAI,EAGL;IACC,MAAM,CAAA,GAAA,mKAAA,CAAA,eAAY,AAAD,EAAE,MAAM;QAAE,SAAA,6KAAA,CAAA,UAAO;IAAC;AACrC;;;IA1Ba;IASA;IAWA;;AApBA,4OAAA;AASA,4OAAA;AAWA,4OAAA"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\r\nimport env from \"../constants/env\";\r\nimport { BEARER_COOKIE_NAME } from '@/constants';\r\nimport { getCookie } from 'cookies-next/client';\r\n// import { sendLogToLogflare } from '@/lib/log-requests';\r\n\r\nfunction checkIfIsClient() {\r\n    return typeof window !== 'undefined';\r\n}\r\n\r\ntype ApiResponse<T> =\r\n    {\r\n        success: true;\r\n        data: T; status:\r\n        number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: InternalAxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: undefined\r\n    }\r\n    |\r\n    {\r\n        success: false;\r\n        data: undefined;\r\n        status: number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: AxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: any\r\n    };\r\n\r\ntype ErrorHandler = (error: any) => void;\r\n\r\nconst baseURL = env.NEXT_PUBLIC_API_URL;\r\n\r\nclass ApiService {\r\n    private axiosInstance: AxiosInstance;\r\n    baseURL: string = '';\r\n\r\n    /** \r\n     * @param version - The version of the API to use. Defaults to 'v1'.\r\n     */\r\n    constructor(\r\n        {\r\n            version,\r\n            prefix\r\n        }:\r\n            {\r\n                version?: 'v1' | 'v2',\r\n                prefix?: 'api' | 'dash-utils'\r\n            } =\r\n            {\r\n                version: 'v1',\r\n                prefix: 'api'\r\n            }) {\r\n\r\n\r\n        // Version is only available for api prefix so if prefix is dash-utils, version is not used\r\n        this.baseURL = `${baseURL}/${prefix}${prefix === 'api' ? `/${version}` : ''}`;\r\n\r\n        this.axiosInstance = axios.create({\r\n            baseURL: this.baseURL,\r\n            withCredentials: true\r\n        });\r\n    }\r\n\r\n    private async setHeaders() {\r\n        const isClient = checkIfIsClient();\r\n        if (isClient) {\r\n            return {};\r\n\r\n        };\r\n        const headers = (await import('next/headers')).headers;\r\n        const rawHeaders = await headers();\r\n        const headersObj: Record<string, string> = {};\r\n        rawHeaders.forEach((value, key) => {\r\n            headersObj[key] = value;\r\n        });\r\n        return headersObj;\r\n    }\r\n\r\n    private async request<T>(\r\n        method: 'get' | 'post' | 'patch' | 'put' | 'delete',\r\n        path: string,\r\n        config?: AxiosRequestConfig,\r\n        onError?: ErrorHandler\r\n    ): Promise<ApiResponse<T>> {\r\n        try {\r\n            const isClient = checkIfIsClient();\r\n            const headers = await this.setHeaders();\r\n            let token = ''\r\n\r\n            if (isClient) {\r\n                token = getCookie(BEARER_COOKIE_NAME) as string;\r\n            } else {\r\n                const getCookieServerByName = (await import('@/actions/cookies')).getCookieServerByName;\r\n                token = await getCookieServerByName({ name: BEARER_COOKIE_NAME }) as string;\r\n                if (path.includes('/files/download')) {\r\n                    console.log('Token on request for /api/v1/files/download: ', token);\r\n                }\r\n            }\r\n\r\n            const requestHeaders = {\r\n                    cookie: isClient ? undefined : headers.cookie,\r\n                    'x-dashboard-call': 'true',\r\n                    Authorization: `Bearer ${token}`,\r\n                    ...config?.headers\r\n                }\r\n\r\n            if (path.includes('/files/download')) {\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n                console.log('Headers: ', headers);\r\n                console.log('Request on /api/v1/files/download: ', requestHeaders);\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n            }\r\n\r\n            const response = await this.axiosInstance({\r\n                method,\r\n                url: path,\r\n                ...config,\r\n                headers: requestHeaders,\r\n            });\r\n            // Simplificamos el objeto de respuesta para evitar problemas de serialización\r\n            return {\r\n                success: true,\r\n                data: response.data,\r\n                status: response.status,\r\n                statusText: response.statusText,\r\n                headers: response.headers,\r\n                config: response.config,\r\n                request: response.request,\r\n                error: undefined\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error in request:', error?.config?.url);\r\n\r\n            if (onError) onError(error);\r\n            await this.handleError(error);\r\n            return {\r\n                success: false,\r\n                data: undefined,\r\n                status: error.response?.status || 500,\r\n                statusText: error.response?.statusText || 'Unknown Error',\r\n                headers: error.response?.headers || {},\r\n                config: error.config || {},\r\n                request: error.request || {},\r\n                error: error?.response?.data || error.message,\r\n            };\r\n        }\r\n    }\r\n\r\n    public async get<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('get', path, config, onError);\r\n    }\r\n\r\n    public async post<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('post', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async patch<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('patch', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async put<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('put', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async delete<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('delete', path, config, onError);\r\n    }\r\n\r\n    private async handleError(error: any) {\r\n        console.error('API Error:', {\r\n            message: error.message,\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n        });\r\n    }\r\n}\r\n\r\nexport const apiService = new ApiService();\r\n\r\nexport const dashUtilsService = new ApiService({ prefix: 'dash-utils' });\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACA,0DAA0D;AAE1D,SAAS;IACL,OAAO,gBAAkB;AAC7B;AA2BA,MAAM,UAAU,iIAAA,CAAA,UAAG,CAAC,mBAAmB;AAEvC,MAAM;IACM,cAA6B;IACrC,UAAkB,GAAG;IAErB;;KAEC,GACD,YACI,EACI,OAAO,EACP,MAAM,EAKL,GACD;QACI,SAAS;QACT,QAAQ;IACZ,CAAC,CAAE;QAGP,2FAA2F;QAC3F,IAAI,CAAC,OAAO,GAAG,GAAG,QAAQ,CAAC,EAAE,SAAS,WAAW,QAAQ,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI;QAE7E,IAAI,CAAC,aAAa,GAAG,+IAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC9B,SAAS,IAAI,CAAC,OAAO;YACrB,iBAAiB;QACrB;IACJ;IAEA,MAAc,aAAa;QACvB,MAAM,WAAW;QACjB,uCAAc;;QAGd;;QACA,MAAM,UAAU,CAAC,8IAA4B,EAAE,OAAO;QACtD,MAAM,aAAa,MAAM;QACzB,MAAM,aAAqC,CAAC;QAC5C,WAAW,OAAO,CAAC,CAAC,OAAO;YACvB,UAAU,CAAC,IAAI,GAAG;QACtB;QACA,OAAO;IACX;IAEA,MAAc,QACV,MAAmD,EACnD,IAAY,EACZ,MAA2B,EAC3B,OAAsB,EACC;QACvB,IAAI;YACA,MAAM,WAAW;YACjB,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;YACrC,IAAI,QAAQ;YAEZ,uCAAc;;YAEd,OAAO;gBACH,MAAM,wBAAwB,CAAC,2HAAiC,EAAE,qBAAqB;gBACvF,QAAQ,MAAM,sBAAsB;oBAAE,MAAM,mIAAA,CAAA,qBAAkB;gBAAC;gBAC/D,IAAI,KAAK,QAAQ,CAAC,oBAAoB;oBAClC,QAAQ,GAAG,CAAC,iDAAiD;gBACjE;YACJ;YAEA,MAAM,iBAAiB;gBACf,QAAQ,6EAAuB,QAAQ,MAAM;gBAC7C,oBAAoB;gBACpB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,GAAG,QAAQ,OAAO;YACtB;YAEJ,IAAI,KAAK,QAAQ,CAAC,oBAAoB;gBAClC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBAEZ,QAAQ,GAAG,CAAC,aAAa;gBACzB,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;YAEhB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;gBACtC;gBACA,KAAK;gBACL,GAAG,MAAM;gBACT,SAAS;YACb;YACA,8EAA8E;YAC9E,OAAO;gBACH,SAAS;gBACT,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,QAAQ,SAAS,MAAM;gBACvB,SAAS,SAAS,OAAO;gBACzB,OAAO;YACX;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,qBAAqB,OAAO,QAAQ;YAElD,IAAI,SAAS,QAAQ;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,OAAO;gBACH,SAAS;gBACT,MAAM;gBACN,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,YAAY,MAAM,QAAQ,EAAE,cAAc;gBAC1C,SAAS,MAAM,QAAQ,EAAE,WAAW,CAAC;gBACrC,QAAQ,MAAM,MAAM,IAAI,CAAC;gBACzB,SAAS,MAAM,OAAO,IAAI,CAAC;gBAC3B,OAAO,OAAO,UAAU,QAAQ,MAAM,OAAO;YACjD;QACJ;IACJ;IAEA,MAAa,IAAO,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC5G,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,QAAQ;IAC7C;IAEA,MAAa,KAAQ,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACzH,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC3D;IAEA,MAAa,MAAS,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC1H,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC5D;IAEA,MAAa,IAAO,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACxH,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC1D;IAEA,MAAa,OAAU,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC/G,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM,QAAQ;IAChD;IAEA,MAAc,YAAY,KAAU,EAAE;QAClC,QAAQ,KAAK,CAAC,cAAc;YACxB,SAAS,MAAM,OAAO;YACtB,QAAQ,MAAM,QAAQ,EAAE;YACxB,MAAM,MAAM,QAAQ,EAAE;QAC1B;IACJ;AACJ;AAEO,MAAM,aAAa,IAAI;AAEvB,MAAM,mBAAmB,IAAI,WAAW;IAAE,QAAQ;AAAa"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/api/proxy/[...path]/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { apiService } from '@/services/api';\n\nexport const runtime = 'edge';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ path: string[] }> }\n) {\n  try {\n    // Resolve params promise\n    const resolvedParams = await params;\n\n    // Construir el path para apiService\n    // apiService ya tiene /api/v1 como base, así que removemos esas partes del path\n    let pathParts = [...resolvedParams.path];\n\n    // Si el path empieza con 'v1', lo removemos porque apiService ya lo incluye\n    if (pathParts[0] === 'v1') {\n      pathParts = pathParts.slice(1);\n    }\n\n    // Limpiar y construir la URL correctamente\n    const cleanPath = `/${pathParts.join('/')}`;\n    const searchParams = new URLSearchParams(request.nextUrl.search);\n\n    // Remover el parámetro 'path' si existe (parece ser agregado por error)\n    searchParams.delete('path');\n\n    const apiPath = cleanPath + (searchParams.toString() ? `?${searchParams.toString()}` : '');\n\n    console.log('Original path parts:', resolvedParams.path);\n    console.log('Original search:', request.nextUrl.search);\n    console.log('Cleaned path for apiService:', apiPath);\n\n    // Debug: Verificar si tenemos token antes de hacer la llamada\n    const { getCookieServerByName } = await import('@/actions/cookies');\n    const debugToken = await getCookieServerByName({ name: 'bearer_token' });\n    console.log('Debug - Token before apiService call:', debugToken ? 'EXISTS' : 'NULL');\n    console.log('Debug - Token length:', debugToken?.length || 0);\n\n    // Usar apiService directamente con responseType arraybuffer para archivos\n    const result = await apiService.get(apiPath, {\n      responseType: 'arraybuffer'\n    });\n\n    if (!result.success) {\n      console.error('API service error:', result.error);\n      return new NextResponse(`Error fetching file: ${result.status} ${result.statusText}`, {\n        status: result.status\n      });\n    }\n\n    // Obtener el contenido y los headers de la respuesta\n    const data = result.data as ArrayBuffer;\n    const contentType = result.headers['content-type'] || 'application/octet-stream';\n    const contentDisposition = result.headers['content-disposition'];\n    const etag = result.headers['etag'];\n    const lastModified = result.headers['last-modified'];\n    const cacheControl = result.headers['cache-control'];\n\n    console.log('Response content type:', contentType);\n    console.log('Response size:', data.byteLength, 'bytes');\n\n    // Crear una nueva respuesta con los mismos headers y contenido\n    const newResponse = new NextResponse(data, {\n      status: result.status,\n      headers: {\n        'Content-Type': contentType,\n        ...(contentDisposition && { 'Content-Disposition': contentDisposition }),\n        ...(etag && { 'ETag': etag }),\n        ...(lastModified && { 'Last-Modified': lastModified }),\n        ...(cacheControl && { 'Cache-Control': cacheControl }),\n      }\n    });\n\n    return newResponse;\n  } catch (error: any) {\n    console.error('Error proxying file request:', error);\n    return new NextResponse('Internal Server Error', { status: 500 });\n  }\n}\n\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,MAAM,UAAU;AAEhB,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA2C;IAEnD,IAAI;QACF,yBAAyB;QACzB,MAAM,iBAAiB,MAAM;QAE7B,oCAAoC;QACpC,gFAAgF;QAChF,IAAI,YAAY;eAAI,eAAe,IAAI;SAAC;QAExC,4EAA4E;QAC5E,IAAI,SAAS,CAAC,EAAE,KAAK,MAAM;YACzB,YAAY,UAAU,KAAK,CAAC;QAC9B;QAEA,2CAA2C;QAC3C,MAAM,YAAY,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC,MAAM;QAC3C,MAAM,eAAe,IAAI,gBAAgB,QAAQ,OAAO,CAAC,MAAM;QAE/D,wEAAwE;QACxE,aAAa,MAAM,CAAC;QAEpB,MAAM,UAAU,YAAY,CAAC,aAAa,QAAQ,KAAK,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI,GAAG,EAAE;QAEzF,QAAQ,GAAG,CAAC,wBAAwB,eAAe,IAAI;QACvD,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,OAAO,CAAC,MAAM;QACtD,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,8DAA8D;QAC9D,MAAM,EAAE,qBAAqB,EAAE,GAAG;QAClC,MAAM,aAAa,MAAM,sBAAsB;YAAE,MAAM;QAAe;QACtE,QAAQ,GAAG,CAAC,yCAAyC,aAAa,WAAW;QAC7E,QAAQ,GAAG,CAAC,yBAAyB,YAAY,UAAU;QAE3D,0EAA0E;QAC1E,MAAM,SAAS,MAAM,gIAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS;YAC3C,cAAc;QAChB;QAEA,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,KAAK,CAAC,sBAAsB,OAAO,KAAK;YAChD,OAAO,IAAI,+LAAA,CAAA,eAAY,CAAC,CAAC,qBAAqB,EAAE,OAAO,MAAM,CAAC,CAAC,EAAE,OAAO,UAAU,EAAE,EAAE;gBACpF,QAAQ,OAAO,MAAM;YACvB;QACF;QAEA,qDAAqD;QACrD,MAAM,OAAO,OAAO,IAAI;QACxB,MAAM,cAAc,OAAO,OAAO,CAAC,eAAe,IAAI;QACtD,MAAM,qBAAqB,OAAO,OAAO,CAAC,sBAAsB;QAChE,MAAM,OAAO,OAAO,OAAO,CAAC,OAAO;QACnC,MAAM,eAAe,OAAO,OAAO,CAAC,gBAAgB;QACpD,MAAM,eAAe,OAAO,OAAO,CAAC,gBAAgB;QAEpD,QAAQ,GAAG,CAAC,0BAA0B;QACtC,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UAAU,EAAE;QAE/C,+DAA+D;QAC/D,MAAM,cAAc,IAAI,+LAAA,CAAA,eAAY,CAAC,MAAM;YACzC,QAAQ,OAAO,MAAM;YACrB,SAAS;gBACP,gBAAgB;gBAChB,GAAI,sBAAsB;oBAAE,uBAAuB;gBAAmB,CAAC;gBACvE,GAAI,QAAQ;oBAAE,QAAQ;gBAAK,CAAC;gBAC5B,GAAI,gBAAgB;oBAAE,iBAAiB;gBAAa,CAAC;gBACrD,GAAI,gBAAgB;oBAAE,iBAAiB;gBAAa,CAAC;YACvD;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,IAAI,+LAAA,CAAA,eAAY,CAAC,yBAAyB;YAAE,QAAQ;QAAI;IACjE;AACF"}}]}