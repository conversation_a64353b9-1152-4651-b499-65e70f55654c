globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/(root)/vehicles/[id]/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/src/app/react-scan.tsx <module evaluation>":{"id":"[project]/src/app/react-scan.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js"],"async":false},"[project]/src/app/react-scan.tsx":{"id":"[project]/src/app/react-scan.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js"],"async":false},"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>":{"id":"[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js"],"async":false},"[project]/node_modules/react-hot-toast/dist/index.mjs":{"id":"[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js"],"async":false},"[project]/src/providers/Providers.tsx <module evaluation>":{"id":"[project]/src/providers/Providers.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js"],"async":false},"[project]/src/providers/Providers.tsx":{"id":"[project]/src/providers/Providers.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/app-dir/link.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/app-dir/link.js":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js"],"async":false},"[project]/src/app/(root)/client.tsx <module evaluation>":{"id":"[project]/src/app/(root)/client.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js"],"async":false},"[project]/src/app/(root)/client.tsx":{"id":"[project]/src/app/(root)/client.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js"],"async":false},"[project]/src/app/(root)/vehicles/[id]/error.tsx <module evaluation>":{"id":"[project]/src/app/(root)/vehicles/[id]/error.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js","/_next/static/chunks/node_modules_e34233c4._.js","/_next/static/chunks/src_66508915._.js","/_next/static/chunks/src_app_(root)_vehicles_%5Bid%5D_error_tsx_55736f33._.js"],"async":false},"[project]/src/app/(root)/vehicles/[id]/error.tsx":{"id":"[project]/src/app/(root)/vehicles/[id]/error.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js","/_next/static/chunks/node_modules_e34233c4._.js","/_next/static/chunks/src_66508915._.js","/_next/static/chunks/src_app_(root)_vehicles_%5Bid%5D_error_tsx_55736f33._.js"],"async":false},"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js <module evaluation>":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/app-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/app-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/app-router.js":{"id":"[project]/node_modules/next/dist/client/components/app-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js":{"id":"[project]/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.js <module evaluation>":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.js":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"async":false},"[project]/src/app/(root)/vehicles/[id]/vehicle-detail-client.tsx <module evaluation>":{"id":"[project]/src/app/(root)/vehicles/[id]/vehicle-detail-client.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js","/_next/static/chunks/src_36c8755f._.js","/_next/static/chunks/node_modules_axios_lib_99999129._.js","/_next/static/chunks/node_modules_next_64025d81._.js","/_next/static/chunks/node_modules_zod_lib_index_mjs_ee760afb._.js","/_next/static/chunks/node_modules_luxon_src_dfb79c07._.js","/_next/static/chunks/node_modules_date-fns_546c907e._.js","/_next/static/chunks/node_modules_%40radix-ui_d1cbc13c._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_94142e73._.js","/_next/static/chunks/src_app_(root)_vehicles_%5Bid%5D_page_tsx_55736f33._.js"],"async":false},"[project]/src/app/(root)/vehicles/[id]/vehicle-detail-client.tsx":{"id":"[project]/src/app/(root)/vehicles/[id]/vehicle-detail-client.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js","/_next/static/chunks/src_36c8755f._.js","/_next/static/chunks/node_modules_axios_lib_99999129._.js","/_next/static/chunks/node_modules_next_64025d81._.js","/_next/static/chunks/node_modules_zod_lib_index_mjs_ee760afb._.js","/_next/static/chunks/node_modules_luxon_src_dfb79c07._.js","/_next/static/chunks/node_modules_date-fns_546c907e._.js","/_next/static/chunks/node_modules_%40radix-ui_d1cbc13c._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_94142e73._.js","/_next/static/chunks/src_app_(root)_vehicles_%5Bid%5D_page_tsx_55736f33._.js"],"async":false},"[project]/src/context/user-context.tsx <module evaluation>":{"id":"[project]/src/context/user-context.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js","/_next/static/chunks/src_36c8755f._.js","/_next/static/chunks/node_modules_axios_lib_99999129._.js","/_next/static/chunks/node_modules_next_64025d81._.js","/_next/static/chunks/node_modules_zod_lib_index_mjs_ee760afb._.js","/_next/static/chunks/node_modules_luxon_src_dfb79c07._.js","/_next/static/chunks/node_modules_date-fns_546c907e._.js","/_next/static/chunks/node_modules_%40radix-ui_d1cbc13c._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_94142e73._.js","/_next/static/chunks/src_app_(root)_vehicles_%5Bid%5D_page_tsx_55736f33._.js"],"async":false},"[project]/src/context/user-context.tsx":{"id":"[project]/src/context/user-context.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_5914f757._.js","/_next/static/chunks/src_e3b4d2f8._.js","/_next/static/chunks/src_app_layout_tsx_a4cb4545._.js","/_next/static/chunks/_bb028604._.js","/_next/static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js","/_next/static/chunks/src_36c8755f._.js","/_next/static/chunks/node_modules_axios_lib_99999129._.js","/_next/static/chunks/node_modules_next_64025d81._.js","/_next/static/chunks/node_modules_zod_lib_index_mjs_ee760afb._.js","/_next/static/chunks/node_modules_luxon_src_dfb79c07._.js","/_next/static/chunks/node_modules_date-fns_546c907e._.js","/_next/static/chunks/node_modules_%40radix-ui_d1cbc13c._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_94142e73._.js","/_next/static/chunks/src_app_(root)_vehicles_%5Bid%5D_page_tsx_55736f33._.js"],"async":false}},"ssrModuleMapping":{},"edgeSSRModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/error-boundary.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/src/app/react-scan.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/react-scan.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/react-hot-toast/dist/index.mjs [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/src/providers/Providers.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/Providers.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/app-dir/link.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/src/app/(root)/client.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/(root)/client.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/src/app/(root)/vehicles/[id]/error.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/(root)/vehicles/[id]/error.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/app-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/app-router.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/src/app/(root)/vehicles/[id]/vehicle-detail-client.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/(root)/vehicles/[id]/vehicle-detail-client.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/src/context/user-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/context/user-context.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}}},"rscModuleMapping":{},"edgeRscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/src/app/react-scan.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/react-scan.tsx (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/react-hot-toast/dist/index.mjs (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/src/providers/Providers.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/Providers.tsx (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/app-dir/link.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/src/app/(root)/client.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/(root)/client.tsx (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/src/app/(root)/vehicles/[id]/error.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/(root)/vehicles/[id]/error.tsx (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/app-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/app-router.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/src/app/(root)/vehicles/[id]/vehicle-detail-client.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/(root)/vehicles/[id]/vehicle-detail-client.tsx (client reference/proxy)","name":"*","chunks":[],"async":false}},"[project]/src/context/user-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/context/user-context.tsx (client reference/proxy)","name":"*","chunks":[],"async":false}}},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__88666d76._.css","inlined":false}],"[project]/src/app/(root)/layout":[{"path":"static/chunks/[root-of-the-server]__88666d76._.css","inlined":false}],"[project]/src/app/(root)/vehicles/[id]/error":[{"path":"static/chunks/[root-of-the-server]__88666d76._.css","inlined":false}],"[project]/src/app/(root)/vehicles/[id]/page":[{"path":"static/chunks/[root-of-the-server]__88666d76._.css","inlined":false},{"path":"static/chunks/node_modules_react-date-range_dist_4c16c247._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8e621b72._.js"],"[project]/src/app/layout":["static/chunks/node_modules_5914f757._.js","static/chunks/src_e3b4d2f8._.js","static/chunks/src_app_layout_tsx_a4cb4545._.js"],"[project]/src/app/(root)/layout":["static/chunks/node_modules_5914f757._.js","static/chunks/src_e3b4d2f8._.js","static/chunks/src_app_layout_tsx_a4cb4545._.js","static/chunks/_bb028604._.js","static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js"],"[project]/src/app/(root)/vehicles/[id]/error":["static/chunks/node_modules_5914f757._.js","static/chunks/src_e3b4d2f8._.js","static/chunks/src_app_layout_tsx_a4cb4545._.js","static/chunks/_bb028604._.js","static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js","static/chunks/node_modules_e34233c4._.js","static/chunks/src_66508915._.js","static/chunks/src_app_(root)_vehicles_[id]_error_tsx_55736f33._.js"],"[project]/src/app/(root)/vehicles/[id]/page":["static/chunks/node_modules_5914f757._.js","static/chunks/src_e3b4d2f8._.js","static/chunks/src_app_layout_tsx_a4cb4545._.js","static/chunks/_bb028604._.js","static/chunks/src_app_(root)_layout_tsx_fd9e9dac._.js","static/chunks/src_36c8755f._.js","static/chunks/node_modules_axios_lib_99999129._.js","static/chunks/node_modules_next_64025d81._.js","static/chunks/node_modules_zod_lib_index_mjs_ee760afb._.js","static/chunks/node_modules_luxon_src_dfb79c07._.js","static/chunks/node_modules_date-fns_546c907e._.js","static/chunks/node_modules_@radix-ui_d1cbc13c._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_94142e73._.js","static/chunks/src_app_(root)_vehicles_[id]_page_tsx_55736f33._.js"]}}
