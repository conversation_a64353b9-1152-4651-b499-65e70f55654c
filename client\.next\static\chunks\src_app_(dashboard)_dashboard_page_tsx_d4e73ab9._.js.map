{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28dashboard%29/dashboard/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useEffect } from \"react\"\r\nimport { useRouter } from \"next/navigation\"\r\n\r\nexport default function DashboardRedirect() {\r\n  const router = useRouter()\r\n\r\n  useEffect(() => {\r\n    // Mock user role - en producción vendría del contexto de autenticación\r\n    const userRole = \"admin\" // Cambiar según el usuario autenticado\r\n    router.push(`/dashboard/${userRole}`)\r\n  }, [router])\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center h-full\">\r\n      <div className=\"text-center\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n        <p className=\"mt-2 text-muted-foreground\">Redirigiendo...</p>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": ["c", "_c", "useEffect", "useRouter", "DashboardRedirect", "$", "$i", "Symbol", "for", "router", "t0", "t1", "push", "t2"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,SAAS,QAAQ,iBAAiB;;;AAH3C,YAAY;;;;AAKG;;IAAA,MAAAE,CAAA,mLAAAJ,IAAAA,AAAA,EAAA;IAAA,IAAAI,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACb,MAAAI,MAAA,4IAAeN,aAAAA;IAAW,IAAAO,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAI,MAAA,EAAA;QAEhBC,EAAA,GAAAA,CAAA;YAGRD,MAAM,CAAAG,IAAA,CAAM,CAAA,WAAA,EADK,OAAO,EACY,CAAC;QAAA;QACpCD,EAAA,GAAA;YAACF,MAAM;SAAA;QAACJ,CAAA,CAAA,EAAA,GAAAI,MAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;sKAJXH,YAAAA,AAAA,EAAUQ,EAIT,EAAEC,EAAQ,CAAC;IAAA,IAAAE,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGVK,EAAA,iBAAA,6LAAA,GAKM;YALS,SAAyC,EAAzC,yCAAyC;oCACtD,6LAAA,GAGM;gBAHS,SAAa,EAAb,aAAa;;kCAC1B,6LAAA,GAA2F;wBAA5E,SAAqE,EAArE,qEAAqE;;;;;;kCACpF,6LAAA,CAA6D;wBAAhD,SAA4B,EAA5B,4BAA4B;kCAAC,eAAe,EAAzD,CAA6D,CAC/D,EAHA,GAGM,CACR,EALA,GAKM;;;;;;;;;;;;;;;;;QAAAR,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,OALNQ,EAKM;AAAA;;;0JAdO,CAAU,CAAC;;;KADbT,kBAAA", "debugId": null}}]}