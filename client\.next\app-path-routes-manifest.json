{"/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/api/get-headers/route": "/api/get-headers", "/api/set-private-token/route": "/api/set-private-token", "/api/proxy/[...path]/route": "/api/proxy/[...path]", "/(auth)/email-verified/page": "/email-verified", "/(auth)/forgot-password/page": "/forgot-password", "/(auth)/reset-password/page": "/reset-password", "/(auth)/sign-up/page": "/sign-up", "/(auth)/sign-in/page": "/sign-in", "/(dashboard)/dashboard/admin/clients/page": "/dashboard/admin/clients", "/(dashboard)/dashboard/admin/hosts/page": "/dashboard/admin/hosts", "/(dashboard)/dashboard/admin/page": "/dashboard/admin", "/(dashboard)/dashboard/admin/profile/page": "/dashboard/admin/profile", "/(dashboard)/dashboard/admin/reservations/page": "/dashboard/admin/reservations", "/(dashboard)/dashboard/admin/payouts/page": "/dashboard/admin/payouts", "/(dashboard)/dashboard/admin/reports/page": "/dashboard/admin/reports", "/(dashboard)/dashboard/admin/states/page": "/dashboard/admin/states", "/(dashboard)/dashboard/admin/vehicles/[id]/page": "/dashboard/admin/vehicles/[id]", "/(dashboard)/dashboard/admin/verifications/page": "/dashboard/admin/verifications", "/(dashboard)/dashboard/admin/support/page": "/dashboard/admin/support", "/(dashboard)/dashboard/admin/vehicles/approvals/page": "/dashboard/admin/vehicles/approvals", "/(dashboard)/dashboard/admin/vehicles/page": "/dashboard/admin/vehicles", "/(dashboard)/dashboard/client/favorites/page": "/dashboard/client/favorites", "/(dashboard)/dashboard/client/history/page": "/dashboard/client/history", "/(dashboard)/dashboard/client/messages/page": "/dashboard/client/messages", "/(dashboard)/dashboard/client/page": "/dashboard/client", "/(dashboard)/dashboard/client/payments/page": "/dashboard/client/payments", "/(dashboard)/dashboard/client/reservations/[id]/page": "/dashboard/client/reservations/[id]", "/(dashboard)/dashboard/client/reservations/page": "/dashboard/client/reservations", "/(dashboard)/dashboard/client/search/page": "/dashboard/client/search", "/(dashboard)/dashboard/client/settings/page": "/dashboard/client/settings", "/(dashboard)/dashboard/client/verification/page": "/dashboard/client/verification", "/(dashboard)/dashboard/host/earnings/page": "/dashboard/host/earnings", "/(dashboard)/dashboard/host/messages/page": "/dashboard/host/messages", "/(dashboard)/dashboard/host/page": "/dashboard/host", "/(dashboard)/dashboard/host/reports/page": "/dashboard/host/reports", "/(dashboard)/dashboard/host/settings/page": "/dashboard/host/settings", "/(dashboard)/dashboard/host/reservations/page": "/dashboard/host/reservations", "/(dashboard)/dashboard/host/vehicles/[id]/calendar/page": "/dashboard/host/vehicles/[id]/calendar", "/(root)/booking/[id]/page": "/booking/[id]", "/(dashboard)/dashboard/host/vehicles/[id]/edit/page": "/dashboard/host/vehicles/[id]/edit", "/(dashboard)/dashboard/host/vehicles/[id]/page": "/dashboard/host/vehicles/[id]", "/(root)/page": "/", "/(dashboard)/dashboard/host/vehicles/page": "/dashboard/host/vehicles", "/(dashboard)/dashboard/host/vehicles/new/page": "/dashboard/host/vehicles/new", "/(dashboard)/dashboard/page": "/dashboard", "/(dashboard)/dashboard/host/verification/page": "/dashboard/host/verification", "/(root)/vehicles/[id]/page": "/vehicles/[id]", "/(root)/vehicles/page": "/vehicles"}