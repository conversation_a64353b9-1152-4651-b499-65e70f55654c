{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,0KAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,kMAAC,0KAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,0KAAA,CAAA,OAAqB,CAAC,WAAW"}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,kMAAC;QACC,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,kMAAC,wKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,kMAAC,wKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,kMAAC,wKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,kMAAC,wKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,kMAAC,0KAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,kMAAC,0KAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,kMAAC,0KAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,kMAAC,0KAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,kMAAC,0KAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,kMAAC,gOAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,kMAAC,0KAAA,CAAA,SAAsB;kBACrB,cAAA,kMAAC,0KAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,kMAAC;;;;;8BACD,kMAAC,0KAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,kMAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,kMAAC,0KAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,kMAAC,0KAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,kMAAC;gBAAK,WAAU;0BACd,cAAA,kMAAC,0KAAA,CAAA,gBAA6B;8BAC5B,cAAA,kMAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,kMAAC,0KAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,kMAAC,0KAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,kMAAC,0KAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,kMAAC,4NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,kMAAC,0KAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,kMAAC,gOAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC"}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,kMAAC,6KAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,6KAAA,CAAA,OAAuB,CAAC,WAAW"}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/settings/additional-role-request.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { User, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle } from \"lucide-react\"\nimport { useUser } from '@/context/user-context'\nimport { userRolesApi } from '@/lib/api/user-roles.api'\nimport toast from 'react-hot-toast'\n\nexport function AdditionalRoleRequest() {\n  const { user } = useUser()\n  const [isLoading, setIsLoading] = useState(false)\n\n  // Determinar qué rol puede solicitar\n  const getAvailableRoleToRequest = () => {\n    if (!user.availableUserTypes) return null\n\n    const hasClient = user.availableUserTypes.includes('client')\n    const hasHost = user.availableUserTypes.includes('host')\n\n    if (!hasClient && user.userType !== 'client') return 'client'\n    if (!hasHost && user.userType !== 'host') return 'host'\n\n    return null // Ya tiene ambos roles\n  }\n\n  const roleToRequest = getAvailableRoleToRequest()\n\n  // Si ya tiene ambos roles, no mostrar nada\n  if (!roleToRequest) {\n    return null\n  }\n\n  const handleRequestRole = async () => {\n    if (!roleToRequest) return\n\n    setIsLoading(true)\n    try {\n      const response = await userRolesApi.requestAdditionalRole(roleToRequest as 'client' | 'host')\n\n      // Actualizar el contexto del usuario\n\n      toast.success(response.message)\n\n    } catch (error) {\n      console.error('Error requesting additional role:', error)\n      toast.error('Error al solicitar el rol adicional')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getRoleInfo = (role: string) => {\n    if (role === 'host') {\n      return {\n        title: 'Convertirse en Anfitrión',\n        description: 'Renta tus vehículos y genera ingresos adicionales',\n        icon: UserCheck,\n        benefits: [\n          'Publica tus vehículos en la plataforma',\n          'Genera ingresos pasivos',\n          'Controla tu disponibilidad',\n          'Accede a herramientas de gestión'\n        ]\n      }\n    } else {\n      return {\n        title: 'Convertirse en Cliente',\n        description: 'Renta vehículos de otros usuarios',\n        icon: User,\n        benefits: [\n          'Accede a miles de vehículos',\n          'Reserva por horas o días',\n          'Encuentra vehículos cerca de ti',\n          'Califica tu experiencia'\n        ]\n      }\n    }\n  }\n\n  const roleInfo = getRoleInfo(roleToRequest)\n  const RoleIcon = roleInfo.icon\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center gap-3\">\n          <RoleIcon className=\"h-6 w-6 text-primary\" />\n          <div>\n            <CardTitle>{roleInfo.title}</CardTitle>\n            <CardDescription>{roleInfo.description}</CardDescription>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        <div className=\"space-y-2\">\n          <h4 className=\"text-sm font-medium\">Beneficios:</h4>\n          <ul className=\"space-y-1\">\n            {roleInfo.benefits.map((benefit, index) => (\n              <li key={index} className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                {benefit}\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        <div className=\"flex items-center justify-between pt-4 border-t\">\n          <div className=\"space-y-1\">\n            <p className=\"text-sm font-medium\">Rol actual:</p>\n            <Badge variant=\"outline\" className=\"capitalize\">\n              {user.userType === 'host' ? 'Anfitrión' : 'Cliente'}\n            </Badge>\n          </div>\n\n          <Button\n            onClick={handleRequestRole}\n            disabled={isLoading}\n            className=\"min-w-[120px]\"\n          >\n            {isLoading ? 'Solicitando...' : `Ser ${roleToRequest === 'host' ? 'Anfitrión' : 'Cliente'}`}\n          </Button>\n        </div>\n\n        <div className=\"text-xs text-muted-foreground bg-muted/50 p-3 rounded-md\">\n          <strong>Nota:</strong> Al solicitar este rol adicional, podrás alternar entre ambos modos desde tu perfil.\n          {roleToRequest === 'host' && ' Para acceder a todas las funciones de anfitrión, deberás completar el proceso de verificación.'}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qCAAqC;IACrC,MAAM,4BAA4B;QAChC,IAAI,CAAC,KAAK,kBAAkB,EAAE,OAAO;QAErC,MAAM,YAAY,KAAK,kBAAkB,CAAC,QAAQ,CAAC;QACnD,MAAM,UAAU,KAAK,kBAAkB,CAAC,QAAQ,CAAC;QAEjD,IAAI,CAAC,aAAa,KAAK,QAAQ,KAAK,UAAU,OAAO;QACrD,IAAI,CAAC,WAAW,KAAK,QAAQ,KAAK,QAAQ,OAAO;QAEjD,OAAO,KAAK,uBAAuB;;IACrC;IAEA,MAAM,gBAAgB;IAEtB,2CAA2C;IAC3C,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,eAAe;QAEpB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,iJAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC;YAE1D,qCAAqC;YAErC,+JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS,OAAO;QAEhC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,+JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,QAAQ;YACnB,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,MAAM,wNAAA,CAAA,YAAS;gBACf,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;QACF,OAAO;YACL,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,MAAM,0MAAA,CAAA,OAAI;gBACV,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;QACF;IACF;IAEA,MAAM,WAAW,YAAY;IAC7B,MAAM,WAAW,SAAS,IAAI;IAE9B,qBACE,kMAAC,wIAAA,CAAA,OAAI;;0BACH,kMAAC,wIAAA,CAAA,aAAU;0BACT,cAAA,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAS,WAAU;;;;;;sCACpB,kMAAC;;8CACC,kMAAC,wIAAA,CAAA,YAAS;8CAAE,SAAS,KAAK;;;;;;8CAC1B,kMAAC,wIAAA,CAAA,kBAAe;8CAAE,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAI5C,kMAAC,wIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,kMAAC;gCAAG,WAAU;0CACX,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,kMAAC;wCAAe,WAAU;;0DACxB,kMAAC,mOAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB;;uCAFM;;;;;;;;;;;;;;;;kCAQf,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,kMAAC,yIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAChC,KAAK,QAAQ,KAAK,SAAS,cAAc;;;;;;;;;;;;0CAI9C,kMAAC,0IAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,YAAY,mBAAmB,CAAC,IAAI,EAAE,kBAAkB,SAAS,cAAc,WAAW;;;;;;;;;;;;kCAI/F,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;0CAAO;;;;;;4BAAc;4BACrB,kBAAkB,UAAU;;;;;;;;;;;;;;;;;;;AAKvC"}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/host/settings/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { Switch } from \"@/components/ui/switch\"\r\nimport { Textarea } from \"@/components/ui/textarea\"\r\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport { AdditionalRoleRequest } from \"@/components/settings/additional-role-request\"\r\n\r\nexport default function HostSettingsPage() {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h1 className=\"text-3xl font-bold tracking-tight\">Configuración</h1>\r\n        <p className=\"text-muted-foreground\">Gestiona tu perfil y preferencias de anfitrión</p>\r\n      </div>\r\n\r\n      <Tabs defaultValue=\"profile\" className=\"space-y-4\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"profile\">Perfil</TabsTrigger>\r\n          <TabsTrigger value=\"notifications\">Notificaciones</TabsTrigger>\r\n          <TabsTrigger value=\"pricing\">Precios</TabsTrigger>\r\n          <TabsTrigger value=\"policies\">Políticas</TabsTrigger>\r\n          <TabsTrigger value=\"security\">Seguridad</TabsTrigger>\r\n        </TabsList>\r\n\r\n        <TabsContent value=\"profile\" className=\"space-y-4\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Información Personal</CardTitle>\r\n              <CardDescription>Actualiza tu información de perfil</CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"firstName\">Nombre</Label>\r\n                  <Input id=\"firstName\" defaultValue=\"Carlos\" />\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"lastName\">Apellido</Label>\r\n                  <Input id=\"lastName\" defaultValue=\"Rodriguez\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"email\">Email</Label>\r\n                <Input id=\"email\" type=\"email\" defaultValue=\"<EMAIL>\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"phone\">Teléfono</Label>\r\n                <Input id=\"phone\" defaultValue=\"+****************\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"bio\">Biografía</Label>\r\n                <Textarea\r\n                  id=\"bio\"\r\n                  placeholder=\"Cuéntanos sobre ti como anfitrión...\"\r\n                  defaultValue=\"Anfitrión experimentado con más de 3 años en la plataforma. Me encanta ayudar a los viajeros a tener la mejor experiencia posible.\"\r\n                />\r\n              </div>\r\n              <Button>Guardar Cambios</Button>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Verificaciones</CardTitle>\r\n              <CardDescription>Estado de tus verificaciones</CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">Identidad</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Documento de identidad verificado</p>\r\n                </div>\r\n                <Badge variant=\"default\">Verificado</Badge>\r\n              </div>\r\n              <Separator />\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">Licencia de Conducir</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Licencia válida verificada</p>\r\n                </div>\r\n                <Badge variant=\"default\">Verificado</Badge>\r\n              </div>\r\n              <Separator />\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">Información Bancaria</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Cuenta para recibir pagos</p>\r\n                </div>\r\n                <Badge variant=\"secondary\">Pendiente</Badge>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Componente para solicitar rol adicional */}\r\n          <AdditionalRoleRequest />\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"notifications\" className=\"space-y-4\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Preferencias de Notificación</CardTitle>\r\n              <CardDescription>Configura cómo quieres recibir notificaciones</CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-6\">\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <p className=\"font-medium\">Nuevas Reservas</p>\r\n                    <p className=\"text-sm text-muted-foreground\">\r\n                      Recibir notificación cuando alguien reserve un vehículo\r\n                    </p>\r\n                  </div>\r\n                  <Switch defaultChecked />\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <p className=\"font-medium\">Mensajes</p>\r\n                    <p className=\"text-sm text-muted-foreground\">Notificaciones de nuevos mensajes</p>\r\n                  </div>\r\n                  <Switch defaultChecked />\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <p className=\"font-medium\">Pagos</p>\r\n                    <p className=\"text-sm text-muted-foreground\">Confirmaciones de pagos recibidos</p>\r\n                  </div>\r\n                  <Switch defaultChecked />\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <p className=\"font-medium\">Recordatorios</p>\r\n                    <p className=\"text-sm text-muted-foreground\">Recordatorios de entrega y recogida</p>\r\n                  </div>\r\n                  <Switch defaultChecked />\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Canales de Notificación</CardTitle>\r\n              <CardDescription>Elige cómo recibir las notificaciones</CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">Email</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Notificaciones por correo electrónico</p>\r\n                </div>\r\n                <Switch defaultChecked />\r\n              </div>\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">SMS</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Mensajes de texto para notificaciones urgentes</p>\r\n                </div>\r\n                <Switch />\r\n              </div>\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">Push</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Notificaciones push en la aplicación</p>\r\n                </div>\r\n                <Switch defaultChecked />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"pricing\" className=\"space-y-4\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Configuración de Precios</CardTitle>\r\n              <CardDescription>Establece tus estrategias de precios</CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"basePrice\">Precio Base por Día</Label>\r\n                <Input id=\"basePrice\" type=\"number\" defaultValue=\"50\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"weeklyDiscount\">Descuento Semanal (%)</Label>\r\n                <Input id=\"weeklyDiscount\" type=\"number\" defaultValue=\"10\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"monthlyDiscount\">Descuento Mensual (%)</Label>\r\n                <Input id=\"monthlyDiscount\" type=\"number\" defaultValue=\"20\" />\r\n              </div>\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">Precios Dinámicos</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Ajustar precios automáticamente según la demanda</p>\r\n                </div>\r\n                <Switch />\r\n              </div>\r\n              <Button>Guardar Configuración</Button>\r\n            </CardContent>\r\n          </Card>\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"policies\" className=\"space-y-4\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Políticas de Renta</CardTitle>\r\n              <CardDescription>Define las reglas para tus vehículos</CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"minAge\">Edad Mínima del Conductor</Label>\r\n                <Select defaultValue=\"21\">\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"18\">18 años</SelectItem>\r\n                    <SelectItem value=\"21\">21 años</SelectItem>\r\n                    <SelectItem value=\"25\">25 años</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"cancellation\">Política de Cancelación</Label>\r\n                <Select defaultValue=\"flexible\">\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"flexible\">Flexible</SelectItem>\r\n                    <SelectItem value=\"moderate\">Moderada</SelectItem>\r\n                    <SelectItem value=\"strict\">Estricta</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"mileage\">Límite de Kilometraje (por día)</Label>\r\n                <Input id=\"mileage\" type=\"number\" defaultValue=\"200\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"rules\">Reglas Adicionales</Label>\r\n                <Textarea\r\n                  id=\"rules\"\r\n                  placeholder=\"Ej: No fumar, no mascotas, etc.\"\r\n                  defaultValue=\"- No fumar en el vehículo&#10;- No se permiten mascotas&#10;- Devolver con el mismo nivel de combustible\"\r\n                />\r\n              </div>\r\n              <Button>Actualizar Políticas</Button>\r\n            </CardContent>\r\n          </Card>\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"security\" className=\"space-y-4\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Seguridad de la Cuenta</CardTitle>\r\n              <CardDescription>Protege tu cuenta con estas configuraciones</CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"currentPassword\">Contraseña Actual</Label>\r\n                <Input id=\"currentPassword\" type=\"password\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"newPassword\">Nueva Contraseña</Label>\r\n                <Input id=\"newPassword\" type=\"password\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"confirmPassword\">Confirmar Nueva Contraseña</Label>\r\n                <Input id=\"confirmPassword\" type=\"password\" />\r\n              </div>\r\n              <Button>Cambiar Contraseña</Button>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Autenticación de Dos Factores</CardTitle>\r\n              <CardDescription>Agrega una capa extra de seguridad</CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">2FA por SMS</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Recibir códigos por mensaje de texto</p>\r\n                </div>\r\n                <Switch />\r\n              </div>\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">App Autenticadora</p>\r\n                  <p className=\"text-sm text-muted-foreground\">Usar Google Authenticator o similar</p>\r\n                </div>\r\n                <Switch />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAce,SAAS;IACtB,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;;kCACC,kMAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,kMAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAGvC,kMAAC,wIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAU,WAAU;;kCACrC,kMAAC,wIAAA,CAAA,WAAQ;;0CACP,kMAAC,wIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,kMAAC,wIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAgB;;;;;;0CACnC,kMAAC,wIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,kMAAC,wIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,kMAAC,wIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;;;;;;;kCAGhC,kMAAC,wIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;;0CACrC,kMAAC,wIAAA,CAAA,OAAI;;kDACH,kMAAC,wIAAA,CAAA,aAAU;;0DACT,kMAAC,wIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,kMAAC,wIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,kMAAC,wIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;wDAAI,WAAU;;0EACb,kMAAC,yIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;;0EAC3B,kMAAC,yIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAY,cAAa;;;;;;;;;;;;kEAErC,kMAAC;wDAAI,WAAU;;0EACb,kMAAC,yIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,kMAAC,yIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAW,cAAa;;;;;;;;;;;;;;;;;;0DAGtC,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,kMAAC,yIAAA,CAAA,QAAK;wDAAC,IAAG;wDAAQ,MAAK;wDAAQ,cAAa;;;;;;;;;;;;0DAE9C,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,kMAAC,yIAAA,CAAA,QAAK;wDAAC,IAAG;wDAAQ,cAAa;;;;;;;;;;;;0DAEjC,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAM;;;;;;kEACrB,kMAAC,4IAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,aAAY;wDACZ,cAAa;;;;;;;;;;;;0DAGjB,kMAAC,0IAAA,CAAA,SAAM;0DAAC;;;;;;;;;;;;;;;;;;0CAIZ,kMAAC,wIAAA,CAAA,OAAI;;kDACH,kMAAC,wIAAA,CAAA,aAAU;;0DACT,kMAAC,wIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,kMAAC,wIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,kMAAC,wIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;;0EACC,kMAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,kMAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;0DAE3B,kMAAC,6IAAA,CAAA,YAAS;;;;;0DACV,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;;0EACC,kMAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,kMAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;0DAE3B,kMAAC,6IAAA,CAAA,YAAS;;;;;0DACV,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;;0EACC,kMAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,kMAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,kMAAC,uKAAA,CAAA,wBAAqB;;;;;;;;;;;kCAGxB,kMAAC,wIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAgB,WAAU;;0CAC3C,kMAAC,wIAAA,CAAA,OAAI;;kDACH,kMAAC,wIAAA,CAAA,aAAU;;0DACT,kMAAC,wIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,kMAAC,wIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,kMAAC,wIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;;8EACC,kMAAC;oEAAE,WAAU;8EAAc;;;;;;8EAC3B,kMAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAI/C,kMAAC,0IAAA,CAAA,SAAM;4DAAC,cAAc;;;;;;;;;;;;8DAExB,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;;8EACC,kMAAC;oEAAE,WAAU;8EAAc;;;;;;8EAC3B,kMAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAE/C,kMAAC,0IAAA,CAAA,SAAM;4DAAC,cAAc;;;;;;;;;;;;8DAExB,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;;8EACC,kMAAC;oEAAE,WAAU;8EAAc;;;;;;8EAC3B,kMAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAE/C,kMAAC,0IAAA,CAAA,SAAM;4DAAC,cAAc;;;;;;;;;;;;8DAExB,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;;8EACC,kMAAC;oEAAE,WAAU;8EAAc;;;;;;8EAC3B,kMAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAE/C,kMAAC,0IAAA,CAAA,SAAM;4DAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,kMAAC,wIAAA,CAAA,OAAI;;kDACH,kMAAC,wIAAA,CAAA,aAAU;;0DACT,kMAAC,wIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,kMAAC,wIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,kMAAC,wIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;;0EACC,kMAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,kMAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,kMAAC,0IAAA,CAAA,SAAM;wDAAC,cAAc;;;;;;;;;;;;0DAExB,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;;0EACC,kMAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,kMAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,kMAAC,0IAAA,CAAA,SAAM;;;;;;;;;;;0DAET,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;;0EACC,kMAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,kMAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,kMAAC,0IAAA,CAAA,SAAM;wDAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,kMAAC,wIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,kMAAC,wIAAA,CAAA,OAAI;;8CACH,kMAAC,wIAAA,CAAA,aAAU;;sDACT,kMAAC,wIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,kMAAC,wIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,kMAAC,wIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,kMAAC;4CAAI,WAAU;;8DACb,kMAAC,yIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,kMAAC,yIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAY,MAAK;oDAAS,cAAa;;;;;;;;;;;;sDAEnD,kMAAC;4CAAI,WAAU;;8DACb,kMAAC,yIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,kMAAC,yIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAiB,MAAK;oDAAS,cAAa;;;;;;;;;;;;sDAExD,kMAAC;4CAAI,WAAU;;8DACb,kMAAC,yIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,kMAAC,yIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAkB,MAAK;oDAAS,cAAa;;;;;;;;;;;;sDAEzD,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;;sEACC,kMAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,kMAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAE/C,kMAAC,0IAAA,CAAA,SAAM;;;;;;;;;;;sDAET,kMAAC,0IAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;;;;;;kCAKd,kMAAC,wIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,kMAAC,wIAAA,CAAA,OAAI;;8CACH,kMAAC,wIAAA,CAAA,aAAU;;sDACT,kMAAC,wIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,kMAAC,wIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,kMAAC,wIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,kMAAC;4CAAI,WAAU;;8DACb,kMAAC,yIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,kMAAC,0IAAA,CAAA,SAAM;oDAAC,cAAa;;sEACnB,kMAAC,0IAAA,CAAA,gBAAa;sEACZ,cAAA,kMAAC,0IAAA,CAAA,cAAW;;;;;;;;;;sEAEd,kMAAC,0IAAA,CAAA,gBAAa;;8EACZ,kMAAC,0IAAA,CAAA,aAAU;oEAAC,OAAM;8EAAK;;;;;;8EACvB,kMAAC,0IAAA,CAAA,aAAU;oEAAC,OAAM;8EAAK;;;;;;8EACvB,kMAAC,0IAAA,CAAA,aAAU;oEAAC,OAAM;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAI7B,kMAAC;4CAAI,WAAU;;8DACb,kMAAC,yIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,kMAAC,0IAAA,CAAA,SAAM;oDAAC,cAAa;;sEACnB,kMAAC,0IAAA,CAAA,gBAAa;sEACZ,cAAA,kMAAC,0IAAA,CAAA,cAAW;;;;;;;;;;sEAEd,kMAAC,0IAAA,CAAA,gBAAa;;8EACZ,kMAAC,0IAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;8EAC7B,kMAAC,0IAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;8EAC7B,kMAAC,0IAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;sDAIjC,kMAAC;4CAAI,WAAU;;8DACb,kMAAC,yIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,kMAAC,yIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAU,MAAK;oDAAS,cAAa;;;;;;;;;;;;sDAEjD,kMAAC;4CAAI,WAAU;;8DACb,kMAAC,yIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,kMAAC,4IAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,aAAY;oDACZ,cAAa;;;;;;;;;;;;sDAGjB,kMAAC,0IAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;;;;;;kCAKd,kMAAC,wIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;0CACtC,kMAAC,wIAAA,CAAA,OAAI;;kDACH,kMAAC,wIAAA,CAAA,aAAU;;0DACT,kMAAC,wIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,kMAAC,wIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,kMAAC,wIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,kMAAC,yIAAA,CAAA,QAAK;wDAAC,IAAG;wDAAkB,MAAK;;;;;;;;;;;;0DAEnC,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,kMAAC,yIAAA,CAAA,QAAK;wDAAC,IAAG;wDAAc,MAAK;;;;;;;;;;;;0DAE/B,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,yIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,kMAAC,yIAAA,CAAA,QAAK;wDAAC,IAAG;wDAAkB,MAAK;;;;;;;;;;;;0DAEnC,kMAAC,0IAAA,CAAA,SAAM;0DAAC;;;;;;;;;;;;;;;;;;0CAIZ,kMAAC,wIAAA,CAAA,OAAI;;kDACH,kMAAC,wIAAA,CAAA,aAAU;;0DACT,kMAAC,wIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,kMAAC,wIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,kMAAC,wIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;;0EACC,kMAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,kMAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,kMAAC,0IAAA,CAAA,SAAM;;;;;;;;;;;0DAET,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;;0EACC,kMAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,kMAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,kMAAC,0IAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvB"}}]}