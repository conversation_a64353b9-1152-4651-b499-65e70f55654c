{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": ["cn", "Skeleton", "t0", "$", "_c", "$i", "Symbol", "for", "className", "props", "t1", "t2"], "mappings": ";;;;;AAAA,SAASA,EAAE,QAAQ,aAAa;;;;AAEhC,kBAAAE,EAAA;IAAA,MAAAC,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAD,EAAA,EAAA;QAAkB,CAAA,EAAAM,SAAA,EAAA,GAAAC,OAAA,GAAAP,EAGqB;QAAAC,CAAA,CAAA,EAAA,GAAAD,EAAA;QAAAC,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QAGtBE,EAAA,8HAAAV,KAAAA,AAAA,EAAG,mCAAmC,EAAEQ,SAAS,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAO,EAAA,EAAA;QAD/DC,EAAA,iBAAA,6LAAA,GAGE;YAFW,SAAkD,CAAlD,CAAAD,EAAkD;YAAA,GACzDD,KAAK,IACT;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,OAHFQ,EAGE;AAAA;KARNV", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/api/reservations.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\nimport { Vehicle } from './vehicles.api';\n\nexport interface ReservationData {\n  vehicleId: string;\n  startDate: string;\n  endDate: string;\n  totalPrice: number;\n  contactName?: string;\n  contactEmail?: string;\n  contactPhone?: string;\n}\n\nexport interface ReservationResponse {\n  id: string;\n  userId: string;\n  createdAt: Date;\n  updatedAt: Date;\n  vehicleId: string;\n  startDate: string;\n  endDate: string;\n  totalPrice: number;\n  status: string;\n  by: string;\n  reason: string | null;\n  contactName: string | null;\n  contactEmail: string | null;\n  contactPhone: string | null;\n  vehicle: Vehicle;\n}\n\nexport interface HostReservation extends ReservationResponse {\n  user: {\n    id: string;\n    name: string;\n    email: string;\n    image: string;\n  };\n}\n\nexport interface ReservationCreationResponse {\n  id: string;\n  userId: string;\n  createdAt: Date;\n  updatedAt: Date;\n  vehicleId: string;\n  startDate: Date;\n  endDate: Date;\n  totalPrice: number;\n  status: string;\n  by: string;\n  reason: string | null;\n  contactName: string | null;\n  contactEmail: string | null;\n  contactPhone: string | null;\n}\n\nexport interface ReservationsForAdminView extends HostReservation {\n  isPersonalReservation: boolean;\n}\n\nexport interface ReservationStats {\n  totalReservations: number;\n  activeReservations: number;\n  pendingReservations: number;\n  completedReservations: number;\n  cancelledReservations: number;\n  totalEarnings: number;\n}\n\nexport const reservationsApi = {\n\n  client: {\n  // Crear una nueva reserva\n    create: async (data: ReservationData) => {\n      const response = await apiService.post<ReservationCreationResponse>('/user/reservations', data);\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n\n    // Obtener reservas del usuario\n    getReservations: async () => {\n      const response = await apiService.get<ReservationResponse[]>('/user/reservations');\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n\n    // Obtener una reserva específica\n    getReservationById: async (id: string) => {\n      const response = await apiService.get<ReservationResponse>(`/user/reservations/${id}`);\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n\n    // Cancelar una reserva\n    cancelReservation: async (id: string) => {\n      const response = await apiService.patch<ReservationResponse>(`/user/reservations/${id}/cancel`);\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n  },\n\n\n\n  /* Group by host */\n  host: {\n    getReservations: async (params: { page: number; limit: number }) => {\n      const response = await apiService.get<HostReservation[]>('/host/reservations', { params });\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n    cancelReservation: async (id: string) => {\n      const response = await apiService.patch<ReservationResponse>(`/host/reservations/${id}/cancel`);\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n    getPersonalReservations: async () => {\n      const response = await apiService.get<ReservationResponse[]>('/host/reservations/personal');\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n    updateReservationStatus: async (id: string, status: string) => {\n      const response = await apiService.patch<ReservationResponse>(`/host/reservations/${id}/status`, { status });\n      return response.data;\n    },\n    getStats: async () => {\n      const response = await apiService.get<ReservationStats>('/host/reservations/stats');\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    }\n  },\n\n  admin: {\n\n    // Obtener todas las reservas para el administrador\n    getReservations: async () => {\n      const response = await apiService.get<ReservationsForAdminView[]>('/admin/reservations');\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n  }\n};\n\n\n"], "names": [], "mappings": ";;;AAAA;;AAsEO,MAAM,kBAAkB;IAE7B,QAAQ;QACR,0BAA0B;QACxB,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAA8B,sBAAsB;YAC1F,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QAEA,+BAA+B;QAC/B,iBAAiB;YACf,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAwB;YAC7D,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QAEA,iCAAiC;QACjC,oBAAoB,OAAO;YACzB,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAsB,CAAC,mBAAmB,EAAE,IAAI;YACrF,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QAEA,uBAAuB;QACvB,mBAAmB,OAAO;YACxB,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAsB,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC;YAC9F,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;IACF;IAIA,iBAAiB,GACjB,MAAM;QACJ,iBAAiB,OAAO;YACtB,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAoB,sBAAsB;gBAAE;YAAO;YACxF,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QACA,mBAAmB,OAAO;YACxB,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAsB,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC;YAC9F,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QACA,yBAAyB;YACvB,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAwB;YAC7D,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QACA,yBAAyB,OAAO,IAAY;YAC1C,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAsB,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE;YAAO;YACzG,OAAO,SAAS,IAAI;QACtB;QACA,UAAU;YACR,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAmB;YACxD,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;IACF;IAEA,OAAO;QAEL,mDAAmD;QACnD,iBAAiB;YACf,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAA6B;YAClE,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28dashboard%29/dashboard/client/reservations/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28dashboard%29/dashboard/client/reservations/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from \"react\"\r\nimport { useQuery } from \"@tanstack/react-query\"\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\"\r\nimport { Card, CardContent } from \"@/components/ui/card\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Calendar, Car, Clock, /* MapPin */ } from \"lucide-react\"\r\nimport { format } from \"date-fns\"\r\nimport { es } from \"date-fns/locale\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { reservationsApi } from \"@/lib/api/reservations.api\"\r\n\r\nexport default function ClientReservationsPage() {\r\n  const [activeTab, setActiveTab] = useState(\"active\")\r\n  \r\n  const { data: reservations, isLoading } = useQuery({\r\n    queryKey: ['client-reservations'],\r\n    queryFn: reservationsApi.client.getReservations,\r\n    staleTime: 60 * 1000, // 1 minuto\r\n  })\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    switch (status) {\r\n      case 'active':\r\n        return <Badge className=\"bg-green-500\">Activa</Badge>\r\n      case 'confirmed':\r\n        return <Badge className=\"bg-green-500\">Confirmada</Badge>\r\n      case 'pending':\r\n        return <Badge className=\"bg-blue-500\">Pendiente</Badge>\r\n      case 'upcoming':\r\n        return <Badge className=\"bg-blue-500\">Próxima</Badge>\r\n      case 'completed':\r\n        return <Badge className=\"bg-gray-500\">Completada</Badge>\r\n      case 'cancelled':\r\n        return <Badge className=\"bg-red-500\">Cancelada</Badge>\r\n      default:\r\n        return <Badge>{status}</Badge>\r\n    }\r\n  }\r\n\r\n  const filteredReservations = reservations?.filter(reservation => {\r\n    if (activeTab === \"active\") {\r\n      return ['active', 'confirmed', 'pending', 'upcoming'].includes(reservation.status.toLowerCase());\r\n    }\r\n    if (activeTab === \"completed\") return reservation.status.toLowerCase() === \"completed\";\r\n    if (activeTab === \"cancelled\") return reservation.status.toLowerCase() === \"cancelled\";\r\n    return true;\r\n  });\r\n\r\n  // Función para formatear fechas\r\n  const formatDate = (dateString: string | Date) => {\r\n    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n    return format(date, \"d 'de' MMMM, yyyy\", { locale: es });\r\n  }\r\n\r\n  // Función para obtener la imagen del vehículo\r\n  const getVehicleImage = (vehicle: any) => {\r\n    if (!vehicle) return \"/placeholder.svg?height=300&width=500\";\r\n    if (vehicle.images && vehicle.images.length > 0) return vehicle.images[0];\r\n    return \"/placeholder.svg?height=300&width=500\";\r\n  }\r\n\r\n  // Función para obtener el nombre del vehículo\r\n  const getVehicleName = (vehicle: any) => {\r\n    if (!vehicle) return \"Vehículo no disponible\";\r\n    return `${vehicle.make} ${vehicle.model} ${vehicle.year}`;\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h1 className=\"text-3xl font-bold tracking-tight\">Mis Reservaciones</h1>\r\n        <p className=\"text-muted-foreground\">Gestiona tus reservas de vehículos</p>\r\n      </div>\r\n\r\n      <Tabs defaultValue=\"active\" value={activeTab} onValueChange={setActiveTab}>\r\n        <TabsList>\r\n          <TabsTrigger value=\"active\">Activas y Próximas</TabsTrigger>\r\n          <TabsTrigger value=\"completed\">Completadas</TabsTrigger>\r\n          <TabsTrigger value=\"cancelled\">Canceladas</TabsTrigger>\r\n        </TabsList>\r\n        \r\n        <TabsContent value={activeTab} className=\"space-y-4\">\r\n          {isLoading ? (\r\n            // Esqueletos de carga\r\n            Array.from({ length: 3 }).map((_, i) => (\r\n              <Card key={i}>\r\n                <CardContent className=\"p-6\">\r\n                  <div className=\"flex flex-col md:flex-row gap-4\">\r\n                    <Skeleton className=\"h-40 w-full md:w-48 rounded-md\" />\r\n                    <div className=\"flex-1 space-y-2\">\r\n                      <Skeleton className=\"h-6 w-48\" />\r\n                      <Skeleton className=\"h-4 w-32\" />\r\n                      <Skeleton className=\"h-4 w-64\" />\r\n                      <Skeleton className=\"h-4 w-24\" />\r\n                      <div className=\"flex justify-end mt-4\">\r\n                        <Skeleton className=\"h-10 w-32\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ))\r\n          ) : !filteredReservations || filteredReservations.length === 0 ? (\r\n            <Card>\r\n              <CardContent className=\"p-6 text-center\">\r\n                <p className=\"text-muted-foreground py-4\">No tienes reservaciones {activeTab === \"active\" ? \"activas o próximas\" : activeTab === \"completed\" ? \"completadas\" : \"canceladas\"}</p>\r\n              </CardContent>\r\n            </Card>\r\n          ) : (\r\n            filteredReservations.map(reservation => (\r\n              <Card key={reservation.id}>\r\n                <CardContent className=\"p-6\">\r\n                  <div className=\"flex flex-col md:flex-row gap-4\">\r\n                    <div className=\"relative h-40 w-full md:w-48 rounded-md overflow-hidden\">\r\n                      <Image\r\n                        src={getVehicleImage(reservation.vehicle)}\r\n                        alt={getVehicleName(reservation.vehicle)}\r\n                        fill\r\n                        className=\"object-cover\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex justify-between items-start\">\r\n                        <div>\r\n                          <h3 className=\"text-lg font-bold\">{getVehicleName(reservation.vehicle)}</h3>\r\n                          {reservation.vehicle?.host && (\r\n                            <p className=\"text-sm text-muted-foreground\">\r\n                              Anfitrión: {reservation.vehicle.host.name}\r\n                            </p>\r\n                          )}\r\n                        </div>\r\n                        {getStatusBadge(reservation.status)}\r\n                      </div>\r\n                      \r\n                      <div className=\"mt-4 space-y-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <Calendar className=\"h-4 w-4 mr-2\" />\r\n                          <span>\r\n                            {formatDate(reservation.startDate)} - {formatDate(reservation.endDate)}\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"flex items-center\">\r\n                          <Clock className=\"h-4 w-4 mr-2\" />\r\n                          <span>\r\n                            {format(new Date(reservation.startDate), \"HH:mm\")} - {format(new Date(reservation.endDate), \"HH:mm\")}\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"flex items-center\">\r\n                          <Car className=\"h-4 w-4 mr-2\" />\r\n                          <span>Total: ${reservation.totalPrice}</span>\r\n                        </div>\r\n                      </div>\r\n                      \r\n                      <div className=\"flex justify-end mt-4\">\r\n                        <Link href={`/dashboard/client/reservations/${reservation.id}`}>\r\n                          <Button>Ver detalles</Button>\r\n                        </Link>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ))\r\n          )}\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>\r\n  )\r\n}\r\n"], "names": ["c", "_c", "useState", "useQuery", "Tabs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabsList", "TabsTrigger", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Calendar", "Car", "Clock", "format", "es", "Skeleton", "Badge", "Link", "Image", "reservationsApi", "ClientReservationsPage", "$", "$i", "Symbol", "for", "activeTab", "setActiveTab", "t0", "query<PERSON><PERSON>", "queryFn", "client", "getReservations", "staleTime", "data", "reservations", "isLoading", "getStatusBadge", "_temp", "T0", "T1", "t1", "t2", "t3", "t4", "t5", "t6", "t7", "t8", "t9", "filteredReservations", "filter", "reservation", "includes", "status", "toLowerCase", "formatDate", "_temp2", "getVehicleImage", "_temp3", "getVehicleName", "_temp4", "Array", "from", "length", "map", "_temp5", "reservation_0", "id", "vehicle", "host", "name", "startDate", "endDate", "Date", "totalPrice", "t10", "t11", "t12", "_", "i", "vehicle_0", "make", "model", "year", "images", "dateString", "date", "locale"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,IAAI,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,sBAAsB;AAC/E,SAASC,IAAI,EAAEC,WAAW,QAAQ,sBAAsB;AACxD,SAASC,MAAM,QAAQ,wBAAwB;;;AAC/C,SAASC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,CAAE,oBAAoB,cAAc;AACjE,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,KAAK,MAAM,YAAY;AAC9B,SAASC,eAAe,QAAQ,4BAA4B;;;AAd5D,YAAY;;;;;;;;;;;;;;;AAgBG;;IAAA,MAAAE,CAAA,mLAAArB,IAAAA,AAAA,EAAA;IAAA,IAAAqB,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACb,MAAA,CAAAI,SAAA,EAAAC,YAAA,CAAA,qKAAkCzB,WAAAA,AAAA,EAAS,QAAQ,CAAC;IAAA,IAAA0B,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEDG,EAAA,GAAA;YAAAC,QAAA,EAAA;gBACtC,qBAAqB;aAAA;YAAAC,OAAA,8IAAAV,kBAAA,CAAAW,MAAA,CAAAC,eAAA;YAAAC,SAAA,EAAA;QAAA;QAGjCX,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAJD,MAAA,EAAAY,IAAA,EAAAC,YAAA,EAAAC,SAAAA,EAAA,iMAA0CjC,AAAA,EAASyB,EAIlD,CAAC;IAEF,MAAAS,cAAA,GAAAC,KAAA;IAiBC,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAA3B,CAAA,CAAA,EAAA,KAAAI,SAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAc,SAAA,IAAAd,CAAA,CAAA,EAAA,KAAAa,YAAA,EAAA;QAED,MAAAe,oBAAA,GAA6Bf,YAAY,EAAAgB,MAAA,EAAAC,WAAA;YAAA,IACnC1B,SAAS,KAAK,QAAQ,EAAA;gBAAA,OACjB;oBAAC,QAAQ;oBAAE,WAAW;oBAAE,SAAS;oBAAE,UAAU;iBAAA,CAAA2B,QAAA,CAAWD,WAAW,CAAAE,MAAA,CAAAC,WAAA,CAAoB,CAAC,CAAC;YAAA;YAAA,IAE9F7B,SAAS,KAAK,WAAW,EAAA;gBAAA,OAAS0B,WAAW,CAAAE,MAAA,CAAAC,WAAA,CAAoB,CAAC,KAAK,WAAW;YAAA;YAAA,IAClF7B,SAAS,KAAK,WAAW,EAAA;gBAAA,OAAS0B,WAAW,CAAAE,MAAA,CAAAC,WAAA,CAAoB,CAAC,KAAK,WAAW;YAAA;YAAA,OAAA;QAAA;QAKxF,MAAAC,UAAA,GAAAC,MAAA;QAMA,MAAAC,eAAA,GAAAC,MAAA;QAOA,MAAAC,cAAA,GAAAC,MAAA;QAMiBb,EAAA,GAAA,WAAW;QAAA,IAAA1B,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YACxBwB,EAAA,iBAAA,6LAAA,GAGM;;kCAFJ,6LAAA,EAAwE;wBAA1D,SAAmC,EAAnC,mCAAmC;kCAAC,iBAAiB,EAAnE,EAAwE;;;;;;kCACxE,6LAAA,CAA2E;wBAA9D,SAAuB,EAAvB,uBAAuB;kCAAC,kCAAkC,EAAvE,CAA2E,CAC7E,EAHA,GAGM;;;;;;;;;;;;YAAA3B,CAAA,CAAA,GAAA,GAAA2B,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAA3B,CAAA,CAAA,GAAA;QAAA;QAELkB,EAAA,uIAAApC,OAAA;QAAkBwC,EAAA,GAAA,QAAQ;QAAQlB,EAAA,CAAAA,CAAA,CAAAA,SAAS;QAAiBC,EAAA,CAAAA,CAAA,CAAAA,YAAY;QAAA,IAAAL,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YACvEsB,EAAA,iBAAA,iUAAC,WAAQ;;kCACP,iUAAC,cAAW;wBAAO,KAAQ,EAAR,QAAQ;kCAAC,kBAAkB,EAA7C,WAAW;;;;;;kCACZ,iUAAC,cAAW;wBAAO,KAAW,EAAX,WAAW;kCAAC,WAAW,EAAzC,WAAW;;;;;;kCACZ,iUAAC,cAAW;wBAAO,KAAW,EAAX,WAAW;kCAAC,UAAU,EAAxC,WAAW,CACd,EAJC,QAAQ,CAIE;;;;;;;;;;;;YAAAzB,CAAA,CAAA,GAAA,GAAAyB,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAzB,CAAA,CAAA,GAAA;QAAA;QAEViB,EAAA,uIAAAlC,cAAA;QAAmBqB,EAAA,CAAAA,CAAA,CAAAA,SAAS;QAAYgB,EAAA,GAAA,WAAW;QACjDC,EAAA,GAAAP,SAAS,GAER0B,KAAA,CAAAC,IAAA,CAAA;YAAAC,MAAA,EAAA;QAAA,CAAwB,CAAC,CAAAC,GAAA,CAAAC,MAiBxB,CAAC,GACA,CAAChB,oBAAoB,IAAIA,oBAAoB,CAAAc,MAAA,KAAA,CAAa,iBAC5D,iUAAC,OAAI;oCACH,iUAAC,cAAW;gBAAW,SAAiB,EAAjB,iBAAiB;wCACtC,6LAAA,CAAgL;oBAAnK,SAA4B,EAA5B,4BAA4B;;wBAAC,wBAAyB;wBAAAtC,SAAS,KAAK,QAAQ,GAAG,uBAAoB,GAAGA,SAAS,KAAK,WAAW,GAAG,aAAa,GAAG,YAAY,CAAC,EAA5K,CAAgL,CAClL,EAFC,WAAW,CAGd,EAJC,IAAI,CAIE;;;;;;;;;;;;;;;;mBAEPwB,oBAAoB,CAAAe,GAAA,CAAAE,aAAA,kBAClB,6LAAC,2IAAI,CAAM,GAAc,CAAd;wCACT,iUAAC,cAAW;oBAAW,SAAK,EAAL,KAAK;4CAC1B,6LAAA,GA+CM;wBA/CS,SAAiC,EAAjC,iCAAiC;;0CAC9C,6LAAA,GAOM;gCAPS,SAAyD,EAAzD,yDAAyD;wDACtE,8TAAC,UAAK;oCACC,GAAoC,CAApC,CAAAT,eAAe,CAACN,aAAW,CAAAiB,OAAQ,CAAC;oCACpC,GAAmC,CAAnC,CAAAT,cAAc,CAACR,aAAW,CAAAiB,OAAQ,CAAC;oCACxC,IAAI,CAAJ,CAAA,KAAI;oCACM,SAAc,EAAd,cAAc,GAE5B,EAPA,GAOM;;;;;;;;;;;0CACN,6LAAA,GAqCM;gCArCS,SAAQ,EAAR,QAAQ;;kDACrB,6LAAA,GAUM;wCAVS,SAAkC,EAAlC,kCAAkC;;0DAC/C,6LAAA,GAOM;;kEANJ,6LAAA,EAA4E;wDAA9D,SAAmB,EAAnB,mBAAmB,CAAE;kEAAAT,cAAc,CAACR,aAAW,CAAAiB,OAAQ,CAAC,CAAC,EAAvE,EAA4E,CAC3E;;;;;;oDAAAjB,aAAW,CAAAiB,OAAA,EAAAC,IAAA,kBACV,6LAAA,CAEI;wDAFS,SAA+B,EAA/B,+BAA+B;;4DAAC,WAC/B;4DAAAlB,aAAW,CAAAiB,OAAA,CAAAC,IAAA,CAAAC,IAAA,CACzB,EAFA,CAEI,CACL,CACH,EAPA,GAOM,CACL;;;;;;;;;;;;;4CAAAlC,cAAc,CAACe,aAAW,CAAAE,MAAO,CAAC,CACrC,EAVA,GAUM;;;;;;;kDAEN,6LAAA,GAiBM;wCAjBS,SAAgB,EAAhB,gBAAgB;;0DAC7B,6LAAA,GAKM;gDALS,SAAmB,EAAnB,mBAAmB;;kEAChC,6LAAC,yNAAQ;wDAAW,SAAc,EAAd,cAAc;;;;;;kEAClC,6LAAA,IAEO,CADJ;;4DAAAE,UAAU,CAACJ,aAAW,CAAAoB,SAAU,CAAC;4DAAC,GAAI;4DAAAhB,UAAU,CAACJ,aAAW,CAAAqB,OAAQ,CAAC,CACxE,EAFA,IAEO,CACT,EALA,GAKM;;;;;;;;;;;;;0DACN,6LAAA,GAKM;gDALS,SAAmB,EAAnB,mBAAmB;;kEAChC,qYAAC,QAAK;wDAAW,SAAc,EAAd,cAAc;;;;;;kEAC/B,6LAAA,IAEO,CADJ;;yNAAA3D,SAAAA,AAAA,EAAA,IAAA4D,IAAA,CAAgBtB,aAAW,CAAAoB,SAAA,GAAa,OAAO,CAAC;4DAAC,GAAI;gEAAA1D,kKAAAA,AAAA,EAAA,IAAA4D,IAAA,CAAgBtB,aAAW,CAAAqB,OAAA,GAAW,OAAO,CAAC,CACtG,EAFA,IAEO,CACT,EALA,GAKM;;;;;;;;;;;;;0DACN,6LAAA,GAGM;gDAHS,SAAmB,EAAnB,mBAAmB;;kEAChC,iYAAC,MAAG;wDAAW,SAAc,EAAd,cAAc;;;;;;kEAC7B,6LAAA,IAA6C;;4DAAvC,QAAS;4DAAArB,aAAW,CAAAuB,UAAA,CAAY,EAAtC,IAA6C,CAC/C,EAHA,GAGM,CACR,EAjBA,GAiBM;;;;;;;;;;;;;;;;;;;kDAEN,6LAAA,GAIM;wCAJS,SAAuB,EAAvB,uBAAuB;gEACpC,6LAAC,0KAAI;4CAAO,IAAkD,CAAlD,CAAA,CAAA,+BAAA,EAAkCvB,aAAW,CAAAgB,EAAA,EAAK;oEAC5D,mUAAC,SAAM;0DAAC,YAAY,EAAnB,MAAM,CACT,EAFC,IAAI,CAGP,EAJA,GAIM,CACR,EArCA,GAqCM,CACR,EA/CA,GA+CM,CACR,EAjDC,WAAW,CAkDd,EAnDC,IAAI,CAoDN,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eArDchB,aAAW,CAAAgB,EAAA,CAAG;;;;;QAqD5B9C,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAc,SAAA;QAAAd,CAAA,CAAA,EAAA,GAAAa,YAAA;QAAAb,CAAA,CAAA,EAAA,GAAAiB,EAAA;QAAAjB,CAAA,CAAA,EAAA,GAAAkB,EAAA;QAAAlB,CAAA,CAAA,EAAA,GAAAmB,EAAA;QAAAnB,CAAA,CAAA,EAAA,GAAAoB,EAAA;QAAApB,CAAA,CAAA,EAAA,GAAAqB,EAAA;QAAArB,CAAA,CAAA,GAAA,GAAAsB,EAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAuB,EAAA;QAAAvB,CAAA,CAAA,GAAA,GAAAwB,EAAA;QAAAxB,CAAA,CAAA,GAAA,GAAAyB,EAAA;QAAAzB,CAAA,CAAA,GAAA,GAAA0B,EAAA;QAAA1B,CAAA,CAAA,GAAA,GAAA2B,EAAA;IAAA,OAAA;QAAAV,EAAA,GAAAjB,CAAA,CAAA,EAAA;QAAAkB,EAAA,GAAAlB,CAAA,CAAA,EAAA;QAAAmB,EAAA,GAAAnB,CAAA,CAAA,EAAA;QAAAoB,EAAA,GAAApB,CAAA,CAAA,EAAA;QAAAqB,EAAA,GAAArB,CAAA,CAAA,EAAA;QAAAsB,EAAA,GAAAtB,CAAA,CAAA,GAAA;QAAAuB,EAAA,GAAAvB,CAAA,CAAA,GAAA;QAAAwB,EAAA,GAAAxB,CAAA,CAAA,GAAA;QAAAyB,EAAA,GAAAzB,CAAA,CAAA,GAAA;QAAA0B,EAAA,GAAA1B,CAAA,CAAA,GAAA;QAAA2B,EAAA,GAAA3B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsD,GAAA;IAAA,IAAAtD,CAAA,CAAA,GAAA,KAAAiB,EAAA,IAAAjB,CAAA,CAAA,GAAA,KAAAmB,EAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAoB,EAAA,IAAApB,CAAA,CAAA,GAAA,KAAAqB,EAAA,EAAA;QAlFHiC,GAAA,iBAAA,6LAAC,EAAW;YAAQlD,KAAS,CAATA,CAAAA,EAAS;YAAY,SAAW,CAAX,CAAAgB,EAAW,EACjD;sBAAAC,EAiFA,CACH,EAnFC,EAAW,CAmFE;;;;;;QAAArB,CAAA,CAAA,GAAA,GAAAiB,EAAA;QAAAjB,CAAA,CAAA,GAAA,GAAAmB,EAAA;QAAAnB,CAAA,CAAA,GAAA,GAAAoB,EAAA;QAAApB,CAAA,CAAA,GAAA,GAAAqB,EAAA;QAAArB,CAAA,CAAA,GAAA,GAAAsD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAtD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAuD,GAAA;IAAA,IAAAvD,CAAA,CAAA,GAAA,KAAAkB,EAAA,IAAAlB,CAAA,CAAA,GAAA,KAAAsD,GAAA,IAAAtD,CAAA,CAAA,GAAA,KAAAsB,EAAA,IAAAtB,CAAA,CAAA,GAAA,KAAAuB,EAAA,IAAAvB,CAAA,CAAA,GAAA,KAAAwB,EAAA,IAAAxB,CAAA,CAAA,GAAA,KAAAyB,EAAA,EAAA;QA1FhB8B,GAAA,iBAAA,6LAAC,EAAI;YAAc,YAAQ,CAAR,CAAAjC,EAAQ;YAAQlB,KAAS,CAATA,CAAAA,EAAS;YAAiBC,aAAY,CAAZA,CAAAA,EAAY,EACvE;;gBAAAoB,EAIW,CAEX;gBAAA6B,GAmFc,CAChB,EA3FC,EAAI,CA2FE;;;;;;;QAAAtD,CAAA,CAAA,GAAA,GAAAkB,EAAA;QAAAlB,CAAA,CAAA,GAAA,GAAAsD,GAAA;QAAAtD,CAAA,CAAA,GAAA,GAAAsB,EAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAuB,EAAA;QAAAvB,CAAA,CAAA,GAAA,GAAAwB,EAAA;QAAAxB,CAAA,CAAA,GAAA,GAAAyB,EAAA;QAAAzB,CAAA,CAAA,GAAA,GAAAuD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAvD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwD,GAAA;IAAA,IAAAxD,CAAA,CAAA,GAAA,KAAAuD,GAAA,IAAAvD,CAAA,CAAA,GAAA,KAAA0B,EAAA,IAAA1B,CAAA,CAAA,GAAA,KAAA2B,EAAA,EAAA;QAjGT6B,GAAA,iBAAA,6LAAA,GAkGM;YAlGS,SAAW,CAAX,CAAA9B,EAAW,EACxB;;gBAAAC,EAGM,CAEN;gBAAA4B,GA2FO,CACT,EAlGA,GAkGM;;;;;;;QAAAvD,CAAA,CAAA,GAAA,GAAAuD,GAAA;QAAAvD,CAAA,CAAA,GAAA,GAAA0B,EAAA;QAAA1B,CAAA,CAAA,GAAA,GAAA2B,EAAA;QAAA3B,CAAA,CAAA,GAAA,GAAAwD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAxD,CAAA,CAAA,GAAA;IAAA;IAAA,OAlGNwD,GAkGM;AAAA;;;uLAxJkC3E;;;KAH7BkB,uBAAA;AAAA,SAAA6C,OAAAa,CAAA,EAAAC,CAAA;IAAA,qBA0ED,iUAAC,OAAI,CAAMA,GAAC,CAADA;gCACT,iUAAC,cAAW;YAAW,SAAK,EAAL,KAAK;sBAC1B,2MAAA,GAWM;gBAXS,SAAiC,EAAjC,iCAAiC;;kCAC9C,qUAAC,WAAQ;wBAAW,SAAgC,EAAhC,gCAAgC;;;;;;kCACpD,6LAAA,GAQM;wBARS,SAAkB,EAAlB,kBAAkB;;0CAC/B,qUAAC,WAAQ;gCAAW,SAAU,EAAV,UAAU;;;;;;0CAC9B,qUAAC,WAAQ;gCAAW,SAAU,EAAV,UAAU;;;;;;0CAC9B,qUAAC,WAAQ;gCAAW,SAAU,EAAV,UAAU;;;;;;0CAC9B,qUAAC,WAAQ;gCAAW,SAAU,EAAV,UAAU;;;;;;0CAC9B,6LAAA,GAEM;gCAFS,SAAuB,EAAvB,uBAAuB;wDACpC,qUAAC,WAAQ;oCAAW,SAAW,EAAX,WAAW,GACjC,EAFA,GAEM,CACR,EARA,GAQM,CACR,EAXA,GAWM,CACR,EAbC,WAAW,CAcd,EAfC,IAAI,CAeE;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAfIA,CAAC;;;;;AAeL;AAzFN,SAAAnB,OAAAoB,SAAA;IAAA,IAAA,CAoDNZ,SAAO,EAAA;QAAA,OAAS,2BAAwB;IAAA;IAAA,OACtC,GAAGA,SAAO,CAAAa,IAAA,CAAA,CAAA,EAASb,SAAO,CAAAc,KAAA,CAAA,CAAA,EAAUd,SAAO,CAAAe,IAAA,EAAO;AAAA;AArD9C,SAAAzB,OAAAU,OAAA;IAAA,IAAA,CA6CNA,OAAO,EAAA;QAAA,OAAS,uCAAuC;IAAA;IAAA,IACxDA,OAAO,CAAAgB,MAAA,IAAWhB,OAAO,CAAAgB,MAAA,CAAArB,MAAA,GAAA,CAAkB,EAAA;QAAA,OAASK,OAAO,CAAAgB,MAAA,CAAA,EAAA;IAAA;IAAA,OACxD,uCAAuC;AAAA;AA/CnC,SAAA5B,OAAA6B,UAAA;IAuCX,MAAAC,IAAA,GAAa,OAAOD,UAAU,KAAK,QAAQ,GAAA,IAAAZ,IAAA,CAAYY,UAAU,IAAIA,UAAU;IAAC,oKACzExE,SAAAA,AAAA,EAAOyE,IAAI,EAAE,mBAAmB,EAAA;QAAAC,MAAA,iJAAAzE,KAAAA;IAAA,CAAgB,CAAC;AAAA;AAxC7C,SAAAuB,MAAAgB,MAAA;IAAA,OAUHA,MAAM;QAAA,KACP,QAAQ;YAAA;gBAAA,qBACJ,kUAAC,QAAK;oBAAW,SAAc,EAAd,cAAc;8BAAC,MAAM,EAArC,KAAK,CAAwC;;;;;;YAAA;QAAA,KAClD,WAAW;YAAA;gBAAA,qBACP,6LAAC,6IAAK;oBAAW,SAAc,EAAd,cAAc;8BAAC,UAAU,EAAzC,KAAK,CAA4C;;;;;;YAAA;QAAA,KACtD,SAAS;YAAA;gBAAA,qBACL,kUAAC,QAAK;oBAAW,SAAa,EAAb,aAAa;8BAAC,SAAS,EAAvC,KAAK,CAA0C;;;;;;YAAA;QAAA,KACpD,UAAU;YAAA;gBAAA,qBACN,iUAAC,SAAK;oBAAW,SAAa,EAAb,aAAa;8BAAC,OAAO,EAArC,KAAK,CAAwC;;;;;;YAAA;QAAA,KAClD,WAAW;YAAA;gBAAA,qBACP,kUAAC,QAAK;oBAAW,SAAa,EAAb,aAAa;8BAAC,UAAU,EAAxC,KAAK,CAA2C;;;;;;YAAA;QAAA,KACrD,WAAW;YAAA;gBAAA,qBACP,kUAAC,QAAK;oBAAW,SAAY,EAAZ,YAAY;8BAAC,SAAS,EAAtC,KAAK,CAAyC;;;;;;YAAA;QAAA;YAAA;gBAAA,qBAE/C,kUAAC,QAAK,CAAEA;8BAAAA,MAAM,CAAC,EAAd,KAAK,CAAiB;;;;;;YAAA;IAAA;AAAA", "debugId": null}}]}