{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/env.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const envSchema = z.object({\r\n  NEXT_PUBLIC_API_URL: z.string().url(),\r\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\r\n  IS_DEV: z.string().optional().transform((val) => val === 'true'),\r\n});\r\n\r\nexport type Env = z.infer<typeof envSchema>;\r\n\r\n// create the same object but with process.env\r\nconst env = envSchema.parse({\r\n  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n  NODE_ENV: process.env.NODE_ENV,\r\n  IS_DEV: process.env.IS_DEV,\r\n});\r\n\r\nexport default env;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,YAAY,4IAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,qBAAqB,4IAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IACnC,UAAU,4IAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAChE,QAAQ,4IAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,MAAQ,QAAQ;AAC3D;AAIA,8CAA8C;AAC9C,MAAM,MAAM,UAAU,KAAK,CAAC;IAC1B,mBAAmB;IACnB,QAAQ;IACR,QAAQ,QAAQ,GAAG,CAAC,MAAM;AAC5B;uCAEe"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/log-requests.ts"], "sourcesContent": ["'use server';\r\nconst LOGFLARE_API_URL = 'https://api.logflare.app/logs';\r\nconst LOGFLARE_API_KEY = process.env.LOGFLARE_API_KEY!;\r\nconst LOGFLARE_SOURCE_ID = process.env.LOGFLARE_SOURCE_ID!;\r\n\r\nconst IS_PAGES = process.env.PAGES === 'true';\r\n\r\n// Función para enviar logs a Logflare\r\nexport const sendLogToLogflare = async (logEntry: Record<string, any>) => {\r\n\r\n  if (!IS_PAGES) return;\r\n  const logflareEventBody = {\r\n    event_message: logEntry.message,\r\n    metadata: {\r\n      ...logEntry.metadata,\r\n    },\r\n  };\r\n\r\n  // console.log('Logflare Event Body:', logflareEventBody);\r\n\r\n  const init = {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"X-API-KEY\": LOGFLARE_API_KEY,\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      // source: sourceKey,\r\n      log_entry: logflareEventBody.event_message,\r\n      metadata: logflareEventBody.metadata,\r\n    }),\r\n  };\r\n\r\n  await fetch(`${LOGFLARE_API_URL}?source=${LOGFLARE_SOURCE_ID}`, init);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AACA,MAAM,mBAAmB;AACzB,MAAM,mBAAmB,QAAQ,GAAG,CAAC,gBAAgB;AACrD,MAAM,qBAAqB,QAAQ,GAAG,CAAC,kBAAkB;AAEzD,MAAM,WAAW,QAAQ,GAAG,CAAC,KAAK,KAAK;AAGhC,MAAM,oBAAoB,OAAO;IAEtC,IAAI,CAAC,UAAU;IACf,MAAM,oBAAoB;QACxB,eAAe,SAAS,OAAO;QAC/B,UAAU;YACR,GAAG,SAAS,QAAQ;QACtB;IACF;IAEA,0DAA0D;IAE1D,MAAM,OAAO;QACX,QAAQ;QACR,SAAS;YACP,aAAa;YACb,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,qBAAqB;YACrB,WAAW,kBAAkB,aAAa;YAC1C,UAAU,kBAAkB,QAAQ;QACtC;IACF;IAEA,MAAM,MAAM,GAAG,iBAAiB,QAAQ,EAAE,oBAAoB,EAAE;AAClE;;;IA1Ba;;AAAA,0OAAA"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/cookies.ts"], "sourcesContent": ["'use server';\nimport { cookies } from \"next/headers\";\nimport { getCookie, setCookie, deleteCookie } from \"cookies-next/server\";\n\n\nexport const getCookieServerByName = async ({\n  name,\n}: {\n  name: string;\n}) => {\n  const cookie = getCookie(name, { cookies });\n  return cookie;\n};\n\nexport const setCookieServerByName = async ({\n  name,\n  value\n}: {\n  name: string;\n  value: string;\n}) => {\n  console.log('setCookieServerByName', name, value);\n  await setCookie(name, value, { cookies });\n};\n\nexport const deleteCookieServerByName = async ({\n  name\n}: {\n  name: string;\n}) => {\n  await deleteCookie(name, { cookies });\n};"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AACA;;;;;;AAGO,MAAM,wBAAwB,OAAO,EAC1C,IAAI,EAGL;IACC,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,MAAM;QAAE,SAAA,2KAAA,CAAA,UAAO;IAAC;IACzC,OAAO;AACT;AAEO,MAAM,wBAAwB,OAAO,EAC1C,IAAI,EACJ,KAAK,EAIN;IACC,QAAQ,GAAG,CAAC,yBAAyB,MAAM;IAC3C,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO;QAAE,SAAA,2KAAA,CAAA,UAAO;IAAC;AACzC;AAEO,MAAM,2BAA2B,OAAO,EAC7C,IAAI,EAGL;IACC,MAAM,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE,MAAM;QAAE,SAAA,2KAAA,CAAA,UAAO;IAAC;AACrC;;;IA1Ba;IASA;IAWA;;AApBA,0OAAA;AASA,0OAAA;AAWA,0OAAA"}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/index.ts"], "sourcesContent": ["export const BEARER_COOKIE_NAME = \"bearer_token\";\r\nexport const PENDING_INVITATION_COOKIE = 'pending_invitation';\r\nexport const PENDING_INVITATION_EMAIL_COOKIE = 'pending_invitation_email';\r\nexport const PENDING_INVITATION_ORG_ID_COOKIE = 'pending_invitation_org_id';"], "names": [], "mappings": ";;;;;;AAAO,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAClC,MAAM,kCAAkC;AACxC,MAAM,mCAAmC"}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/getSession.ts"], "sourcesContent": ["'use server';\r\nimport { cookies } from \"next/headers\";\r\nimport { headers } from \"next/headers\";\r\nimport { cache } from \"react\";\r\nimport { betterFetch } from \"@better-fetch/fetch\";\r\nimport env from \"@/constants/env\";\r\nimport { sendLogToLogflare } from \"@/lib/log-requests\";\r\nimport { getCookieServerByName } from './cookies';\r\nimport { BEARER_COOKIE_NAME } from '@/constants';\r\nimport { Session } from 'better-auth/types';\r\nimport { redirect } from 'next/navigation';\r\n\r\ntype Config = {\r\n  // redirect?: boolean;\r\n  shouldRedirect?: boolean;\r\n}\r\n\r\nexport const getServerSession = cache(async (name?: string, { shouldRedirect = true }: Config = {}) => {\r\n  \r\n  const headersList = await headers();\r\n  const sessionHeader = headersList.get('session');\r\n\r\n  if (sessionHeader) {\r\n    const session = JSON.parse(sessionHeader);\r\n    if (session && session.user){\r\n      return session as { user: User, session: Session };\r\n    }\r\n  }\r\n  \r\n  const cookiesStore = await cookies();\r\n\r\n  const allCookies = cookiesStore.toString();\r\n  try {\r\n\r\n    const token = await getCookieServerByName({\r\n      name: BEARER_COOKIE_NAME,\r\n    });\r\n\r\n\r\n    const { data: session } = await betterFetch<{ user: User, session: Session }>(\r\n      \"/api/auth/get-session\",\r\n      {\r\n        baseURL: env.NEXT_PUBLIC_API_URL,\r\n        headers: {\r\n          cookie: allCookies,\r\n          'x-function-call': `${name}`\r\n        },\r\n        auth: {\r\n          type: 'Bearer',\r\n          token: () => {\r\n            if (token) {\r\n              return token; // No truncar el token\r\n            }\r\n          }\r\n        }\r\n      },\r\n    );\r\n    console.log('Session on [getServerSession]: ', session);\r\n\r\n    if (token && !session) {\r\n      // remove token from cookie\r\n      cookiesStore.delete(BEARER_COOKIE_NAME);\r\n    }\r\n\r\n    // log session using sendLogToLogflare\r\n    sendLogToLogflare({\r\n      message: 'Session on [getServerSession]',\r\n      metadata: {\r\n        session: session\r\n      }\r\n    });\r\n\r\n    // if (!session) return redirect('/sign-in');\r\n    if (!session && shouldRedirect) return redirect('/sign-in');\r\n\r\n\r\n  return session as NonNullable<typeof session>;\r\n} catch (error) {\r\n  console.error(`Error getting session on [${name}]`, error);\r\n    // return null;\r\n    throw error;\r\n}\r\n});"], "names": [], "mappings": ";;;;;;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;;;;;;;;;;;;;AAOO,MAAM,mBAAmB,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE,OAAO,MAAe,EAAE,iBAAiB,IAAI,EAAU,GAAG,CAAC,CAAC;IAEhG,MAAM,cAAc,MAAM,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAChC,MAAM,gBAAgB,YAAY,GAAG,CAAC;IAEtC,IAAI,eAAe;QACjB,MAAM,UAAU,KAAK,KAAK,CAAC;QAC3B,IAAI,WAAW,QAAQ,IAAI,EAAC;YAC1B,OAAO;QACT;IACF;IAEA,MAAM,eAAe,MAAM,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,aAAa,aAAa,QAAQ;IACxC,IAAI;QAEF,MAAM,QAAQ,MAAM,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE;YACxC,MAAM,iIAAA,CAAA,qBAAkB;QAC1B;QAGA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EACxC,yBACA;YACE,SAAS,+HAAA,CAAA,UAAG,CAAC,mBAAmB;YAChC,SAAS;gBACP,QAAQ;gBACR,mBAAmB,GAAG,MAAM;YAC9B;YACA,MAAM;gBACJ,MAAM;gBACN,OAAO;oBACL,IAAI,OAAO;wBACT,OAAO,OAAO,sBAAsB;oBACtC;gBACF;YACF;QACF;QAEF,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,IAAI,SAAS,CAAC,SAAS;YACrB,2BAA2B;YAC3B,aAAa,MAAM,CAAC,iIAAA,CAAA,qBAAkB;QACxC;QAEA,sCAAsC;QACtC,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD,EAAE;YAChB,SAAS;YACT,UAAU;gBACR,SAAS;YACX;QACF;QAEA,6CAA6C;QAC7C,IAAI,CAAC,WAAW,gBAAgB,OAAO,CAAA,GAAA,+KAAA,CAAA,WAAQ,AAAD,EAAE;QAGlD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC,EAAE;QAClD,eAAe;QACf,MAAM;IACV;AACA;;;IAjEa;;AAAA,0OAAA"}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/.next-internal/server/app/(root)/vehicles/page/actions.js (server actions loader)"], "sourcesContent": ["export {getServerSession as '7f6e5dd6d98cabce2becd25e33365cd44b2d51173d'} from 'ACTIONS_MODULE0'\nexport {sendLogToLogflare as '7fffc66d044a7121f3de0a9c5ed63c896e5380b88c'} from 'ACTIONS_MODULE1'\nexport {getCookieServerByName as '7f32d250dc1bdd0adce5c3c0000b707d3e6bcd2ce7'} from 'ACTIONS_MODULE2'\nexport {setCookieServerByName as '7f3fa197bdc82caa4a02116f6307b070d6c553f962'} from 'ACTIONS_MODULE2'\nexport {deleteCookieServerByName as '7f5229c7fe229d2322a500af6d52e732bd64101335'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AACA;AACA"}}]}