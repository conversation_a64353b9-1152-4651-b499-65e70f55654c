"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, User, UserCheck } from "lucide-react"
import { userRolesApi } from '@/lib/api/user-roles.api'
import toast from 'react-hot-toast'
import { useUser } from '@/context/user-context'

export function RoleSwitch() {
  const { user, setUser } = useUser()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  // Solo mostrar si el usuario tiene más de un rol disponible
  if (!user.availableUserTypes || user.availableUserTypes.length <= 1) {
    return null
  }

  const handleRoleSwitch = async (newRole: 'client' | 'host') => {
    if (newRole === user.userType) return

    setIsLoading(true)
    try {
      const response = await userRolesApi.switchRole(newRole)

      // Actualizar el contexto del usuario
      toast.promise(
        async () => {
          await userRolesApi.switchRole(newRole)
          setUser((prevUser) => ({
            ...prevUser,
            userType: newRole,
          }))
          router.push('/dashboard')
        },
        {
          loading: 'Cambiando de rol...',
          success: response.message,
          error: 'Error al cambiar de rol',
        }
      )


    } catch (error) {
      console.error('Error switching role:', error)
      toast.error('Error al cambiar de rol')
    } finally {
      setIsLoading(false)
    }
  }

  const getRoleLabel = (role: string) => {
    return role === 'host' ? 'Anfitrión' : 'Cliente'
  }

  const getRoleIcon = (role: string) => {
    return role === 'host' ? UserCheck : User
  }

  console.log('user.userType', user.userType)
  const currentRoleLabel = getRoleLabel(user.userType || 'client')
  const CurrentRoleIcon = getRoleIcon(user.userType || 'client')

  return (
    <div className="px-4 py-2 border-t border-white/10">
      <div className="text-xs text-gray-400 mb-2">Modo actual</div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-2 h-auto text-left hover:bg-white/5"
            disabled={isLoading}
          >
            <div className="flex items-center">
              <CurrentRoleIcon className="h-4 w-4 mr-2 text-gray-400" />
              <span className="text-sm text-white">{currentRoleLabel}</span>
              {user.userType === 'host' && user.isHostVerified && (
                <Badge variant="secondary" className="ml-2 text-xs">
                  Verificado
                </Badge>
              )}
            </div>
            <ChevronDown className="h-4 w-4 text-gray-400" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="top" align="start" className="w-48 mb-2">
          {user.availableUserTypes.map((role) => {
            const RoleIcon = getRoleIcon(role)
            const isCurrentRole = role === user.userType

            return (
              <DropdownMenuItem
                key={role}
                onClick={() => handleRoleSwitch(role as 'client' | 'host')}
                disabled={isCurrentRole || isLoading}
                className={isCurrentRole ? 'bg-accent' : ''}
              >
                <RoleIcon className="mr-2 h-4 w-4" />
                <span>{getRoleLabel(role)}</span>
                {isCurrentRole && (
                  <Badge variant="outline" className="ml-auto text-xs">
                    Actual
                  </Badge>
                )}
                {role === 'host' && user.isHostVerified && (
                  <Badge variant="secondary" className="ml-auto text-xs">
                    Verificado
                  </Badge>
                )}
              </DropdownMenuItem>
            )
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
