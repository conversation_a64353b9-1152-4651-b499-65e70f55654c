{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,kMAAC,wKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,kMAAC,wKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,kMAAC,wKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,kMAAC,wKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,kMAAC;QACC,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/api/reservations.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\nimport { Vehicle } from './vehicles.api';\n\nexport interface ReservationData {\n  vehicleId: string;\n  startDate: string;\n  endDate: string;\n  totalPrice: number;\n  contactName?: string;\n  contactEmail?: string;\n  contactPhone?: string;\n}\n\nexport interface ReservationResponse {\n  id: string;\n  userId: string;\n  createdAt: Date;\n  updatedAt: Date;\n  vehicleId: string;\n  startDate: string;\n  endDate: string;\n  totalPrice: number;\n  status: string;\n  by: string;\n  reason: string | null;\n  contactName: string | null;\n  contactEmail: string | null;\n  contactPhone: string | null;\n  vehicle: Vehicle;\n}\n\nexport interface HostReservation extends ReservationResponse {\n  user: {\n    id: string;\n    name: string;\n    email: string;\n    image: string;\n  };\n}\n\nexport interface ReservationCreationResponse {\n  id: string;\n  userId: string;\n  createdAt: Date;\n  updatedAt: Date;\n  vehicleId: string;\n  startDate: Date;\n  endDate: Date;\n  totalPrice: number;\n  status: string;\n  by: string;\n  reason: string | null;\n  contactName: string | null;\n  contactEmail: string | null;\n  contactPhone: string | null;\n}\n\nexport interface ReservationsForAdminView extends HostReservation {\n  isPersonalReservation: boolean;\n}\n\nexport interface ReservationStats {\n  totalReservations: number;\n  activeReservations: number;\n  pendingReservations: number;\n  completedReservations: number;\n  cancelledReservations: number;\n  totalEarnings: number;\n}\n\nexport const reservationsApi = {\n\n  client: {\n  // Crear una nueva reserva\n    create: async (data: ReservationData) => {\n      const response = await apiService.post<ReservationCreationResponse>('/user/reservations', data);\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n\n    // Obtener reservas del usuario\n    getReservations: async () => {\n      const response = await apiService.get<ReservationResponse[]>('/user/reservations');\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n\n    // Obtener una reserva específica\n    getReservationById: async (id: string) => {\n      const response = await apiService.get<ReservationResponse>(`/user/reservations/${id}`);\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n\n    // Cancelar una reserva\n    cancelReservation: async (id: string) => {\n      const response = await apiService.patch<ReservationResponse>(`/user/reservations/${id}/cancel`);\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n  },\n\n\n\n  /* Group by host */\n  host: {\n    getReservations: async (params: { page: number; limit: number }) => {\n      const response = await apiService.get<HostReservation[]>('/host/reservations', { params });\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n    cancelReservation: async (id: string) => {\n      const response = await apiService.patch<ReservationResponse>(`/host/reservations/${id}/cancel`);\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n    getPersonalReservations: async () => {\n      const response = await apiService.get<ReservationResponse[]>('/host/reservations/personal');\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n    updateReservationStatus: async (id: string, status: string) => {\n      const response = await apiService.patch<ReservationResponse>(`/host/reservations/${id}/status`, { status });\n      return response.data;\n    },\n    getStats: async () => {\n      const response = await apiService.get<ReservationStats>('/host/reservations/stats');\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    }\n  },\n\n  admin: {\n\n    // Obtener todas las reservas para el administrador\n    getReservations: async () => {\n      const response = await apiService.get<ReservationsForAdminView[]>('/admin/reservations');\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error);\n      }\n    },\n  }\n};\n\n\n"], "names": [], "mappings": ";;;AAAA;;AAsEO,MAAM,kBAAkB;IAE7B,QAAQ;QACR,0BAA0B;QACxB,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAA8B,sBAAsB;YAC1F,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QAEA,+BAA+B;QAC/B,iBAAiB;YACf,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAwB;YAC7D,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QAEA,iCAAiC;QACjC,oBAAoB,OAAO;YACzB,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAsB,CAAC,mBAAmB,EAAE,IAAI;YACrF,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QAEA,uBAAuB;QACvB,mBAAmB,OAAO;YACxB,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAsB,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC;YAC9F,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;IACF;IAIA,iBAAiB,GACjB,MAAM;QACJ,iBAAiB,OAAO;YACtB,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAoB,sBAAsB;gBAAE;YAAO;YACxF,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QACA,mBAAmB,OAAO;YACxB,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAsB,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC;YAC9F,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QACA,yBAAyB;YACvB,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAwB;YAC7D,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;QACA,yBAAyB,OAAO,IAAY;YAC1C,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAsB,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE;YAAO;YACzG,OAAO,SAAS,IAAI;QACtB;QACA,UAAU;YACR,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAmB;YACxD,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;IACF;IAEA,OAAO;QAEL,mDAAmD;QACnD,iBAAiB;YACf,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAA6B;YAClE,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK;YAChC;QACF;IACF;AACF"}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/client/reservations/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from \"react\"\r\nimport { useQuery } from \"@tanstack/react-query\"\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\"\r\nimport { Card, CardContent } from \"@/components/ui/card\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Calendar, Car, Clock, /* MapPin */ } from \"lucide-react\"\r\nimport { format } from \"date-fns\"\r\nimport { es } from \"date-fns/locale\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { reservationsApi } from \"@/lib/api/reservations.api\"\r\n\r\nexport default function ClientReservationsPage() {\r\n  const [activeTab, setActiveTab] = useState(\"active\")\r\n  \r\n  const { data: reservations, isLoading } = useQuery({\r\n    queryKey: ['client-reservations'],\r\n    queryFn: reservationsApi.client.getReservations,\r\n    staleTime: 60 * 1000, // 1 minuto\r\n  })\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    switch (status) {\r\n      case 'active':\r\n        return <Badge className=\"bg-green-500\">Activa</Badge>\r\n      case 'confirmed':\r\n        return <Badge className=\"bg-green-500\">Confirmada</Badge>\r\n      case 'pending':\r\n        return <Badge className=\"bg-blue-500\">Pendiente</Badge>\r\n      case 'upcoming':\r\n        return <Badge className=\"bg-blue-500\">Próxima</Badge>\r\n      case 'completed':\r\n        return <Badge className=\"bg-gray-500\">Completada</Badge>\r\n      case 'cancelled':\r\n        return <Badge className=\"bg-red-500\">Cancelada</Badge>\r\n      default:\r\n        return <Badge>{status}</Badge>\r\n    }\r\n  }\r\n\r\n  const filteredReservations = reservations?.filter(reservation => {\r\n    if (activeTab === \"active\") {\r\n      return ['active', 'confirmed', 'pending', 'upcoming'].includes(reservation.status.toLowerCase());\r\n    }\r\n    if (activeTab === \"completed\") return reservation.status.toLowerCase() === \"completed\";\r\n    if (activeTab === \"cancelled\") return reservation.status.toLowerCase() === \"cancelled\";\r\n    return true;\r\n  });\r\n\r\n  // Función para formatear fechas\r\n  const formatDate = (dateString: string | Date) => {\r\n    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n    return format(date, \"d 'de' MMMM, yyyy\", { locale: es });\r\n  }\r\n\r\n  // Función para obtener la imagen del vehículo\r\n  const getVehicleImage = (vehicle: any) => {\r\n    if (!vehicle) return \"/placeholder.svg?height=300&width=500\";\r\n    if (vehicle.images && vehicle.images.length > 0) return vehicle.images[0];\r\n    return \"/placeholder.svg?height=300&width=500\";\r\n  }\r\n\r\n  // Función para obtener el nombre del vehículo\r\n  const getVehicleName = (vehicle: any) => {\r\n    if (!vehicle) return \"Vehículo no disponible\";\r\n    return `${vehicle.make} ${vehicle.model} ${vehicle.year}`;\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h1 className=\"text-3xl font-bold tracking-tight\">Mis Reservaciones</h1>\r\n        <p className=\"text-muted-foreground\">Gestiona tus reservas de vehículos</p>\r\n      </div>\r\n\r\n      <Tabs defaultValue=\"active\" value={activeTab} onValueChange={setActiveTab}>\r\n        <TabsList>\r\n          <TabsTrigger value=\"active\">Activas y Próximas</TabsTrigger>\r\n          <TabsTrigger value=\"completed\">Completadas</TabsTrigger>\r\n          <TabsTrigger value=\"cancelled\">Canceladas</TabsTrigger>\r\n        </TabsList>\r\n        \r\n        <TabsContent value={activeTab} className=\"space-y-4\">\r\n          {isLoading ? (\r\n            // Esqueletos de carga\r\n            Array.from({ length: 3 }).map((_, i) => (\r\n              <Card key={i}>\r\n                <CardContent className=\"p-6\">\r\n                  <div className=\"flex flex-col md:flex-row gap-4\">\r\n                    <Skeleton className=\"h-40 w-full md:w-48 rounded-md\" />\r\n                    <div className=\"flex-1 space-y-2\">\r\n                      <Skeleton className=\"h-6 w-48\" />\r\n                      <Skeleton className=\"h-4 w-32\" />\r\n                      <Skeleton className=\"h-4 w-64\" />\r\n                      <Skeleton className=\"h-4 w-24\" />\r\n                      <div className=\"flex justify-end mt-4\">\r\n                        <Skeleton className=\"h-10 w-32\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ))\r\n          ) : !filteredReservations || filteredReservations.length === 0 ? (\r\n            <Card>\r\n              <CardContent className=\"p-6 text-center\">\r\n                <p className=\"text-muted-foreground py-4\">No tienes reservaciones {activeTab === \"active\" ? \"activas o próximas\" : activeTab === \"completed\" ? \"completadas\" : \"canceladas\"}</p>\r\n              </CardContent>\r\n            </Card>\r\n          ) : (\r\n            filteredReservations.map(reservation => (\r\n              <Card key={reservation.id}>\r\n                <CardContent className=\"p-6\">\r\n                  <div className=\"flex flex-col md:flex-row gap-4\">\r\n                    <div className=\"relative h-40 w-full md:w-48 rounded-md overflow-hidden\">\r\n                      <Image\r\n                        src={getVehicleImage(reservation.vehicle)}\r\n                        alt={getVehicleName(reservation.vehicle)}\r\n                        fill\r\n                        className=\"object-cover\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex justify-between items-start\">\r\n                        <div>\r\n                          <h3 className=\"text-lg font-bold\">{getVehicleName(reservation.vehicle)}</h3>\r\n                          {reservation.vehicle?.host && (\r\n                            <p className=\"text-sm text-muted-foreground\">\r\n                              Anfitrión: {reservation.vehicle.host.name}\r\n                            </p>\r\n                          )}\r\n                        </div>\r\n                        {getStatusBadge(reservation.status)}\r\n                      </div>\r\n                      \r\n                      <div className=\"mt-4 space-y-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <Calendar className=\"h-4 w-4 mr-2\" />\r\n                          <span>\r\n                            {formatDate(reservation.startDate)} - {formatDate(reservation.endDate)}\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"flex items-center\">\r\n                          <Clock className=\"h-4 w-4 mr-2\" />\r\n                          <span>\r\n                            {format(new Date(reservation.startDate), \"HH:mm\")} - {format(new Date(reservation.endDate), \"HH:mm\")}\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"flex items-center\">\r\n                          <Car className=\"h-4 w-4 mr-2\" />\r\n                          <span>Total: ${reservation.totalPrice}</span>\r\n                        </div>\r\n                      </div>\r\n                      \r\n                      <div className=\"flex justify-end mt-4\">\r\n                        <Link href={`/dashboard/client/reservations/${reservation.id}`}>\r\n                          <Button>Ver detalles</Button>\r\n                        </Link>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ))\r\n          )}\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAdA;;;;;;;;;;;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,YAAY,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,UAAU;YAAC;SAAsB;QACjC,SAAS,gJAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,eAAe;QAC/C,WAAW,KAAK;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,kMAAC,yIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAe;;;;;;YACzC,KAAK;gBACH,qBAAO,kMAAC,yIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAe;;;;;;YACzC,KAAK;gBACH,qBAAO,kMAAC,yIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAc;;;;;;YACxC,KAAK;gBACH,qBAAO,kMAAC,yIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAc;;;;;;YACxC,KAAK;gBACH,qBAAO,kMAAC,yIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAc;;;;;;YACxC,KAAK;gBACH,qBAAO,kMAAC,yIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAa;;;;;;YACvC;gBACE,qBAAO,kMAAC,yIAAA,CAAA,QAAK;8BAAE;;;;;;QACnB;IACF;IAEA,MAAM,uBAAuB,cAAc,OAAO,CAAA;QAChD,IAAI,cAAc,UAAU;YAC1B,OAAO;gBAAC;gBAAU;gBAAa;gBAAW;aAAW,CAAC,QAAQ,CAAC,YAAY,MAAM,CAAC,WAAW;QAC/F;QACA,IAAI,cAAc,aAAa,OAAO,YAAY,MAAM,CAAC,WAAW,OAAO;QAC3E,IAAI,cAAc,aAAa,OAAO,YAAY,MAAM,CAAC,WAAW,OAAO;QAC3E,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;QACrE,OAAO,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,qBAAqB;YAAE,QAAQ,mJAAA,CAAA,KAAE;QAAC;IACxD;IAEA,8CAA8C;IAC9C,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,SAAS,OAAO;QACrB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG,OAAO,QAAQ,MAAM,CAAC,EAAE;QACzE,OAAO;IACT;IAEA,8CAA8C;IAC9C,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE;IAC3D;IAEA,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;;kCACC,kMAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,kMAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAGvC,kMAAC,wIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAS,OAAO;gBAAW,eAAe;;kCAC3D,kMAAC,wIAAA,CAAA,WAAQ;;0CACP,kMAAC,wIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,kMAAC,wIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,kMAAC,wIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;;;;;;;kCAGjC,kMAAC,wIAAA,CAAA,cAAW;wBAAC,OAAO;wBAAW,WAAU;kCACtC,YACC,sBAAsB;wBACtB,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,kMAAC,wIAAA,CAAA,OAAI;0CACH,cAAA,kMAAC,wIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,kMAAC;wCAAI,WAAU;;0DACb,kMAAC,4IAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,4IAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,kMAAC,4IAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,kMAAC,4IAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,kMAAC,4IAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,kMAAC;wDAAI,WAAU;kEACb,cAAA,kMAAC,4IAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAVnB;;;;wCAiBX,CAAC,wBAAwB,qBAAqB,MAAM,KAAK,kBAC3D,kMAAC,wIAAA,CAAA,OAAI;sCACH,cAAA,kMAAC,wIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,kMAAC;oCAAE,WAAU;;wCAA6B;wCAAyB,cAAc,WAAW,uBAAuB,cAAc,cAAc,gBAAgB;;;;;;;;;;;;;;;;mCAInK,qBAAqB,GAAG,CAAC,CAAA,4BACvB,kMAAC,wIAAA,CAAA,OAAI;0CACH,cAAA,kMAAC,wIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,kMAAC;wCAAI,WAAU;;0DACb,kMAAC;gDAAI,WAAU;0DACb,cAAA,kMAAC,iLAAA,CAAA,UAAK;oDACJ,KAAK,gBAAgB,YAAY,OAAO;oDACxC,KAAK,eAAe,YAAY,OAAO;oDACvC,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAGd,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;wDAAI,WAAU;;0EACb,kMAAC;;kFACC,kMAAC;wEAAG,WAAU;kFAAqB,eAAe,YAAY,OAAO;;;;;;oEACpE,YAAY,OAAO,EAAE,sBACpB,kMAAC;wEAAE,WAAU;;4EAAgC;4EAC/B,YAAY,OAAO,CAAC,IAAI,CAAC,IAAI;;;;;;;;;;;;;4DAI9C,eAAe,YAAY,MAAM;;;;;;;kEAGpC,kMAAC;wDAAI,WAAU;;0EACb,kMAAC;gEAAI,WAAU;;kFACb,kMAAC,kNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,kMAAC;;4EACE,WAAW,YAAY,SAAS;4EAAE;4EAAI,WAAW,YAAY,OAAO;;;;;;;;;;;;;0EAGzE,kMAAC;gEAAI,WAAU;;kFACb,kMAAC,4MAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,kMAAC;;4EACE,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,SAAS,GAAG;4EAAS;4EAAI,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,OAAO,GAAG;;;;;;;;;;;;;0EAGhG,kMAAC;gEAAI,WAAU;;kFACb,kMAAC,wMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;kFACf,kMAAC;;4EAAK;4EAAS,YAAY,UAAU;;;;;;;;;;;;;;;;;;;kEAIzC,kMAAC;wDAAI,WAAU;kEACb,cAAA,kMAAC,2KAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,+BAA+B,EAAE,YAAY,EAAE,EAAE;sEAC5D,cAAA,kMAAC,0IAAA,CAAA,SAAM;0EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA7CT,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;AA0DvC"}}]}