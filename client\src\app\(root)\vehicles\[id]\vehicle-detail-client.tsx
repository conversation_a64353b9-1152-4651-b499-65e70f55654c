"use client"

import { useState, useEffect, useMemo } from "react"
import { Vehicle, vehiclesApi } from "@/lib/api/vehicles.api"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { ChevronRight, /* Info, Calendar, Clock */ } from "lucide-react"
import Image from "next/image"
// import Link from 'next/link'
import DateRangeModal from '@/components/vehicles/date-range-modal'
import { DateTime } from 'luxon'
import { VehicleGallery } from '@/components/vehicles/vehicle-gallery'
import { useQuery } from '@tanstack/react-query'
import { useUser } from "@/context/user-context"
import FavoriteButton from '@/components/vehicles/favorite-button'
import { setCookie } from "cookies-next"
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog"
import { LoginForm } from "@/components/auth/login-form"
import { RegisterForm } from "@/components/auth/register-form"
import toast from "react-hot-toast"
import { addDays, differenceInDays, /* format */ } from "date-fns"
import Link from 'next/link'
// import { es } from "date-fns/locale"
// import { Badge } from "@/components/ui/badge"

const featureLabels: Record<string, Record<string, string>> = {
  mx: {
    transmission: "Transmisión",
    fuelType: "Combustible",
    seats: "Asientos",
    mileage: "Kilometraje",
    registrationNumber: "Matrícula",
    insurancePolicy: "Seguro",
    rules: "Reglas",
    location: "Ubicación",
    weeklyRate: "Tarifa semanal",
    monthlyRate: "Tarifa mensual"
  },
  en: {
    transmission: "Transmission",
    fuelType: "Fuel Type",
    seats: "Seats",
    mileage: "Mileage",
    registrationNumber: "Registration Number",
    insurancePolicy: "Insurance Policy",
    rules: "Rules",
    location: "Location",
    weeklyRate: "Weekly Rate",
    monthlyRate: "Monthly Rate"
  }
}

const labels: Record<string, Record<string, string>> = {
  mx: {
    automatic: "Automático",
    manual: "Manual"
  },
  en: {
    automatic: "Automatic",
    manual: "Manual"
  }
}

interface VehicleDetailClientProps {
  params: {
    id: string
  }
  vehicle: Vehicle
}

export default function VehicleDetailClient({ params: { id }, vehicle }: VehicleDetailClientProps) {
  const [dateRange, setDateRange] = useState<{ startDate: Date; endDate: Date }>({
    startDate: new Date(),
    endDate: addDays(new Date(), 1)
  })
  const [total, setTotal] = useState(0)
  const router = useRouter()
  const { user } = useUser()
  console.log('user', user)

  // Estado para el modal de autenticación
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')

  // const { data: vehicle, isLoading, error } = useQuery({
  //   queryKey: ['vehicle', id],
  //   // queryFn: () => vehiclesApi.getById(id),
  //   queryFn: () => getVehicleById(id),
  //   staleTime: 60 * 60 * 1000, // Caché por 1 hora
  // })
  // console.log('vehicle', vehicle)

  // Obtener fechas no disponibles
  const { data: unavailableDates } = useQuery({
    queryKey: ['vehicle-unavailable-dates', id],
    queryFn: () => vehiclesApi.getUnavailableDates(id),
    staleTime: 60 * 60 * 1000, // Caché por 1 hora
    enabled: !!id
  })

  // console.log('unavailableDatesData', unavailableDates)

  // Obtener configuración de disponibilidad
  const { data: availabilityData } = useQuery({
    queryKey: ['vehicle-availability', id],
    queryFn: () => vehiclesApi.getAvailabilitySettings(id),
    // staleTime: 60 * 60 * 1000, // Caché por 1 hora
    enabled: !!id,
    retry: 1, // Limitar reintentos en caso de error
    // onError: (error) => {
    //   console.error("Error fetching availability settings:", error);
    // }
  })

  // const unavailableDates = unavailableDatesData?.data || []

  // Configuración de disponibilidad por defecto
  const availability = useMemo(() => {
    return availabilityData || {
      minimumRentalNights: 1,
      maximumRentalNights: 30,
      defaultCheckInTime: "14:00",
      defaultCheckOutTime: "12:00",
      mondayAvailable: true,
      tuesdayAvailable: true,
      wednesdayAvailable: true,
      thursdayAvailable: true,
      fridayAvailable: true,
      saturdayAvailable: true,
      sundayAvailable: true,
      blockedDates: []
    }
  }, [availabilityData])


  // Inicializar el rango de fechas por defecto basado en la configuración
  useEffect(() => {
    if (availability && vehicle) {
      const today = new Date()
      const startDate = addDays(today, 1) // Por defecto, empezar mañana
      const endDate = addDays(startDate, Math.max(availability.minimumRentalNights, 0))

      setDateRange({
        startDate,
        endDate
      })

      // Calcular el total inicial (días, no días)
      const nights = Math.max(availability.minimumRentalNights, 0)
      setTotal(vehicle.price * nights)
    }
  }, [availability, vehicle])

  // Actualizar el total cuando cambia el rango de fechas
  useEffect(() => {
    if (vehicle && dateRange) {
      // Calcular el número de días (días - 1)
      const diffNights = differenceInDays(dateRange.endDate, dateRange.startDate)
      const newTotal = vehicle.price * diffNights
      setTotal(newTotal)
    }
  }, [dateRange, vehicle])

  // Función para obtener los días de la semana no disponibles
  const getUnavailableDaysOfWeek = () => {
    const unavailableDays = []

    if (!availability.mondayAvailable) unavailableDays.push(1) // Lunes
    if (!availability.tuesdayAvailable) unavailableDays.push(2) // Martes
    if (!availability.wednesdayAvailable) unavailableDays.push(3) // Miércoles
    if (!availability.thursdayAvailable) unavailableDays.push(4) // Jueves
    if (!availability.fridayAvailable) unavailableDays.push(5) // Viernes
    if (!availability.saturdayAvailable) unavailableDays.push(6) // Sábado
    if (!availability.sundayAvailable) unavailableDays.push(0) // Domingo

    return unavailableDays
  }

  // Función para verificar si un día está disponible
  const isDateDisabled = (date: Date) => {
    // Verificar si es un día de la semana no disponible
    const unavailableDaysOfWeek = getUnavailableDaysOfWeek()
    if (unavailableDaysOfWeek.includes(date.getDay())) {
      return true
    }

    // Verificar si está en las fechas bloqueadas manualmente
    const blockedDates = Array.isArray(availability.blockedDates)
      ? availability.blockedDates
      : []

    // Verificar si está en las fechas no disponibles (reservas)
    return unavailableDates?.some(
      (dateStr) => {
        const unavailableDate = new Date(dateStr)
        return (
          date.getFullYear() === unavailableDate.getFullYear() &&
          date.getMonth() === unavailableDate.getMonth() &&
          date.getDate() === unavailableDate.getDate()
        )
      }
    ) || blockedDates.some(
      (blockedDate: any) => {
        if (typeof blockedDate === 'string') {
          const date1 = new Date(blockedDate)
          return (
            date.getFullYear() === date1.getFullYear() &&
            date.getMonth() === date1.getMonth() &&
            date.getDate() === date1.getDate()
          )
        } else if (blockedDate && blockedDate.date) {
          const date1 = new Date(blockedDate.date)
          return (
            date.getFullYear() === date1.getFullYear() &&
            date.getMonth() === date1.getMonth() &&
            date.getDate() === date1.getDate()
          )
        }
        return false
      }
    )
  }
  console.log('isDateDisabled', isDateDisabled(new Date()))

  // Función para manejar el cambio de fechas
  const handleDateChange = (range: { startDate: Date; endDate: Date }) => {
    // Verificar que el rango cumple con el mínimo de días
    const diffNights = differenceInDays(range.endDate, range.startDate)

    if (diffNights < availability.minimumRentalNights) {
      toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)
      return
    }

    if (diffNights > availability.maximumRentalNights) {
      toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)
      return
    }

    setDateRange(range)

    // Actualizar el total (no es necesario aquí ya que se actualiza en el useEffect)
    // El useEffect se encargará de actualizar el total
  }

  // Función para manejar el botón de reserva
  const handleReserveClick = () => {
    if (!dateRange) {
      toast.error("Por favor selecciona las fechas de tu reserva")
      return
    }

    // Asegurarse de que dateRange.startDate y dateRange.endDate son objetos Date válidos
    if (!(dateRange.startDate instanceof Date) || !(dateRange.endDate instanceof Date)) {
      // Intentar convertir a objetos Date si no lo son
      try {
        const startDate = dateRange.startDate instanceof Date ? dateRange.startDate : new Date(dateRange.startDate);
        const endDate = dateRange.endDate instanceof Date ? dateRange.endDate : new Date(dateRange.endDate);

        // Verificar que el rango cumple con el mínimo de días
        const diffNights = differenceInDays(endDate, startDate);

        if (diffNights < availability.minimumRentalNights) {
          toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)
          return
        }

        if (diffNights > availability.maximumRentalNights) {
          toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)
          return
        }

        // Redirigir con las fechas convertidas
        if (!user) {
          setCookie('bookingRedirect', `/booking/${id}?from=${startDate.toISOString()}&to=${endDate.toISOString()}`)
          setAuthMode('login')
          setShowAuthDialog(true)
          return
        }

        router.push(`/booking/${id}?from=${startDate.toISOString()}&to=${endDate.toISOString()}`)
      } catch (error) {
        console.error("Error al procesar las fechas:", error);
        toast.error("Error al procesar las fechas. Por favor, selecciona las fechas nuevamente.")
        return;
      }
    } else {
      // Verificar que el rango cumple con el mínimo de días
      const diffNights = differenceInDays(dateRange.endDate, dateRange.startDate);

      if (diffNights < availability.minimumRentalNights) {
        toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)
        return
      }

      if (diffNights > availability.maximumRentalNights) {
        toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)
        return
      }

      // Verificar si el usuario está autenticado
      if (!user) {
        // Guardar la URL de redirección para después del login
        setCookie('bookingRedirect', `/booking/${id}?from=${dateRange.startDate.toISOString()}&to=${dateRange.endDate.toISOString()}`)
        setAuthMode('login')
        setShowAuthDialog(true)
        return
      }

      // Si está autenticado, redirigir a la página de reserva
      router.push(`/booking/${id}?from=${dateRange.startDate.toISOString()}&to=${dateRange.endDate.toISOString()}`)
    }
  }

  // Función para cambiar entre login y registro
  const toggleAuthMode = () => {
    setAuthMode(authMode === 'login' ? 'register' : 'login')
  }

  // Función para cerrar el modal después de autenticación exitosa
  const handleAuthSuccess = () => {
    setShowAuthDialog(false)
  }

  // Obtener características principales
  const parseDate = (date: string) => {
    // parse it like this format: "25 de junio de 2025" using luxon, date is a iso string
    return DateTime.fromISO(date).toFormat("dd 'de' MMMM 'de' yyyy", { locale: "es" })
  }


  // Obtener imágenes del vehículo
  const images = Array.isArray(vehicle.images) ? vehicle.images : [];

  // Obtener características principales
  // const transmission = vehicle.transmission || '';
  // const transmission = featureLabels["mx"]["transmission"] || '';
  const transmission = labels["mx"][vehicle.transmission] || '';
  console.log('vehicle', vehicle)
  const mileage = vehicle.features?.mileage || 0;
  const location = vehicle.features?.location || '';
  const year = vehicle.year || '';

  // Obtener días no disponibles de la semana en formato legible
  // const unavailableDaysOfWeek = getUnavailableDaysOfWeek()
  // const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado']
  // const unavailableDayNames = unavailableDaysOfWeek.map(day => dayNames[day])

  // // Calcular duración de la reserva
  // const rentalDuration = dateRange
  //   ? differenceInDays(dateRange.endDate, dateRange.startDate) + 1
  //   : availability.minimumRentalDays;

  console.log('availability', availability)

  return (
    <div className="container mx-auto py-6">
      {/* Breadcrumb */}
      <div className="text-sm text-gray-500 mb-4 flex items-center">
        {/* <span>AUTOS DISPONIBLES</span> */}
        <Link href="/vehicles" className="hover:underline">
          AUTOS DISPONIBLES
        </Link>
        <ChevronRight className="h-4 w-4 mx-1" />
        <span>{vehicle.make}</span>
        <ChevronRight className="h-4 w-4 mx-1" />
        <span>{vehicle.model}</span>
        <ChevronRight className="h-4 w-4 mx-1" />
        <span>{year}</span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Columna izquierda - Imágenes y detalles */}
        <div className="lg:col-span-2 space-y-6">
          {/* Etiqueta de recién publicado */}
          <VehicleGallery
            vehicle={vehicle}
            images={images}
          // currentImageIndex={currentImageIndex}
          // setCurrentImageIndex={setCurrentImageIndex}
          />

          {/* Título y precio */}
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center gap-3">
                <h1 className="text-2xl font-bold">
                  {vehicle.make} {vehicle.model} {transmission} {year}
                </h1>
                {/* Botón de favoritos */}
                <FavoriteButton
                  vehicleId={vehicle.id}
                  size="lg"
                  variant="outline"
                  className="flex-shrink-0"
                />
              </div>
              <div className="flex items-center mt-1">
                <span className="text-gray-600">{mileage.toLocaleString()} km</span>
                <span className="mx-2">•</span>
                <span className="text-gray-600">{location}</span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">Precio desde</div>
              <div className="text-2xl font-bold">${vehicle.price.toLocaleString()}</div>
            </div>
          </div>

          {/* Características principales */}
          <div className="mt-8">
            <h2 className="text-xl font-bold mb-4">Equipamiento destacado</h2>
            <div className="grid grid-cols-3 gap-4">
              {vehicle.features && Object.entries(vehicle.features).slice(0, 6).map(([key, value]) => (
                <div key={key} className="bg-gray-50 p-4 rounded-lg">
                  {/* <div className="text-sm text-gray-500 capitalize">{key}</div> */}
                  <div className="text-sm text-gray-500 capitalize">{featureLabels["mx"][key]}</div>
                  <div className="font-medium">{String(value)}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Características detalladas */}
          <div className="mt-8">
            <h2 className="text-xl font-bold mb-4">Características principales</h2>

            <Tabs defaultValue="general">
              <TabsList className="w-full border-b mb-4">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="exterior">Exterior</TabsTrigger>
                <TabsTrigger value="equipamiento">Equipamiento</TabsTrigger>
                <TabsTrigger value="seguridad">Seguridad</TabsTrigger>
                <TabsTrigger value="interior">Interior</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {vehicle.features && Object.entries(vehicle.features).map(([key, value]) => {
                    // Verificar si la clave existe en featureLabels
                    const label = featureLabels["mx"][key] || key;

                    return (
                      <div key={key} className="bg-gray-50 p-4 rounded-lg">
                        <div className="text-sm text-gray-500 capitalize mb-1">{label}</div>
                        <div className="font-medium">
                          {typeof value === 'string' && value.length > 50
                            ? `${value.substring(0, 50)}...`
                            : String(value)}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </TabsContent>

              <TabsContent value="exterior" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Tipo de Carrocería</div>
                    <div className="font-medium">SUV</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Color</div>
                    <div className="font-medium">{vehicle.color}</div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="equipamiento" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Aire acondicionado</div>
                    <div className="font-medium">Sí</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Bluetooth</div>
                    <div className="font-medium">Sí</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Navegación GPS</div>
                    <div className="font-medium">Sí</div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="seguridad" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Airbags</div>
                    <div className="font-medium">6</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Control de estabilidad</div>
                    <div className="font-medium">Sí</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Frenos ABS</div>
                    <div className="font-medium">Sí</div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="interior" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Tapicería</div>
                    <div className="font-medium">Tela</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Asientos eléctricos</div>
                    <div className="font-medium">No</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Volante ajustable</div>
                    <div className="font-medium">Sí</div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Columna derecha - Reserva */}
        <div>
          <div className='sticky top-6 space-y-4'>

            <Card className="">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <h2 className="text-2xl font-bold">${vehicle.price.toLocaleString()} <span className="text-base font-normal">/ día</span></h2>
                  </div>

                  <Separator />

                  <div className="mb-6">
                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-1">Selecciona las fechas</label>
                      <DateRangeModal
                        unavailableDates={unavailableDates}
                        onChange={handleDateChange}
                        initialDateRange={dateRange}
                        minimumRentalNights={availability.minimumRentalNights}
                        maximumRentalNights={availability.maximumRentalNights}
                      />
                    </div>



                    <div className="flex justify-between items-center font-bold">
                      <span>Total</span>
                      <span className="text-sm">${total.toLocaleString()}</span>
                    </div>

                    {/* Información adicional sobre la reserva */}
                    <div className="mt-2 text-xs text-gray-500 space-y-1">
                      <div className="flex justify-between">
                        <span>Precio por día:</span>
                        <span>${vehicle.price.toLocaleString()}</span>
                      </div>
                      {dateRange && (
                        <div className="flex justify-between">
                          <span>Noches seleccionadas:</span>
                          <span>{differenceInDays(dateRange.endDate, dateRange.startDate)}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span>Total:</span>
                        <span>${total.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Mínimo de días para rentar:</span>
                        <span>{availability.minimumRentalNights}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Máximo de días para rentar:</span>
                        <span>{availability.maximumRentalNights}</span>
                      </div>
                    </div>

                  </div>

                  <Button
                    className="w-full bg-[#1a2b5e] hover:bg-[#152348]"
                    onClick={handleReserveClick}
                  >
                    <span className="font-medium">Reservar</span>
                  </Button>

                  <p className="text-sm text-muted-foreground text-center">
                    No se te cobrará nada todavía
                  </p>

                  <div className="pt-4 space-y-2">
                    <div className="flex items-center text-sm">
                      <svg className="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Auto con certificado Autoop</span>
                    </div>

                    <div className="flex items-center text-sm">
                      <svg className="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Alguna caracteristica aqui</span>
                    </div>
                  </div>
                </div>

              </CardContent>
            </Card>
            <div className="border rounded-lg p-6 hidden lg:block">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden relative">
                  <Image
                    src={vehicle.host.image || "/placeholder.svg?height=48&width=48"}
                    alt="Host"
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-medium">{vehicle.host.name}</h3>
                  <p className="text-sm text-gray-500">Miembro desde {parseDate(vehicle.host.createdAt)}</p>
                </div>
              </div>

              <Button variant="outline" className="w-full">
                <span className="font-medium">Contact Host</span>
              </Button>
            </div>
          </div>

        </div>
      </div>

      {/* Modal de autenticación */}
      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog} >
        <DialogContent className="sm:max-w-md ">
        <DialogTitle className='sr-only'>
          {authMode === 'login' ? 'Inicia sesión para continuar' : 'Crea una cuenta para continuar'}
          </DialogTitle>
          <div className='mt-5'>
            {authMode === 'login' ? (
              <LoginForm
                onSuccess={handleAuthSuccess}
                onRegisterClick={toggleAuthMode}
                redirectAfterLogin={true}
              />
            ) : (
              <RegisterForm
                onSuccess={handleAuthSuccess}
                onLoginClick={toggleAuthMode}
                redirectAfterRegister={true}
              />
            )}
          </div>

        </DialogContent>
      </Dialog>
    </div>
  )
}




