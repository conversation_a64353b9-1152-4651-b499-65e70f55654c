{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/constants/env.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const envSchema = z.object({\r\n  NEXT_PUBLIC_API_URL: z.string().url(),\r\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\r\n  IS_DEV: z.string().optional().transform((val) => val === 'true'),\r\n});\r\n\r\nexport type Env = z.infer<typeof envSchema>;\r\n\r\n// create the same object but with process.env\r\nconst env = envSchema.parse({\r\n  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n  NODE_ENV: process.env.NODE_ENV,\r\n  IS_DEV: process.env.IS_DEV,\r\n});\r\n\r\nexport default env;\r\n"], "names": [], "mappings": ";;;;AAYuB;AAZvB;;AAEO,MAAM,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,qBAAqB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IACnC,UAAU,uIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAChE,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,MAAQ,QAAQ;AAC3D;AAIA,8CAA8C;AAC9C,MAAM,MAAM,UAAU,KAAK,CAAC;IAC1B,mBAAmB;IACnB,QAAQ;IACR,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,MAAM;AAC5B;uCAEe", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\r\nimport env from \"../constants/env\";\r\nimport { BEARER_COOKIE_NAME } from '@/constants';\r\nimport { getCookie } from 'cookies-next/client';\r\n// import { sendLogToLogflare } from '@/lib/log-requests';\r\n\r\nfunction checkIfIsClient() {\r\n    return typeof window !== 'undefined';\r\n}\r\n\r\ntype ApiResponse<T> =\r\n    {\r\n        success: true;\r\n        data: T; status:\r\n        number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: InternalAxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: undefined\r\n    }\r\n    |\r\n    {\r\n        success: false;\r\n        data: undefined;\r\n        status: number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: AxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: any\r\n    };\r\n\r\ntype ErrorHandler = (error: any) => void;\r\n\r\nconst baseURL = env.NEXT_PUBLIC_API_URL;\r\n\r\nclass ApiService {\r\n    private axiosInstance: AxiosInstance;\r\n    baseURL: string = '';\r\n\r\n    /** \r\n     * @param version - The version of the API to use. Defaults to 'v1'.\r\n     */\r\n    constructor(\r\n        {\r\n            version,\r\n            prefix\r\n        }:\r\n            {\r\n                version?: 'v1' | 'v2',\r\n                prefix?: 'api' | 'dash-utils'\r\n            } =\r\n            {\r\n                version: 'v1',\r\n                prefix: 'api'\r\n            }) {\r\n\r\n\r\n        // Version is only available for api prefix so if prefix is dash-utils, version is not used\r\n        this.baseURL = `${baseURL}/${prefix}${prefix === 'api' ? `/${version}` : ''}`;\r\n\r\n        this.axiosInstance = axios.create({\r\n            baseURL: this.baseURL,\r\n            withCredentials: true\r\n        });\r\n    }\r\n\r\n    private async setHeaders() {\r\n        const isClient = checkIfIsClient();\r\n        if (isClient) {\r\n            return {};\r\n\r\n        };\r\n        const headers = (await import('next/headers')).headers;\r\n        const rawHeaders = await headers();\r\n        const headersObj: Record<string, string> = {};\r\n        rawHeaders.forEach((value, key) => {\r\n            headersObj[key] = value;\r\n        });\r\n        return headersObj;\r\n    }\r\n\r\n    private async request<T>(\r\n        method: 'get' | 'post' | 'patch' | 'put' | 'delete',\r\n        path: string,\r\n        config?: AxiosRequestConfig,\r\n        onError?: ErrorHandler\r\n    ): Promise<ApiResponse<T>> {\r\n        try {\r\n            const isClient = checkIfIsClient();\r\n            const headers = await this.setHeaders();\r\n            let token = ''\r\n\r\n            if (isClient) {\r\n                token = getCookie(BEARER_COOKIE_NAME) as string;\r\n            } else {\r\n                const getCookieServerByName = (await import('@/actions/cookies')).getCookieServerByName;\r\n                token = await getCookieServerByName({ name: BEARER_COOKIE_NAME }) as string;\r\n                if (path.includes('/files/download')) {\r\n                    console.log('Token on request for /api/v1/files/download: ', token);\r\n                }\r\n            }\r\n\r\n            const requestHeaders = {\r\n                    cookie: isClient ? undefined : headers.cookie,\r\n                    'x-dashboard-call': 'true',\r\n                    Authorization: `Bearer ${token}`,\r\n                    ...config?.headers\r\n                }\r\n\r\n            if (path.includes('/files/download')) {\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n                console.log('Headers: ', headers);\r\n                console.log('Request on /api/v1/files/download: ', requestHeaders);\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n            }\r\n\r\n            const response = await this.axiosInstance({\r\n                method,\r\n                url: path,\r\n                ...config,\r\n                headers: requestHeaders,\r\n            });\r\n            // Simplificamos el objeto de respuesta para evitar problemas de serialización\r\n            return {\r\n                success: true,\r\n                data: response.data,\r\n                status: response.status,\r\n                statusText: response.statusText,\r\n                headers: response.headers,\r\n                config: response.config,\r\n                request: response.request,\r\n                error: undefined\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error in request:', error?.config?.url);\r\n\r\n            if (onError) onError(error);\r\n            await this.handleError(error);\r\n            return {\r\n                success: false,\r\n                data: undefined,\r\n                status: error.response?.status || 500,\r\n                statusText: error.response?.statusText || 'Unknown Error',\r\n                headers: error.response?.headers || {},\r\n                config: error.config || {},\r\n                request: error.request || {},\r\n                error: error?.response?.data || error.message,\r\n            };\r\n        }\r\n    }\r\n\r\n    public async get<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('get', path, config, onError);\r\n    }\r\n\r\n    public async post<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('post', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async patch<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('patch', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async put<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('put', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async delete<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('delete', path, config, onError);\r\n    }\r\n\r\n    private async handleError(error: any) {\r\n        console.error('API Error:', {\r\n            message: error.message,\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n        });\r\n    }\r\n}\r\n\r\nexport const apiService = new ApiService();\r\n\r\nexport const dashUtilsService = new ApiService({ prefix: 'dash-utils' });\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACA,0DAA0D;AAE1D,SAAS;IACL,OAAO,aAAkB;AAC7B;AA2BA,MAAM,UAAU,0HAAA,CAAA,UAAG,CAAC,mBAAmB;AAEvC,MAAM;IACM,cAA6B;IACrC,UAAkB,GAAG;IAErB;;KAEC,GACD,YACI,EACI,OAAO,EACP,MAAM,EAKL,GACD;QACI,SAAS;QACT,QAAQ;IACZ,CAAC,CAAE;QAGP,2FAA2F;QAC3F,IAAI,CAAC,OAAO,GAAG,GAAG,QAAQ,CAAC,EAAE,SAAS,WAAW,QAAQ,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI;QAE7E,IAAI,CAAC,aAAa,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC9B,SAAS,IAAI,CAAC,OAAO;YACrB,iBAAiB;QACrB;IACJ;IAEA,MAAc,aAAa;QACvB,MAAM,WAAW;QACjB,wCAAc;YACV,OAAO,CAAC;QAEZ;;QACA,MAAM;QACN,MAAM;QACN,MAAM;IAKV;IAEA,MAAc,QACV,MAAmD,EACnD,IAAY,EACZ,MAA2B,EAC3B,OAAsB,EACC;QACvB,IAAI;YACA,MAAM,WAAW;YACjB,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;YACrC,IAAI,QAAQ;YAEZ,wCAAc;gBACV,QAAQ,CAAA,GAAA,4JAAA,CAAA,YAAS,AAAD,EAAE,4HAAA,CAAA,qBAAkB;YACxC,OAAO;;YAMP;YAEA,MAAM,iBAAiB;gBACf,QAAQ,uCAAW;gBACnB,oBAAoB;gBACpB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,GAAG,QAAQ,OAAO;YACtB;YAEJ,IAAI,KAAK,QAAQ,CAAC,oBAAoB;gBAClC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBAEZ,QAAQ,GAAG,CAAC,aAAa;gBACzB,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;YAEhB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;gBACtC;gBACA,KAAK;gBACL,GAAG,MAAM;gBACT,SAAS;YACb;YACA,8EAA8E;YAC9E,OAAO;gBACH,SAAS;gBACT,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,QAAQ,SAAS,MAAM;gBACvB,SAAS,SAAS,OAAO;gBACzB,OAAO;YACX;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,qBAAqB,OAAO,QAAQ;YAElD,IAAI,SAAS,QAAQ;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,OAAO;gBACH,SAAS;gBACT,MAAM;gBACN,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,YAAY,MAAM,QAAQ,EAAE,cAAc;gBAC1C,SAAS,MAAM,QAAQ,EAAE,WAAW,CAAC;gBACrC,QAAQ,MAAM,MAAM,IAAI,CAAC;gBACzB,SAAS,MAAM,OAAO,IAAI,CAAC;gBAC3B,OAAO,OAAO,UAAU,QAAQ,MAAM,OAAO;YACjD;QACJ;IACJ;IAEA,MAAa,IAAO,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC5G,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,QAAQ;IAC7C;IAEA,MAAa,KAAQ,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACzH,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC3D;IAEA,MAAa,MAAS,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC1H,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC5D;IAEA,MAAa,IAAO,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACxH,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC1D;IAEA,MAAa,OAAU,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC/G,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM,QAAQ;IAChD;IAEA,MAAc,YAAY,KAAU,EAAE;QAClC,QAAQ,KAAK,CAAC,cAAc;YACxB,SAAS,MAAM,OAAO;YACtB,QAAQ,MAAM,QAAQ,EAAE;YACxB,MAAM,MAAM,QAAQ,EAAE;QAC1B;IACJ;AACJ;AAEO,MAAM,aAAa,IAAI;AAEvB,MAAM,mBAAmB,IAAI,WAAW;IAAE,QAAQ;AAAa", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/api/vehicles.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\r\nimport { cache } from 'react';\r\n\r\nexport interface VehicleFormData {\r\n  make: string;\r\n  model: string;\r\n  year: number;\r\n  color: string;\r\n  vin: string;\r\n  plate: string;\r\n  state_code: string;\r\n  country_code: string;\r\n  price: number;\r\n  description: string;\r\n\r\n  // Nuevos campos estructurados\r\n  engineSize: number;\r\n  transmission: string;\r\n  trim: string;\r\n  bodyType: string;\r\n\r\n  // Campos existentes\r\n  features: any;\r\n  amenities: string[];\r\n  // images?: string[];\r\n  status?: string;\r\n  images?: File[];\r\n  // vinDocument?: File;\r\n  // plateDocument?: File;\r\n  // registrationDocument?: File;\r\n  // insurancePolicyDocument?: File;\r\n  // titleDocument?: File;\r\n  vinDocument: File[];\r\n  plateDocument: File[];\r\n  registrationDocument: File[];\r\n  insurancePolicyDocument: File[];\r\n}\r\n\r\nexport interface Host {\r\n  id: string;\r\n  name: string;\r\n  image?: string;\r\n  email: string;\r\n  phone: string;\r\n  status: string;\r\n  isVerified: boolean;\r\n  isBlocked: boolean;\r\n\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface Vehicle {\r\n  id: string;\r\n  make: string;\r\n  model: string;\r\n  year: number;\r\n  color: string;\r\n  vin?: string;\r\n  // licensePlate?: string;\r\n  plate?: string;\r\n  state_code?: string;\r\n  country_code?: string;\r\n  price: number;\r\n  rating: number;\r\n  reviews: number;\r\n\r\n  // Nuevos campos estructurados\r\n  engineSize: number;\r\n  transmission: string;\r\n  trim: string;\r\n  bodyType: string;\r\n\r\n  approvalHistory: {\r\n    action: string;\r\n    date: string;\r\n    reason?: string;\r\n    userId?: string;\r\n    user?: {\r\n      id: string;\r\n      name: string;\r\n      email: string;\r\n    };\r\n  }[];\r\n\r\n  // Campos existentes\r\n  images: string[];\r\n  features: {\r\n    fuelType: string;\r\n    seats: number;\r\n    mileage: number;\r\n    registrationNumber: string;\r\n    insurancePolicy: string;\r\n    rules: string;\r\n    location: string;\r\n    weeklyRate: number;\r\n    monthlyRate: number;\r\n  };\r\n  description: string;\r\n  amenities: string[];\r\n  host: Host;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface VehicleResponse {\r\n  data: Vehicle[];\r\n  pagination: Pagination;\r\n}\r\n\r\nexport interface TimeSlot {\r\n  startTime: string;\r\n  endTime: string;\r\n}\r\n\r\nexport interface AvailabilitySettings {\r\n  defaultCheckInTime: string;\r\n  defaultCheckOutTime: string;\r\n  minimumRentalNights: number;\r\n  maximumRentalNights: number;\r\n  // bufferTimeBetweenRentals: number; // horas\r\n  advanceBookingPeriod: number; // días\r\n  instantBooking: boolean;\r\n  allowSameDayBooking: boolean;\r\n  cancellationPolicy: \"flexible\" | \"moderate\" | \"strict\";\r\n\r\n  mondayAvailable: boolean;\r\n  tuesdayAvailable: boolean;\r\n  wednesdayAvailable: boolean;\r\n  thursdayAvailable: boolean;\r\n  fridayAvailable: boolean;\r\n  saturdayAvailable: boolean;\r\n  sundayAvailable: boolean;\r\n  blockedDates: any[];\r\n}\r\n\r\nexport interface VehicleDocuments {\r\n  vinDocument: string;\r\n  plateDocument: string;\r\n  registrationDocument: string;\r\n  insurancePolicyDocument: string;\r\n}\r\n\r\n// Agregar esta interfaz para las estadísticas\r\nexport interface VehicleStats {\r\n  stats: {\r\n    total: number;\r\n    active: number;\r\n    rented: number;\r\n    maintenance: number;\r\n    pending: number;\r\n    totalReservations?: number;\r\n    averageRating?: number;\r\n    averageIncome?: number;\r\n  }\r\n}\r\n\r\nexport const vehiclesApi = {\r\n  // Obtener todos los vehículos (público)\r\n  getAll: async (params: { page: number; limit: number }) => {\r\n    const result = await apiService.get<VehicleResponse>('/vehicles', { params });\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener un vehículo por ID (público)\r\n  getById: async (id: string) => {\r\n    const result = await apiService.get<Vehicle>(`/vehicles/${id}`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener todos los vehículos (admin)\r\n  getAllForAdmin: async () => {\r\n    const result = await apiService.get<VehicleResponse>('/admin/vehicles');\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener vehículos del host actual\r\n  getMyVehicles: async (): Promise<VehicleResponse[]> => {\r\n    const result = await apiService.get<VehicleResponse[]>('/host/vehicles');\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Añadir método para obtener un vehículo específico del host\r\n  getMyVehicleById: async (id: string): Promise<VehicleResponse> => {\r\n    const result = await apiService.get<VehicleResponse>(`/host/vehicles/${id}`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  getAvailabilitySettings: async (vehicleId: string): Promise<AvailabilitySettings> => {\r\n    const result = await apiService.get<AvailabilitySettings>(`/vehicles/${vehicleId}/availability`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n\r\n\r\n  host: {\r\n    getAll: async (params: { page?: number; limit?: number }) => {\r\n      const result = await apiService.get<VehicleResponse>('/host/vehicles', { params });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    getById: async (id: string) => {\r\n      const result = await apiService.get<Vehicle>(`/host/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    create: async (data: VehicleFormData) => {\r\n      const result = await apiService.post<Vehicle>('/host/vehicles', data);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        // console.error('Error creating vehicle:', result.error);\r\n        throw new Error(result.error.message);\r\n      }\r\n    },\r\n\r\n    uploadFiles: async (vehicleId: string, data: Partial<VehicleFormData>) => {\r\n      const formData = new FormData();\r\n\r\n      // Agregar archivos al formData para los campos images, vinDocument, plateDocument, registrationDocument, insurancePolicyDocument\r\n      if (data.images) {\r\n        for (const file of data.images) {\r\n          formData.append('images', file);\r\n        }\r\n      }\r\n      if (data.vinDocument) {\r\n        for (const file of data.vinDocument) {\r\n          formData.append('vinDocument', file);\r\n        }\r\n      }\r\n      if (data.plateDocument) {\r\n        for (const file of data.plateDocument) {\r\n          formData.append('plateDocument', file);\r\n        }\r\n      }\r\n      if (data.registrationDocument) {\r\n        for (const file of data.registrationDocument) {\r\n          formData.append('registrationDocument', file);\r\n        }\r\n      }\r\n      if (data.insurancePolicyDocument) {\r\n        for (const file of data.insurancePolicyDocument) {\r\n          formData.append('insurancePolicyDocument', file);\r\n        }\r\n      }\r\n\r\n      const result = await apiService.patch<Vehicle>(`/host/vehicles/${vehicleId}/upload-files`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Añadir método para actualizar archivos\r\n    updateFiles: async (vehicleId: string, data: Partial<VehicleFormData>, imagesToRemove: string[] = []) => {\r\n\r\n      const formData = new FormData();\r\n\r\n      // Agregar archivos al formData solo si existen y tienen elementos\r\n      if (data.images && data.images.length > 0) {\r\n        for (const file of data.images) {\r\n          formData.append('images', file);\r\n        }\r\n      }\r\n\r\n      if (data.vinDocument && data.vinDocument.length > 0) {\r\n        for (const file of data.vinDocument) {\r\n          formData.append('vinDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.plateDocument && data.plateDocument.length > 0) {\r\n        for (const file of data.plateDocument) {\r\n          formData.append('plateDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.registrationDocument && data.registrationDocument.length > 0) {\r\n        for (const file of data.registrationDocument) {\r\n          formData.append('registrationDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.insurancePolicyDocument && data.insurancePolicyDocument.length > 0) {\r\n        for (const file of data.insurancePolicyDocument) {\r\n          formData.append('insurancePolicyDocument', file);\r\n        }\r\n      }\r\n\r\n      // Agregar imágenes a eliminar\r\n      if (imagesToRemove.length > 0) {\r\n        formData.append('imagesToRemove', JSON.stringify(imagesToRemove));\r\n      }\r\n\r\n      const result = await apiService.patch<Vehicle>(`/host/vehicles/${vehicleId}/update-files`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      console.log('Result:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error?.message || 'Ha ocurrido un error actualizando los archivos del vehículo.');\r\n      }\r\n    },\r\n\r\n    update: async (id: string, data: Partial<VehicleFormData>) => {\r\n      const result = await apiService.put<Vehicle>(`/host/vehicles/${id}`, data);\r\n      console.log('Result of update vehicle:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        console.error('Error updating vehicle:', result.error);\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Eliminar un vehículo (host)\r\n    delete: async (id: string) => {\r\n      const result = await apiService.delete(`/host/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    blockDates: async (vehicleId: string, data: {\r\n      startDate: string;\r\n      endDate: string;\r\n      reason?: string;\r\n    }): Promise<any> => {\r\n\r\n      if (!data.startDate || !data.endDate) {\r\n        throw new Error(\"Se requieren fechas válidas para bloquear\");\r\n      }\r\n\r\n      const result = await apiService.post('/host/reservations/block-dates', {\r\n        vehicleId,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        reason: data.reason || \"No disponible\"\r\n      });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Actualizar configuración de disponibilidad\r\n    updateAvailabilitySettings: async (vehicleId: string, data: Partial<AvailabilitySettings>): Promise<AvailabilitySettings> => {\r\n      const result = await apiService.put<AvailabilitySettings>(`/host/vehicles/${vehicleId}/availability`, data);\r\n      console.log('error: ', result.error);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n    }\r\n  },\r\n    getStats: async (): Promise<any> => {\r\n      const result = await apiService.get('/host/vehicles/stats');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Obtener documentos de un vehículo\r\n    getDocuments: async (vehicleId: string) => {\r\n      const result = await apiService.get<VehicleDocuments>(`/host/vehicles/${vehicleId}/documents`);\r\n      console.log('Result of get documents:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    requestReview: async (vehicleId: string) => {\r\n      const result = await apiService.post(`/host/vehicles/${vehicleId}/request-review`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  // Actualizar estado de un vehículo (host)\r\n  updateStatus: async (id: string, status: string): Promise<VehicleResponse> => {\r\n    const result = await apiService.patch<VehicleResponse>(`/host/vehicles/${id}/status`, { status });\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Funciones específicas para administradores\r\n  admin: {\r\n\r\n    getById: async (id: string) => {\r\n      const result = await apiService.get<Vehicle>(`/admin/vehicles/${id}`);\r\n      if (result.success) {\r\n        console.log('vehicle from api', result.data);\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Obtener vehículos pendientes de aprobación\r\n    getPendingVehicles: async () => {\r\n      const result = await apiService.get<Vehicle[]>('/admin/vehicles/pending');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error.message);\r\n      }\r\n    },\r\n\r\n    // Aprobar un vehículo\r\n    approveVehicle: async (id: string) => {\r\n      const result = await apiService.post<Vehicle>(`/admin/vehicles/${id}/approve`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Rechazar un vehículo\r\n    rejectVehicle: async (id: string, reason: string): Promise<any> => {\r\n      const result = await apiService.post(`/admin/vehicles/${id}/reject`, { reason });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Actualizar un vehículo (admin)\r\n    update: async (id: string, data: Partial<VehicleFormData>) => {\r\n      const result = await apiService.put<Vehicle>(`/admin/vehicles/${id}`, data);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Eliminar un vehículo (admin)\r\n    delete: async (id: string): Promise<any> => {\r\n      const result = await apiService.delete(`/admin/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Actualizar estado de un vehículo (admin)\r\n    updateStatus: async (id: string, status: string) => {\r\n      const result = await apiService.patch<Vehicle>(`/admin/vehicles/${id}/status`, { status });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Obtener todos los vehículos para el administrador\r\n    getAll: async (params: { page: number; limit: number }) => {\r\n      const result = await apiService.get<VehicleResponse>('/admin/vehicles', { params });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Nuevo método para obtener estadísticas\r\n    getStats: async (): Promise<VehicleStats> => {\r\n      const result = await apiService.get<VehicleStats>('/admin/vehicles/stats');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n  },\r\n\r\n  // Obtener fechas no disponibles para un vehículo\r\n  getUnavailableDates: async (vehicleId: string): Promise<string[]> => {\r\n    const result = await apiService.get</* string[] */\r\n      { date: string, by: string, reason?: string }[]\r\n    >(`/reservations/unavailable-dates/${vehicleId}`);\r\n    if (result.success) {\r\n      // return { data: result.data };\r\n      // return { data: result.data.map((item) => item.date) };\r\n      return result.data.map((item) => item.date);\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n};\r\n\r\nexport const getVehicleById = cache(vehiclesApi.getById);\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AA6JO,MAAM,cAAc;IACzB,wCAAwC;IACxC,QAAQ,OAAO;QACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,aAAa;YAAE;QAAO;QAC3E,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,uCAAuC;IACvC,SAAS,OAAO;QACd,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI;QAC9D,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,sCAAsC;IACtC,gBAAgB;QACd,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB;QACrD,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,oCAAoC;IACpC,eAAe;QACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAoB;QACvD,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6DAA6D;IAC7D,kBAAkB,OAAO;QACvB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,CAAC,eAAe,EAAE,IAAI;QAC3E,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,yBAAyB,OAAO;QAC9B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAuB,CAAC,UAAU,EAAE,UAAU,aAAa,CAAC;QAC/F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAIA,MAAM;QACJ,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,kBAAkB;gBAAE;YAAO;YAChF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,SAAS,OAAO;YACd,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,eAAe,EAAE,IAAI;YACnE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAU,kBAAkB;YAChE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,0DAA0D;gBAC1D,MAAM,IAAI,MAAM,OAAO,KAAK,CAAC,OAAO;YACtC;QACF;QAEA,aAAa,OAAO,WAAmB;YACrC,MAAM,WAAW,IAAI;YAErB,iIAAiI;YACjI,IAAI,KAAK,MAAM,EAAE;gBACf,KAAK,MAAM,QAAQ,KAAK,MAAM,CAAE;oBAC9B,SAAS,MAAM,CAAC,UAAU;gBAC5B;YACF;YACA,IAAI,KAAK,WAAW,EAAE;gBACpB,KAAK,MAAM,QAAQ,KAAK,WAAW,CAAE;oBACnC,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YACA,IAAI,KAAK,aAAa,EAAE;gBACtB,KAAK,MAAM,QAAQ,KAAK,aAAa,CAAE;oBACrC,SAAS,MAAM,CAAC,iBAAiB;gBACnC;YACF;YACA,IAAI,KAAK,oBAAoB,EAAE;gBAC7B,KAAK,MAAM,QAAQ,KAAK,oBAAoB,CAAE;oBAC5C,SAAS,MAAM,CAAC,wBAAwB;gBAC1C;YACF;YACA,IAAI,KAAK,uBAAuB,EAAE;gBAChC,KAAK,MAAM,QAAQ,KAAK,uBAAuB,CAAE;oBAC/C,SAAS,MAAM,CAAC,2BAA2B;gBAC7C;YACF;YAEA,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE,UAAU;gBACnG,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,yCAAyC;QACzC,aAAa,OAAO,WAAmB,MAAgC,iBAA2B,EAAE;YAElG,MAAM,WAAW,IAAI;YAErB,kEAAkE;YAClE,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;gBACzC,KAAK,MAAM,QAAQ,KAAK,MAAM,CAAE;oBAC9B,SAAS,MAAM,CAAC,UAAU;gBAC5B;YACF;YAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,MAAM,QAAQ,KAAK,WAAW,CAAE;oBACnC,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,MAAM,GAAG,GAAG;gBACvD,KAAK,MAAM,QAAQ,KAAK,aAAa,CAAE;oBACrC,SAAS,MAAM,CAAC,iBAAiB;gBACnC;YACF;YAEA,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,CAAC,MAAM,GAAG,GAAG;gBACrE,KAAK,MAAM,QAAQ,KAAK,oBAAoB,CAAE;oBAC5C,SAAS,MAAM,CAAC,wBAAwB;gBAC1C;YACF;YAEA,IAAI,KAAK,uBAAuB,IAAI,KAAK,uBAAuB,CAAC,MAAM,GAAG,GAAG;gBAC3E,KAAK,MAAM,QAAQ,KAAK,uBAAuB,CAAE;oBAC/C,SAAS,MAAM,CAAC,2BAA2B;gBAC7C;YACF;YAEA,8BAA8B;YAC9B,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,SAAS,MAAM,CAAC,kBAAkB,KAAK,SAAS,CAAC;YACnD;YAEA,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE,UAAU;gBACnG,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,WAAW;YACvB,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;YAC3C;QACF;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,eAAe,EAAE,IAAI,EAAE;YACrE,QAAQ,GAAG,CAAC,6BAA6B;YACzC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,OAAO,KAAK;gBACrD,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,8BAA8B;QAC9B,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;YAC7D,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,YAAY,OAAO,WAAmB;YAMpC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,OAAO,EAAE;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,kCAAkC;gBACrE;gBACA,WAAW,KAAK,SAAS;gBACzB,SAAS,KAAK,OAAO;gBACrB,QAAQ,KAAK,MAAM,IAAI;YACzB;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,6CAA6C;QAC7C,4BAA4B,OAAO,WAAmB;YACpD,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAuB,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE;YACtG,QAAQ,GAAG,CAAC,WAAW,OAAO,KAAK;YACnC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAChC;QACF;QACE,UAAU;YACR,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAC;YACpC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,oCAAoC;QACpC,cAAc,OAAO;YACnB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAmB,CAAC,eAAe,EAAE,UAAU,UAAU,CAAC;YAC7F,QAAQ,GAAG,CAAC,4BAA4B;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,eAAe,OAAO;YACpB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,UAAU,eAAe,CAAC;YACjF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;IACF;IAIA,0CAA0C;IAC1C,cAAc,OAAO,IAAY;QAC/B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAkB,CAAC,eAAe,EAAE,GAAG,OAAO,CAAC,EAAE;YAAE;QAAO;QAC/F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6CAA6C;IAC7C,OAAO;QAEL,SAAS,OAAO;YACd,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,gBAAgB,EAAE,IAAI;YACpE,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,GAAG,CAAC,oBAAoB,OAAO,IAAI;gBAC3C,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,6CAA6C;QAC7C,oBAAoB;YAClB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAY;YAC/C,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,CAAC,OAAO;YACtC;QACF;QAEA,sBAAsB;QACtB,gBAAgB,OAAO;YACrB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAU,CAAC,gBAAgB,EAAE,GAAG,QAAQ,CAAC;YAC7E,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,uBAAuB;QACvB,eAAe,OAAO,IAAY;YAChC,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE;YAAO;YAC9E,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,iCAAiC;QACjC,QAAQ,OAAO,IAAY;YACzB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,gBAAgB,EAAE,IAAI,EAAE;YACtE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,+BAA+B;QAC/B,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,IAAI;YAC9D,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,2CAA2C;QAC3C,cAAc,OAAO,IAAY;YAC/B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE;YAAO;YACxF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,oDAAoD;QACpD,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,mBAAmB;gBAAE;YAAO;YACjF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,yCAAyC;QACzC,UAAU;YACR,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAe;YAClD,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;IACF;IAEA,iDAAiD;IACjD,qBAAqB,OAAO;QAC1B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAEjC,CAAC,gCAAgC,EAAE,WAAW;QAChD,IAAI,OAAO,OAAO,EAAE;YAClB,gCAAgC;YAChC,yDAAyD;YACzD,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;QAC5C,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;AACF;AAEO,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD,EAAE,YAAY,OAAO", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\nimport { DateTime } from \"luxon\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n/**\r\n * Normaliza una fecha ISO a un objeto Date de JavaScript establecido a medianoche en la zona horaria local\r\n * @param dateStr Fecha en formato ISO string\r\n * @returns Objeto Date normalizado\r\n */\r\nexport function normalizeDate(dateStr: string): Date {\r\n  return DateTime.fromISO(dateStr)\r\n    .setZone('local')\r\n    .startOf('day')\r\n    .toJSDate();\r\n}\r\n\r\n/**\r\n * Convierte un array de fechas ISO a objetos Date normalizados\r\n * @param dateStrings Array de fechas en formato ISO string\r\n * @returns Array de objetos Date normalizados\r\n */\r\nexport function normalizeDates(dateStrings: string[]): Date[] {\r\n  if (!dateStrings || !Array.isArray(dateStrings)) return [];\r\n  return dateStrings.map(normalizeDate);\r\n}\r\n\r\n/**\r\n * Convierte un ISO a un string con formato \"dd de MMM de yyyy\"\r\n * @param date Fecha en formato ISO string\r\n * @returns String formateado\r\n */\r\nexport function formatISODate(date: string): string {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMM \\'de\\' yyyy', { locale: 'es' });\r\n}\r\n\r\n/**\r\n * Verifica si una fecha está en un array de fechas deshabilitadas\r\n * @param date Fecha a verificar\r\n * @param disabledDates Array de fechas deshabilitadas\r\n * @returns true si la fecha está deshabilitada, false en caso contrario\r\n */\r\nexport function isDateDisabled(date: Date, disabledDates: Date[]): boolean {\r\n  const luxonDate = DateTime.fromJSDate(date)\r\n    .setZone('local')\r\n    .startOf('day');\r\n\r\n  return disabledDates.some(disabledDate => {\r\n    const luxonDisabledDate = DateTime.fromJSDate(disabledDate);\r\n    return luxonDate.hasSame(luxonDisabledDate, 'day');\r\n  });\r\n}\r\n\r\n/**\r\n * Formatea una fecha a un string en formato: \"dd de MMM de yyyy\"\r\n * @param date Fecha a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatDate(date: string) {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMMM, yyyy', { locale: 'es' });\r\n}\r\n\r\n/** \r\n * Formatea un número a un string en formato de moneda local, por default MXN\r\n * @param value Número a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatCurrency(value: number, currency: string = 'MXN') {\r\n  return new Intl.NumberFormat('es-MX', { style: 'currency', currency }).format(value);\r\n\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAOzE,SAAS,cAAc,OAAe;IAC3C,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SACrB,OAAO,CAAC,SACR,OAAO,CAAC,OACR,QAAQ;AACb;AAOO,SAAS,eAAe,WAAqB;IAClD,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO,CAAC,cAAc,OAAO,EAAE;IAC1D,OAAO,YAAY,GAAG,CAAC;AACzB;AAOO,SAAS,cAAc,IAAY;IACxC,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,4BAA4B;QAAE,QAAQ;IAAK;AACzD;AAQO,SAAS,eAAe,IAAU,EAAE,aAAqB;IAC9D,MAAM,YAAY,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,MACnC,OAAO,CAAC,SACR,OAAO,CAAC;IAEX,OAAO,cAAc,IAAI,CAAC,CAAA;QACxB,MAAM,oBAAoB,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC9C,OAAO,UAAU,OAAO,CAAC,mBAAmB;IAC9C;AACF;AAOO,SAAS,WAAW,IAAY;IACrC,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,uBAAuB;QAAE,QAAQ;IAAK;AACpD;AAOO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY;IAAS,GAAG,MAAM,CAAC;AAEhF", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: ButtonProps) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACS;IACZ,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/vehicles/date-range-modal.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useEffect, useState, useMemo } from \"react\"\r\nimport { DateRange, type RangeKeyDict } from \"react-date-range\"\r\nimport { /* addDays, */ differenceInDays, format } from \"date-fns\"\r\nimport { es } from \"date-fns/locale\"\r\nimport { <PERSON><PERSON>, DialogContent, DialogTrigger, <PERSON><PERSON>Title, DialogFooter } from \"@/components/ui/dialog\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { CalendarIcon, Info } from \"lucide-react\"\r\nimport \"react-date-range/dist/styles.css\"\r\nimport \"react-date-range/dist/theme/default.css\"\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\nimport toast from \"react-hot-toast\"\r\nimport { normalizeDates, isDateDisabled } from \"@/lib/utils\"\r\n\r\ninterface DateRangeModalProps {\r\n  unavailableDates?: string[] // ISO date strings\r\n  onChange: (range: { startDate: Date; endDate: Date }) => void\r\n  initialDateRange?: { startDate: Date; endDate: Date }\r\n  minimumRentalNights?: number\r\n  maximumRentalNights?: number\r\n}\r\n\r\nexport default function DateRangeModal({\r\n  unavailableDates = [],\r\n  onChange,\r\n  initialDateRange,\r\n  // minimumRentalNights = 1,\r\n  // maximumRentalDays = 30\r\n  minimumRentalNights = 1,\r\n  maximumRentalNights = 30\r\n}: DateRangeModalProps) {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const unavailableDates2 = useMemo(() => {\r\n    if (!unavailableDates) return [];\r\n    return normalizeDates(unavailableDates);\r\n  }, [unavailableDates]);\r\n  // Validar initialDateRange antes de usarlo\r\n  const validInitialDateRange = initialDateRange &&\r\n    initialDateRange.startDate instanceof Date &&\r\n    initialDateRange.endDate instanceof Date &&\r\n    !isNaN(initialDateRange.startDate.getTime()) &&\r\n    !isNaN(initialDateRange.endDate.getTime())\r\n    ? initialDateRange\r\n    : null;\r\n\r\n  // Función para encontrar el próximo rango disponible\r\n  const findNextAvailableRange = () => {\r\n    // Comenzar desde mañana\r\n    const tomorrow = new Date()\r\n    tomorrow.setDate(tomorrow.getDate() + 1)\r\n    tomorrow.setHours(0, 0, 0, 0)\r\n\r\n    let startDate = new Date(tomorrow)\r\n\r\n    // Buscar el próximo día disponible\r\n    while (isDateDisabledHandler(startDate)) {\r\n      startDate.setDate(startDate.getDate() + 1)\r\n    }\r\n\r\n    // Para representar N días, necesitamos N+1 días\r\n    // Por ejemplo, para 3 días: check-in, día 1, día 2, día 3, check-out\r\n    let endDate = new Date(startDate)\r\n    let consecutiveDays = 0\r\n\r\n    // Necesitamos minimumRentalNights + 1 días para representar minimumRentalNights días\r\n    while (consecutiveDays < minimumRentalNights) {\r\n      endDate.setDate(endDate.getDate() + 1)\r\n\r\n      if (!isDateDisabledHandler(endDate)) {\r\n        consecutiveDays++\r\n      } else {\r\n        // Si encontramos un día no disponible, reiniciamos la búsqueda\r\n        startDate = new Date(endDate)\r\n        startDate.setDate(startDate.getDate() + 1)\r\n        endDate = new Date(startDate)\r\n        consecutiveDays = 0\r\n      }\r\n    }\r\n\r\n    return { startDate, endDate }\r\n  }\r\n\r\n  // Convert ISO strings to Date objects\r\n  // const disabledDates = useMemo(() => {\r\n  //   return normalizeDates(unavailableDates);\r\n  // }, [unavailableDates]);\r\n\r\n\r\n  // Función para verificar si una fecha está deshabilitada\r\n  const isDateDisabledHandler = (date: Date) => {\r\n    return isDateDisabled(date, unavailableDates2);\r\n  };\r\n\r\n  // Usar el rango de fechas inicial validado o encontrar el próximo disponible\r\n  const [state, setState] = useState<RangeKeyDict['selection'][]>(() => {\r\n    if (validInitialDateRange) {\r\n      // Verificar que el rango inicial no incluye el día actual\r\n      const tomorrow = new Date()\r\n      tomorrow.setDate(tomorrow.getDate() + 1)\r\n      tomorrow.setHours(0, 0, 0, 0)\r\n\r\n      if (validInitialDateRange.startDate.getTime() <= tomorrow.getTime() - 1) {\r\n        // Si incluye el día actual, buscar el próximo rango disponible\r\n        const { startDate, endDate } = findNextAvailableRange()\r\n        return [{\r\n          startDate,\r\n          endDate,\r\n          key: \"selection\",\r\n        }]\r\n      }\r\n\r\n      return [{\r\n        startDate: validInitialDateRange.startDate,\r\n        endDate: validInitialDateRange.endDate,\r\n        key: \"selection\",\r\n      }]\r\n    } else {\r\n      const { startDate, endDate } = findNextAvailableRange()\r\n      return [{\r\n        startDate,\r\n        endDate,\r\n        key: \"selection\",\r\n      }]\r\n    }\r\n  })\r\n\r\n  const handleSelect = (ranges: RangeKeyDict) => {\r\n    const selection = ranges.selection\r\n\r\n    // Solo validar cuando ambas fechas estén seleccionadas y sean diferentes\r\n    if (selection.startDate &&\r\n      selection.endDate &&\r\n      selection.startDate.getTime() !== selection.endDate.getTime()) {\r\n\r\n      // Para N días, necesitamos N+1 días\r\n      // Por ejemplo, check-in el día 1, check-out el día 4 = 3 días\r\n      const diffDays = differenceInDays(selection.endDate, selection.startDate)\r\n\r\n      if (diffDays < minimumRentalNights) {\r\n        toast.error(`La reserva debe ser de al menos ${minimumRentalNights} días`)\r\n        // Mantener la selección anterior\r\n        return\r\n      }\r\n\r\n      if (diffDays > maximumRentalNights) {\r\n        toast.error(`La reserva no puede exceder ${maximumRentalNights} días`)\r\n        // Mantener la selección anterior\r\n        return\r\n      }\r\n    }\r\n\r\n    setState([selection])\r\n  }\r\n\r\n  const handleApply = () => {\r\n    if (state[0].startDate && state[0].endDate) {\r\n      // Verificar nuevamente antes de aplicar\r\n      const diffDays = differenceInDays(state[0].endDate, state[0].startDate)\r\n\r\n      if (diffDays < minimumRentalNights) {\r\n        toast.error(`La reserva debe ser de al menos ${minimumRentalNights} días`)\r\n        return\r\n      }\r\n\r\n      if (diffDays > maximumRentalNights) {\r\n        toast.error(`La reserva no puede exceder ${maximumRentalNights} días`)\r\n        return\r\n      }\r\n\r\n      onChange({\r\n        startDate: state[0].startDate,\r\n        endDate: state[0].endDate,\r\n      })\r\n    }\r\n    setIsOpen(false)\r\n  }\r\n\r\n  // Responsive direction\r\n  const [direction, setDirection] = useState<\"horizontal\" | \"vertical\">(\"horizontal\")\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setDirection(window.innerWidth < 768 ? \"vertical\" : \"horizontal\")\r\n    }\r\n\r\n    handleResize()\r\n    window.addEventListener(\"resize\", handleResize)\r\n    return () => window.removeEventListener(\"resize\", handleResize)\r\n  }, [])\r\n\r\n  // Calcular días\r\n  const nights = state[0].startDate && state[0].endDate\r\n    ? differenceInDays(state[0].endDate, state[0].startDate)\r\n    : 0\r\n\r\n  // Formatear fechas para mostrar\r\n  const fechaRecogida = state[0].startDate ? format(state[0].startDate, \"dd 'de' MMMM, yyyy\", { locale: es }) : \"\"\r\n  const fechaDevolucion = state[0].endDate ? format(state[0].endDate, \"dd 'de' MMMM, yyyy\", { locale: es }) : \"\"\r\n\r\n  // Horarios por defecto\r\n  const horaRecogida = \"14:00\"\r\n  const horaDevolucion = \"12:00\"\r\n  const tolerancia = \"30 minutos\"\r\n\r\n  // Asegurar que el rango inicial cumpla con el mínimo de días\r\n  useEffect(() => {\r\n    if (validInitialDateRange) {\r\n      const diffDays = differenceInDays(validInitialDateRange.endDate, validInitialDateRange.startDate) + 1\r\n      if (diffDays < minimumRentalNights) {\r\n        // Si el rango inicial no cumple con el mínimo, ajustarlo\r\n        const { startDate, endDate } = findNextAvailableRange()\r\n        setState([{\r\n          startDate,\r\n          endDate,\r\n          key: \"selection\"\r\n        }])\r\n      }\r\n    }\r\n  }, [validInitialDateRange, minimumRentalNights, unavailableDates])\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\r\n      <DialogTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          className=\"w-full justify-start text-left font-normal h-auto py-2 px-3\"\r\n          onClick={() => setIsOpen(true)}\r\n        >\r\n          <CalendarIcon className=\"mr-2 h-4 w-4 text-gray-500\" />\r\n          <span>\r\n            {state[0].startDate && state[0].endDate\r\n              ? `${format(state[0].startDate, \"MMM dd, yyyy\")} - ${format(state[0].endDate, \"MMM dd, yyyy\")}`\r\n              : \"Selecciona las fechas\"}\r\n          </span>\r\n        </Button>\r\n      </DialogTrigger>\r\n\r\n      <DialogContent\r\n        className=\"sm:max-w-[725px] p-0 h-fit max-h-[90vh] overflow-y-auto\"\r\n      >\r\n        <DialogTitle asChild>\r\n          <div className=\"p-4 border-b\">\r\n            <div className=\"flex items-center\">\r\n              <h2 className=\"text-lg font-semibold\">Selecciona el rango de fechas</h2>\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <Button variant=\"ghost\" size=\"icon\" className=\"ml-2\">\r\n                      <Info className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent side=\"top\" align=\"center\" className=\"max-w-xs\">\r\n                    <p>\r\n                      {minimumRentalNights > 1\r\n                        ? `La reserva debe ser de al menos ${minimumRentalNights} días.`\r\n                        : \"Selecciona las fechas de tu reserva.\"}\r\n                    </p>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col gap-1 mt-2\">\r\n              <div className=\"flex justify-between text-sm text-gray-500\">\r\n                <span>Fecha de recogida</span>\r\n                <span>Fecha de devolución</span>\r\n              </div>\r\n\r\n              {state[0].startDate && state[0].endDate && (\r\n                <div className=\"text-xs text-gray-600 mt-1 bg-gray-100 rounded p-2\">\r\n                  <span>\r\n                    Puedes recoger el vehículo el <b>{fechaRecogida}</b> a las <b>{horaRecogida}</b>.<br />\r\n                    Debes devolverlo el <b>{fechaDevolucion}</b> a más tardar a las <b>{horaDevolucion}</b> (con tolerancia de {tolerancia}).\r\n                  </span>\r\n                  <br />\r\n                  <span>\r\n                    Aunque el rango seleccionado abarca {nights + 1} días, la reserva corresponde a <b>{nights} {nights === 1 ? 'día' : 'días'}</b> de uso efectivo.\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </DialogTitle>\r\n\r\n        <div\r\n          id=\"date-range-container\"\r\n          className=\"p-4 w-full overflow-x-auto flex justify-center\"\r\n          style={{ overflowY: \"visible\", whiteSpace: \"nowrap\" }}\r\n        >\r\n          <div className=\"inline-block\">\r\n            <DateRange\r\n              onChange={handleSelect}\r\n              moveRangeOnFirstSelection={false}\r\n              ranges={state}\r\n              months={2}\r\n              direction={direction}\r\n              locale={es}\r\n              disabledDay={isDateDisabledHandler}\r\n              minDate={new Date()}\r\n              rangeColors={['#1a2b5e']}\r\n              className=\"border rounded-md\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter className=\"p-4 bg-gray-50 border-t\">\r\n          <div className=\"flex justify-between items-center w-full\">\r\n            <div className=\"flex items-center\">\r\n              <span className=\"font-medium\">Duración de la reserva:</span>\r\n            </div>\r\n            <span className=\"font-bold\">{nights} {nights === 1 ? 'día' : 'días'}</span>\r\n          </div>\r\n          <div className=\"flex justify-end gap-2 mt-4 w-full\">\r\n            <Button variant=\"outline\" onClick={() => setIsOpen(false)}>\r\n              Cancelar\r\n            </Button>\r\n            <Button className=\"bg-[#1a2b5e] hover:bg-[#152348]\" onClick={handleApply}>\r\n              <span className=\"font-medium\">Aplicar</span>\r\n            </Button>\r\n          </div>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAGA;AAMA;AACA;;;AAlBA;;;;;;;;;;;;;AA4Be,SAAS,eAAe,EACrC,mBAAmB,EAAE,EACrB,QAAQ,EACR,gBAAgB,EAChB,2BAA2B;AAC3B,yBAAyB;AACzB,sBAAsB,CAAC,EACvB,sBAAsB,EAAE,EACJ;;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAChC,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAChC,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;oDAAG;QAAC;KAAiB;IACrB,2CAA2C;IAC3C,MAAM,wBAAwB,oBAC5B,iBAAiB,SAAS,YAAY,QACtC,iBAAiB,OAAO,YAAY,QACpC,CAAC,MAAM,iBAAiB,SAAS,CAAC,OAAO,OACzC,CAAC,MAAM,iBAAiB,OAAO,CAAC,OAAO,MACrC,mBACA;IAEJ,qDAAqD;IACrD,MAAM,yBAAyB;QAC7B,wBAAwB;QACxB,MAAM,WAAW,IAAI;QACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;QAE3B,IAAI,YAAY,IAAI,KAAK;QAEzB,mCAAmC;QACnC,MAAO,sBAAsB,WAAY;YACvC,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAC1C;QAEA,gDAAgD;QAChD,qEAAqE;QACrE,IAAI,UAAU,IAAI,KAAK;QACvB,IAAI,kBAAkB;QAEtB,qFAAqF;QACrF,MAAO,kBAAkB,oBAAqB;YAC5C,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;YAEpC,IAAI,CAAC,sBAAsB,UAAU;gBACnC;YACF,OAAO;gBACL,+DAA+D;gBAC/D,YAAY,IAAI,KAAK;gBACrB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;gBACxC,UAAU,IAAI,KAAK;gBACnB,kBAAkB;YACpB;QACF;QAEA,OAAO;YAAE;YAAW;QAAQ;IAC9B;IAEA,sCAAsC;IACtC,wCAAwC;IACxC,6CAA6C;IAC7C,0BAA0B;IAG1B,yDAAyD;IACzD,MAAM,wBAAwB,CAAC;QAC7B,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;IAC9B;IAEA,6EAA6E;IAC7E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;mCAA+B;YAC9D,IAAI,uBAAuB;gBACzB,0DAA0D;gBAC1D,MAAM,WAAW,IAAI;gBACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;gBACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;gBAE3B,IAAI,sBAAsB,SAAS,CAAC,OAAO,MAAM,SAAS,OAAO,KAAK,GAAG;oBACvE,+DAA+D;oBAC/D,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;oBAC/B,OAAO;wBAAC;4BACN;4BACA;4BACA,KAAK;wBACP;qBAAE;gBACJ;gBAEA,OAAO;oBAAC;wBACN,WAAW,sBAAsB,SAAS;wBAC1C,SAAS,sBAAsB,OAAO;wBACtC,KAAK;oBACP;iBAAE;YACJ,OAAO;gBACL,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;gBAC/B,OAAO;oBAAC;wBACN;wBACA;wBACA,KAAK;oBACP;iBAAE;YACJ;QACF;;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY,OAAO,SAAS;QAElC,yEAAyE;QACzE,IAAI,UAAU,SAAS,IACrB,UAAU,OAAO,IACjB,UAAU,SAAS,CAAC,OAAO,OAAO,UAAU,OAAO,CAAC,OAAO,IAAI;YAE/D,oCAAoC;YACpC,8DAA8D;YAC9D,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,OAAO,EAAE,UAAU,SAAS;YAExE,IAAI,WAAW,qBAAqB;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,oBAAoB,KAAK,CAAC;gBACzE,iCAAiC;gBACjC;YACF;YAEA,IAAI,WAAW,qBAAqB;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,oBAAoB,KAAK,CAAC;gBACrE,iCAAiC;gBACjC;YACF;QACF;QAEA,SAAS;YAAC;SAAU;IACtB;IAEA,MAAM,cAAc;QAClB,IAAI,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE;YAC1C,wCAAwC;YACxC,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS;YAEtE,IAAI,WAAW,qBAAqB;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,oBAAoB,KAAK,CAAC;gBACzE;YACF;YAEA,IAAI,WAAW,qBAAqB;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,oBAAoB,KAAK,CAAC;gBACrE;YACF;YAEA,SAAS;gBACP,WAAW,KAAK,CAAC,EAAE,CAAC,SAAS;gBAC7B,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO;YAC3B;QACF;QACA,UAAU;IACZ;IAEA,uBAAuB;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;yDAAe;oBACnB,aAAa,OAAO,UAAU,GAAG,MAAM,aAAa;gBACtD;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;mCAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,GACjD,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,IACrD;IAEJ,gCAAgC;IAChC,MAAM,gBAAgB,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,sBAAsB;QAAE,QAAQ,8IAAA,CAAA,KAAE;IAAC,KAAK;IAC9G,MAAM,kBAAkB,KAAK,CAAC,EAAE,CAAC,OAAO,GAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,sBAAsB;QAAE,QAAQ,8IAAA,CAAA,KAAE;IAAC,KAAK;IAE5G,uBAAuB;IACvB,MAAM,eAAe;IACrB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IAEnB,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,uBAAuB;gBACzB,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,sBAAsB,OAAO,EAAE,sBAAsB,SAAS,IAAI;gBACpG,IAAI,WAAW,qBAAqB;oBAClC,yDAAyD;oBACzD,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;oBAC/B,SAAS;wBAAC;4BACR;4BACA;4BACA,KAAK;wBACP;qBAAE;gBACJ;YACF;QACF;mCAAG;QAAC;QAAuB;QAAqB;KAAiB;IAEjE,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;;0BAClC,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;oBACV,SAAS,IAAM,UAAU;;sCAEzB,6LAAC,iNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;sCACxB,6LAAC;sCACE,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,GACnC,GAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,gBAAgB,GAAG,EAAE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,GAC7F;;;;;;;;;;;;;;;;;0BAKV,6LAAC,qIAAA,CAAA,gBAAa;gBACZ,WAAU;;kCAEV,6LAAC,qIAAA,CAAA,cAAW;wBAAC,OAAO;kCAClB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC,sIAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;kEACN,6LAAC,sIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;4DAAO,WAAU;sEAC5C,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGpB,6LAAC,sIAAA,CAAA,iBAAc;wDAAC,MAAK;wDAAM,OAAM;wDAAS,WAAU;kEAClD,cAAA,6LAAC;sEACE,sBAAsB,IACnB,CAAC,gCAAgC,EAAE,oBAAoB,MAAM,CAAC,GAC9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;wCAGP,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,kBACrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAK;sEAC0B,6LAAC;sEAAG;;;;;;wDAAkB;sEAAO,6LAAC;sEAAG;;;;;;wDAAiB;sEAAC,6LAAC;;;;;wDAAK;sEACnE,6LAAC;sEAAG;;;;;;wDAAoB;sEAAoB,6LAAC;sEAAG;;;;;;wDAAmB;wDAAqB;wDAAW;;;;;;;8DAEzH,6LAAC;;;;;8DACD,6LAAC;;wDAAK;wDACiC,SAAS;wDAAE;sEAAgC,6LAAC;;gEAAG;gEAAO;gEAAE,WAAW,IAAI,QAAQ;;;;;;;wDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3I,6LAAC;wBACC,IAAG;wBACH,WAAU;wBACV,OAAO;4BAAE,WAAW;4BAAW,YAAY;wBAAS;kCAEpD,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0JAAA,CAAA,YAAS;gCACR,UAAU;gCACV,2BAA2B;gCAC3B,QAAQ;gCACR,QAAQ;gCACR,WAAW;gCACX,QAAQ,8IAAA,CAAA,KAAE;gCACV,aAAa;gCACb,SAAS,IAAI;gCACb,aAAa;oCAAC;iCAAU;gCACxB,WAAU;;;;;;;;;;;;;;;;kCAKhB,6LAAC,qIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;kDAEhC,6LAAC;wCAAK,WAAU;;4CAAa;4CAAO;4CAAE,WAAW,IAAI,QAAQ;;;;;;;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,UAAU;kDAAQ;;;;;;kDAG3D,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;wCAAkC,SAAS;kDAC3D,cAAA,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GA9SwB;KAAA", "debugId": null}}, {"offset": {"line": 1751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/vehicles/vehicle-gallery.tsx"], "sourcesContent": ["'use client'\n\nimport { DateTime } from 'luxon'\nimport Image from 'next/image'\nimport React from 'react'\n\ninterface VehicleGalleryProps {\n  vehicle: any\n  images: string[]\n}\n\nexport function VehicleGallery({ vehicle, images, /* currentImageIndex, setCurrentImageIndex */ }: VehicleGalleryProps) {\n\n  const [currentImageIndex, setCurrentImageIndex] = React.useState(0)\n  const [isHovered, setIsHovered] = React.useState(false)\n  const isRecent = (createdAt: string) => {\n    const createdDate = DateTime.fromISO(createdAt);\n    const sevenDaysAgo = DateTime.now().minus({ days: 7 });\n    return createdDate > sevenDaysAgo;\n  }\n\n  return (\n    <>\n      <div className=\"relative\" >\n\n        {isRecent(vehicle.createdAt) && (\n          <div className=\"absolute top-4 left-4 z-10\">\n            <span className=\"bg-blue-500 text-white text-xs px-2 py-1 rounded-md\">\n              Recién publicado\n            </span>\n          </div>\n        )}\n\n        {/* Imagen principal */}\n        <div className=\"relative aspect-[4/3] bg-gray-100 rounded-lg overflow-hidden\" onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>\n          <Image\n            src={images[currentImageIndex] || \"/placeholder.svg\"}\n            alt={`${vehicle.make} ${vehicle.model}`}\n            fill\n            className=\"object-cover\"\n          />\n\n          {/* Controles de navegación */}\n          <div\n            className=\"absolute bottom-4 right-4 bg-white/80 rounded-full px-3 py-1 text-sm\"\n            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}\n          >\n            {currentImageIndex + 1} / {images.length}\n          </div>\n\n          <button\n            className=\"absolute left-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2\"\n            onClick={() => {\n              setCurrentImageIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1))\n            }}\n            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M15 18L9 12L15 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            </svg>\n          </button>\n\n          <button\n            className=\"absolute right-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2\"\n            onClick={() => setCurrentImageIndex(prev => (prev < images.length - 1 ? prev + 1 : 0))}\n            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9 18L15 12L9 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Miniaturas */}\n        <div className=\"grid grid-cols-6 gap-2 mt-2\">\n          {images.slice(0, 6).map((image, index) => (\n            <div\n              key={index}\n              className={`aspect-[4/3] rounded-md overflow-hidden cursor-pointer border-2 ${index === currentImageIndex ? 'border-blue-500' : 'border-transparent'\n                }`}\n              onClick={() => setCurrentImageIndex(index)}\n            >\n              <Image\n                src={image || \"/placeholder.svg\"}\n                alt={`${vehicle.make} ${vehicle.model} - Vista ${index + 1}`}\n                width={100}\n                height={75}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ))}\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,EAAE,OAAO,EAAE,MAAM,EAAsE;;IAEpH,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,WAAW,CAAC;QAChB,MAAM,cAAc,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC;QACrC,MAAM,eAAe,kLAAA,CAAA,WAAQ,CAAC,GAAG,GAAG,KAAK,CAAC;YAAE,MAAM;QAAE;QACpD,OAAO,cAAc;IACvB;IAEA,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;gBAEZ,SAAS,QAAQ,SAAS,mBACzB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAsD;;;;;;;;;;;8BAO1E,6LAAC;oBAAI,WAAU;oBAA+D,cAAc,IAAM,aAAa;oBAAO,cAAc,IAAM,aAAa;;sCACrJ,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,MAAM,CAAC,kBAAkB,IAAI;4BAClC,KAAK,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;4BACvC,IAAI;4BACJ,WAAU;;;;;;sCAIZ,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,SAAS,YAAY,IAAI;gCAAG,YAAY;4BAAoB;;gCAEpE,oBAAoB;gCAAE;gCAAI,OAAO,MAAM;;;;;;;sCAG1C,6LAAC;4BACC,WAAU;4BACV,SAAS;gCACP,qBAAqB,CAAC,OAAU,OAAO,IAAI,OAAO,IAAI,OAAO,MAAM,GAAG;4BACxE;4BACA,OAAO;gCAAE,SAAS,YAAY,IAAI;gCAAG,YAAY;4BAAoB;sCAErE,cAAA,6LAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;gCAAO,OAAM;0CAChE,cAAA,6LAAC;oCAAK,GAAE;oCAAmB,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;;;;;;;;;;;sCAI1G,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,qBAAqB,CAAA,OAAS,OAAO,OAAO,MAAM,GAAG,IAAI,OAAO,IAAI;4BACnF,OAAO;gCAAE,SAAS,YAAY,IAAI;gCAAG,YAAY;4BAAoB;sCAErE,cAAA,6LAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;gCAAO,OAAM;0CAChE,cAAA,6LAAC;oCAAK,GAAE;oCAAkB,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;8BAM3G,6LAAC;oBAAI,WAAU;8BACZ,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC;4BAEC,WAAW,CAAC,gEAAgE,EAAE,UAAU,oBAAoB,oBAAoB,sBAC5H;4BACJ,SAAS,IAAM,qBAAqB;sCAEpC,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,SAAS;gCACd,KAAK,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,SAAS,EAAE,QAAQ,GAAG;gCAC5D,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;2BAVP;;;;;;;;;;;;;;;;;AAkBnB;GApFgB;KAAA", "debugId": null}}, {"offset": {"line": 1949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/context/user-context.tsx"], "sourcesContent": ["'use client';\r\nimport { Session } from 'better-auth/types';\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\n\r\n\r\ninterface UserContextType {\r\n  user: User;\r\n  setUser: React.Dispatch<React.SetStateAction<User>>;\r\n  session: Session;\r\n  setSession: React.Dispatch<React.SetStateAction<any>>;\r\n}\r\n\r\nexport const UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children, session: sessionData }: { children: React.ReactNode; session: { user: User; session: Session } }) {\r\n\r\n  // console.log('sessionData: ', sessionData.user);\r\n  const [session, setSession] = useState(sessionData?.session);\r\n  const [user, setUser] = useState(sessionData?.user);\r\n\r\n  useEffect(() => {\r\n    if (sessionData) {\r\n      setUser(sessionData.user);\r\n      setSession(sessionData.session);\r\n    }\r\n  }, [sessionData]);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser, session, setSession }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error('useUser must be used within a UserProvider');\r\n  }\r\n  return context;\r\n}"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AAYO,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,WAAW,EAA4E;;IAEvI,kDAAkD;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,aAAa;gBACf,QAAQ,YAAY,IAAI;gBACxB,WAAW,YAAY,OAAO;YAChC;QACF;iCAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;YAAS;QAAW;kBAC/D;;;;;;AAGP;GAlBgB;KAAA;AAqBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 2095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium  group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 !select-text cursor-text\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qOACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 2331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/auth-client.ts"], "sourcesContent": ["import { createAuthClient } from \"better-auth/react\";\r\nimport { adminClient, /* multiSessionClient, */ organizationClient } from \"better-auth/client/plugins\";\r\nimport { getCookie } from 'cookies-next/client';\r\n\r\nimport env from \"@/constants/env\";\r\nimport { BEARER_COOKIE_NAME } from './constants';\r\n\r\nexport const authClient = createAuthClient({\r\n  baseURL: env.NEXT_PUBLIC_API_URL,\r\n\r\n  plugins: [\r\n    adminClient(),\r\n    organizationClient(),\r\n    // multiSessionClient()\r\n  ],\r\n  credentials: 'include',\r\n  fetchOptions: {\r\n    onError: (ctx) => {\r\n      console.log('Error:', ctx.error);\r\n      console.log('Response:', ctx.response.url);\r\n    },\r\n    headers: {\r\n      'x-dashboard-call': 'true'\r\n    },\r\n    auth: {\r\n      type: 'Bearer',\r\n      token: () => {\r\n        const token = getCookie(BEARER_COOKIE_NAME);\r\n        if (token) {\r\n          return token; // No truncar el token\r\n        }\r\n      }\r\n    }\r\n  },\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAEA;AACA;;;;;;AAEO,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,SAAS,0HAAA,CAAA,UAAG,CAAC,mBAAmB;IAEhC,SAAS;QACP,CAAA,GAAA,wLAAA,CAAA,cAAW,AAAD;QACV,CAAA,GAAA,wLAAA,CAAA,qBAAkB,AAAD;KAElB;IACD,aAAa;IACb,cAAc;QACZ,SAAS,CAAC;YACR,QAAQ,GAAG,CAAC,UAAU,IAAI,KAAK;YAC/B,QAAQ,GAAG,CAAC,aAAa,IAAI,QAAQ,CAAC,GAAG;QAC3C;QACA,SAAS;YACP,oBAAoB;QACtB;QACA,MAAM;YACJ,MAAM;YACN,OAAO;gBACL,MAAM,QAAQ,CAAA,GAAA,4JAAA,CAAA,YAAS,AAAD,EAAE,4HAAA,CAAA,qBAAkB;gBAC1C,IAAI,OAAO;oBACT,OAAO,OAAO,sBAAsB;gBACtC;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/auth/login-form.tsx"], "sourcesContent": ["\"use client\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport { useState } from \"react\";\nimport { Loader2 } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useRouter } from 'next/navigation';\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport * as z from \"zod\";\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\nimport { toast } from \"react-hot-toast\";\nimport { authClient } from '@/auth-client';\n\nconst signInSchema = z.object({\n  email: z.string().email({ message: \"Correo electrónico inválido\" }),\n  password: z.string().min(1, { message: \"La contraseña es requerida\" }),\n  rememberMe: z.boolean().optional(),\n});\n\ninterface LoginFormProps {\n  onSuccess?: () => void\n  redirectAfterLogin?: boolean\n  onRegisterClick?: () => void\n  callbackUrl?: string\n}\n\ntype SignInFormValues = z.infer<typeof signInSchema>;\n\nexport function LoginForm({ onSuccess, onRegisterClick, redirectAfterLogin = true }: LoginFormProps) {\n  const [loading, setLoading] = useState(false);\n  const router = useRouter();\n\n  const form = useForm<SignInFormValues>({\n    resolver: zodResolver(signInSchema),\n    defaultValues: {\n      email: \"\",\n      password: \"\",\n      rememberMe: false,\n    },\n  });\n\n  const onSubmit = async (data: SignInFormValues) => {\n    try {\n      await authClient.signIn.email(\n        {\n          email: data.email,\n          password: data.password,\n          callbackURL: window.location.origin + \"/dashboard\"\n\n        },\n        {\n          onRequest: () => {\n            setLoading(true);\n          },\n          onResponse: () => {\n            setLoading(false);\n          },\n          onError: (ctx) => {\n            console.log('Error: ', ctx);\n            toast.error(ctx.error.message || \"Error al iniciar sesión\");\n          },\n          onSuccess: async () => {\n\n            // router.push(\"/dashboard\");\n            if (onSuccess) {\n              onSuccess()\n            }\n\n            if (redirectAfterLogin) {\n              toast.success(\"Inicio de sesión exitoso. Redirigiendo al dashboard...\")\n              // Redirigir a la página de reserva si hay una redirección pendiente\n              router.push('/dashboard')\n            }\n          },\n        },\n      );\n    } catch (error) {\n      setLoading(false);\n      toast.error(\"Error al iniciar sesión\");\n      console.log('error: ', error)\n    }\n  };\n\n  return (\n    <>\n      <Card className=\"z-50 rounded-md max-w-md w-full\">\n        <CardHeader>\n          <CardTitle className=\"text-lg md:text-xl\">Iniciar Sesión</CardTitle>\n          <CardDescription className=\"text-xs md:text-sm\">\n            Ingresa tus credenciales para acceder a DriveLink\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Form {...form}>\n            <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n              <FormField\n                control={form.control}\n                name=\"email\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Correo electrónico</FormLabel>\n                    <FormControl>\n                      <Input\n                        type=\"email\"\n                        placeholder=\"<EMAIL>\"\n                        {...field}\n                      />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"password\"\n                render={({ field }) => (\n                  <FormItem>\n                    <div className=\"flex items-center justify-between\">\n                      <FormLabel>Contraseña</FormLabel>\n                      <Link\n                        href=\"/forgot-password\"\n                        className=\"text-xs text-primary hover:underline\"\n                      >\n                        ¿Olvidaste tu contraseña?\n                      </Link>\n                    </div>\n                    <FormControl>\n                      <Input\n                        type=\"password\"\n                        placeholder=\"Contraseña\"\n                        {...field}\n                      />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"rememberMe\"\n                render={({ field }) => (\n                  <FormItem className=\"flex flex-row items-center space-x-2 space-y-0\">\n                    <FormControl>\n                      <Checkbox\n                        checked={field.value}\n                        onCheckedChange={field.onChange}\n                      />\n                    </FormControl>\n                    <FormLabel className=\"text-sm font-normal\">Recordarme</FormLabel>\n                  </FormItem>\n                )}\n              />\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <Loader2 size={16} className=\"animate-spin\" />\n                ) : (\n                  \"Iniciar Sesión\"\n                )}\n              </Button>\n            </form>\n          </Form>\n\n          <div className=\"relative my-4\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <span className=\"w-full border-t\"></span>\n            </div>\n            <div className=\"relative flex justify-center text-xs uppercase\">\n              <span className=\"bg-background px-2 text-muted-foreground\">O continúa con</span>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Button\n              variant=\"outline\"\n              className=\"w-full gap-2\"\n              disabled={loading}\n              onClick={async () => {\n                try {\n                  await authClient.signIn.social(\n                    {\n                      provider: \"google\",\n                      callbackURL: window.location.origin + \"/dashboard\"\n                    },\n                    {\n                      onRequest: () => {\n                        setLoading(true);\n                      },\n                      onResponse: () => {\n                        setLoading(false);\n                      },\n                      onError: (ctx) => {\n                        toast.error(ctx.error.message || \"Error al iniciar sesión con Google\");\n                      },\n                    },\n                  );\n                } catch (error) {\n                  setLoading(false);\n                  toast.error(\"Error al iniciar sesión con Google\");\n                  console.log('error: ', error)\n                }\n              }}\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"0.98em\" height=\"1em\" viewBox=\"0 0 256 262\">\n                <path fill=\"#4285F4\" d=\"M255.878 133.451c0-10.734-.871-18.567-2.756-26.69H130.55v48.448h71.947c-1.45 12.04-9.283 30.172-26.69 42.356l-.244 1.622l38.755 30.023l2.685.268c24.659-22.774 38.875-56.282 38.875-96.027\"></path>\n                <path fill=\"#34A853\" d=\"M130.55 261.1c35.248 0 64.839-11.605 86.453-31.622l-41.196-31.913c-11.024 7.688-25.82 13.055-45.257 13.055c-34.523 0-63.824-22.773-74.269-54.25l-1.531.13l-40.298 31.187l-.527 1.465C35.393 231.798 79.49 261.1 130.55 261.1\"></path>\n                <path fill=\"#FBBC05\" d=\"M56.281 156.37c-2.756-8.123-4.351-16.827-4.351-25.82c0-8.994 1.595-17.697 4.206-25.82l-.073-1.73L15.26 71.312l-1.335.635C5.077 89.644 0 109.517 0 130.55s5.077 40.905 13.925 58.602z\"></path>\n                <path fill=\"#EB4335\" d=\"M130.55 50.479c24.514 0 41.05 10.589 50.479 19.438l36.844-35.974C195.245 12.91 165.798 0 130.55 0C79.49 0 35.393 29.301 13.925 71.947l42.211 32.783c10.59-31.477 39.891-54.251 74.414-54.251\"></path>\n              </svg>\n              Iniciar con Google\n            </Button>\n            <Button\n              variant=\"outline\"\n              className=\"w-full gap-2\"\n              disabled={loading}\n              onClick={async () => {\n                try {\n                  await authClient.signIn.social(\n                    {\n                      provider: \"facebook\",\n                      callbackURL: \"/dashboard\"\n                    },\n                    {\n                      onRequest: () => {\n                        setLoading(true);\n                      },\n                      onResponse: () => {\n                        setLoading(false);\n                      },\n                      onError: (ctx) => {\n                        toast.error(ctx.error.message || \"Error al iniciar sesión con Facebook\");\n                      },\n                    },\n                  );\n                } catch (error) {\n                  setLoading(false);\n                  toast.error(\"Error al iniciar sesión con Facebook\");\n                  console.log('error: ', error)\n                }\n              }}\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"1em\"\n                height=\"1em\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  d=\"M20 3H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h8.615v-6.96h-2.338v-2.725h2.338v-2c0-2.325 1.42-3.592 3.5-3.592c.699-.002 1.399.034 2.095.107v2.42h-1.435c-1.128 0-1.348.538-1.348 1.325v1.735h2.697l-.35 2.725h-2.348V21H20a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1z\"\n                  fill=\"currentColor\"\n                ></path>\n              </svg>\n              Iniciar con Facebook\n            </Button>\n          </div>\n        </CardContent>\n        <CardFooter>\n          <div className=\"flex justify-center w-full\">\n            <p className=\"text-center text-xs text-neutral-500\">\n              ¿No tienes una cuenta?{\" \"}\n\n              {\n                onRegisterClick ? (\n                  <Button\n                    variant=\"link\"\n                    className=\"p-0\"\n                    onClick={onRegisterClick}\n                  >\n                    Crear Cuenta\n                  </Button>\n                ) : (\n                    <Link\n                      href=\"/sign-up\"\n                      className=\"underline\"\n                    >\n                      Crear Cuenta\n                    </Link>\n                )\n              }\n            </p>\n          </div>\n        </CardFooter>\n      </Card>\n    </>\n  )\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;;;AAgBA,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;IAC5B,OAAO,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAA8B;IACjE,UAAU,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;IACpE,YAAY,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;AAClC;AAWO,SAAS,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,qBAAqB,IAAI,EAAkB;;IACjG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,UAAU;YACV,YAAY;QACd;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,CAC3B;gBACE,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,aAAa,OAAO,QAAQ,CAAC,MAAM,GAAG;YAExC,GACA;gBACE,WAAW;oBACT,WAAW;gBACb;gBACA,YAAY;oBACV,WAAW;gBACb;gBACA,SAAS,CAAC;oBACR,QAAQ,GAAG,CAAC,WAAW;oBACvB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI;gBACnC;gBACA,WAAW;oBAET,6BAA6B;oBAC7B,IAAI,WAAW;wBACb;oBACF;oBAEA,IAAI,oBAAoB;wBACtB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd,oEAAoE;wBACpE,OAAO,IAAI,CAAC;oBACd;gBACF;YACF;QAEJ,EAAE,OAAO,OAAO;YACd,WAAW;YACX,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,GAAG,CAAC,WAAW;QACzB;IACF;IAEA,qBACE;kBACE,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAqB;;;;;;sCAC1C,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;sCAAqB;;;;;;;;;;;;8BAIlD,6LAAC,mIAAA,CAAA,cAAW;;sCACV,6LAAC,mIAAA,CAAA,OAAI;4BAAE,GAAG,IAAI;sCACZ,cAAA,6LAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAW,WAAU;;kDACrD,6LAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;kEACP,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACX,GAAG,KAAK;;;;;;;;;;;kEAGb,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,6LAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;kEACP,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;0EACX;;;;;;;;;;;;kEAIH,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACX,GAAG,KAAK;;;;;;;;;;;kEAGb,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,6LAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4DACP,SAAS,MAAM,KAAK;4DACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;kEAGnC,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;;;;;;;kDAKjD,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU;kDAET,wBACC,6LAAC,oNAAA,CAAA,UAAO;4CAAC,MAAM;4CAAI,WAAU;;;;;mDAE7B;;;;;;;;;;;;;;;;;sCAMR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAI/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,UAAU;oCACV,SAAS;wCACP,IAAI;4CACF,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM,CAC5B;gDACE,UAAU;gDACV,aAAa,OAAO,QAAQ,CAAC,MAAM,GAAG;4CACxC,GACA;gDACE,WAAW;oDACT,WAAW;gDACb;gDACA,YAAY;oDACV,WAAW;gDACb;gDACA,SAAS,CAAC;oDACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI;gDACnC;4CACF;wCAEJ,EAAE,OAAO,OAAO;4CACd,WAAW;4CACX,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4CACZ,QAAQ,GAAG,CAAC,WAAW;wCACzB;oCACF;;sDAEA,6LAAC;4CAAI,OAAM;4CAA6B,OAAM;4CAAS,QAAO;4CAAM,SAAQ;;8DAC1E,6LAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,6LAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,6LAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,6LAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;;;;;;;wCACnB;;;;;;;8CAGR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,UAAU;oCACV,SAAS;wCACP,IAAI;4CACF,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM,CAC5B;gDACE,UAAU;gDACV,aAAa;4CACf,GACA;gDACE,WAAW;oDACT,WAAW;gDACb;gDACA,YAAY;oDACV,WAAW;gDACb;gDACA,SAAS,CAAC;oDACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI;gDACnC;4CACF;wCAEJ,EAAE,OAAO,OAAO;4CACd,WAAW;4CACX,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4CACZ,QAAQ,GAAG,CAAC,WAAW;wCACzB;oCACF;;sDAEA,6LAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDACC,GAAE;gDACF,MAAK;;;;;;;;;;;wCAEH;;;;;;;;;;;;;;;;;;;8BAKZ,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAuC;gCAC3B;gCAGrB,gCACE,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;8CACV;;;;;yDAIC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrB;GAvQgB;;QAEC,qIAAA,CAAA,YAAS;QAEX,iKAAA,CAAA,UAAO;;;KAJN", "debugId": null}}, {"offset": {"line": 2929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2999, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/auth/register-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport {\n  <PERSON>,\n  CardContent,\n  CardDescription,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\n// import { Label } from \"@/components/ui/label\";\nimport { useState } from \"react\";\n// import Image from \"next/image\";\nimport { Loader2, /* X */ } from \"lucide-react\";\nimport { toast } from \"react-hot-toast\";\nimport { useRouter } from \"next/navigation\";\nimport Link from 'next/link';\nimport { useForm, /* Controller */ } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport * as z from \"zod\";\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\nimport { authClient } from '@/auth-client';\n\nconst signUpSchema = z.object({\n  firstName: z.string().min(2, { message: \"El nombre debe tener al menos 2 caracteres\" }),\n  lastName: z.string().min(2, { message: \"El apellido debe tener al menos 2 caracteres\" }),\n  email: z.string().email({ message: \"Correo electrónico inválido\" }),\n  password: z.string().min(8, { message: \"La contraseña debe tener al menos 8 caracteres\" }),\n  passwordConfirmation: z.string(),\n  userType: z.enum([\"host\", \"client\"], { required_error: \"Seleccione un tipo de usuario\" }),\n}).refine((data) => data.password === data.passwordConfirmation, {\n  message: \"Las contraseñas no coinciden\",\n  path: [\"passwordConfirmation\"],\n});\n\ntype SignUpFormValues = z.infer<typeof signUpSchema>;\n\ninterface RegisterFormProps {\n  onSuccess?: () => void\n  redirectAfterRegister?: boolean\n  onLoginClick?: () => void\n}\n\nexport function RegisterForm({ onSuccess, onLoginClick, redirectAfterRegister = true }: RegisterFormProps) {\n  // const [image, setImage] = useState<File | null>(null);\n  // const [imagePreview, setImagePreview] = useState<string | null>(null);\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n\n  const form = useForm<SignUpFormValues>({\n    resolver: zodResolver(signUpSchema),\n    defaultValues: {\n      firstName: \"\",\n      lastName: \"\",\n      email: \"\",\n      password: \"\",\n      passwordConfirmation: \"\",\n      userType: \"client\",\n    },\n  });\n\n  // const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n  //   const file = e.target.files?.[0];\n  //   if (file) {\n  //     setImage(file);\n  //     const reader = new FileReader();\n  //     reader.onloadend = () => {\n  //       setImagePreview(reader.result as string);\n  //     };\n  //     reader.readAsDataURL(file);\n  //   }\n  // };\n\n  // const convertImageToBase64 = async (file: File): Promise<string> => {\n  //   return new Promise((resolve, reject) => {\n  //     const reader = new FileReader();\n  //     reader.onload = () => resolve(reader.result as string);\n  //     reader.onerror = reject;\n  //     reader.readAsDataURL(file);\n  //   });\n  // };\n\n  const onSubmit = async (data: SignUpFormValues) => {\n    setLoading(true);\n    try {\n      const response = await authClient.signUp.email({\n        email: data.email,\n        password: data.password,\n        name: `${data.firstName} ${data.lastName}`,\n        // image: image ? await convertImageToBase64(image) : \"\",\n        // @ts-expect-error userType is not defined in the authClient.signUp.email type but required for backend\n        userType: data.userType,\n        // callbackURL: \"/dashboard\",\n        // callbackURL: callbackUrl || window.location.origin + \"/dashboard\",\n        // metadata: {\n        //   userType: data.userType,\n        // },\n        fetchOptions: {\n          onResponse: (ctx) => {\n            setLoading(false);\n            console.log('onResponse', ctx)\n          },\n          onRequest: () => {\n            setLoading(true);\n          },\n          onError: (ctx) => {\n            toast.error(ctx.error.message);\n          },\n          onSuccess: async () => {\n            // router.push(\"/dashboard\");\n            if (onSuccess) {\n              onSuccess()\n            } else {\n              toast.success(\"Cuenta creada exitosamente. Se envió un email de verificación a tu correo. \\n Una vez verificado, serás redirigido al dashboard.\");\n            }\n\n            if (redirectAfterRegister) {\n              router.push('/dashboard')\n            }\n          },\n        },\n      });\n      console.log('response after hooks: ', response)\n    } catch (error) {\n      setLoading(false);\n      toast.error(\"Error al crear la cuenta\");\n      console.log('error: ', error)\n    }\n  };\n\n  return (\n    <>\n      <Card className=\"z-50 rounded-md max-w-md w-full\">\n        <CardHeader>\n          <CardTitle className=\"text-lg md:text-xl\">Crear Cuenta</CardTitle>\n          <CardDescription className=\"text-xs md:text-sm\">\n            Ingresa tus datos para registrarte en DriveLink\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Form {...form}>\n            <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <FormField\n                  control={form.control}\n                  name=\"firstName\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Nombre</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"Nombre\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n                <FormField\n                  control={form.control}\n                  name=\"lastName\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Apellido</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"Apellido\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <FormField\n                control={form.control}\n                name=\"email\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Correo electrónico</FormLabel>\n                    <FormControl>\n                      <Input type=\"email\" placeholder=\"<EMAIL>\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"password\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Contraseña</FormLabel>\n                    <FormControl>\n                      <Input type=\"password\" placeholder=\"Contraseña\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"passwordConfirmation\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Confirmar Contraseña</FormLabel>\n                    <FormControl>\n                      <Input type=\"password\" placeholder=\"Confirmar Contraseña\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"userType\"\n                render={({ field }) => (\n                  <FormItem className=\"space-y-3\">\n                    <FormLabel>Tipo de Usuario</FormLabel>\n                    <FormControl>\n                      <RadioGroup\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                        className=\"flex flex-col space-y-1\"\n                      >\n                        <FormItem className=\"flex items-center space-x-3 space-y-0\">\n                          <FormControl>\n                            <RadioGroupItem value=\"client\" />\n                          </FormControl>\n                          <FormLabel className=\"font-normal\">\n                            Cliente (Quiero rentar autos)\n                          </FormLabel>\n                        </FormItem>\n                        <FormItem className=\"flex items-center space-x-3 space-y-0\">\n                          <FormControl>\n                            <RadioGroupItem value=\"host\" />\n                          </FormControl>\n                          <FormLabel className=\"font-normal\">\n                            Anfitrión (Quiero ofrecer mis autos)\n                          </FormLabel>\n                        </FormItem>\n                      </RadioGroup>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* <div className=\"space-y-2\">\n                <Label htmlFor=\"image\">Foto de Perfil (opcional)</Label>\n                <div className=\"flex items-end gap-4\">\n                  {imagePreview && (\n                    <div className=\"relative w-16 h-16 rounded-sm overflow-hidden\">\n                      <Image\n                        src={imagePreview}\n                        alt=\"Profile preview\"\n                        fill\n                        style={{ objectFit: \"cover\" }}\n                      />\n                    </div>\n                  )}\n                  <div className=\"flex items-center gap-2 w-full\">\n                    <Input\n                      id=\"image\"\n                      type=\"file\"\n                      accept=\"image/*\"\n                      onChange={handleImageChange}\n                      className=\"w-full\"\n                    />\n                    {imagePreview && (\n                      <X\n                        className=\"cursor-pointer\"\n                        onClick={() => {\n                          setImage(null);\n                          setImagePreview(null);\n                        }}\n                      />\n                    )}\n                  </div>\n                </div>\n              </div> */}\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <Loader2 size={16} className=\"animate-spin\" />\n                ) : (\n                  \"Crear Cuenta\"\n                )}\n              </Button>\n            </form>\n          </Form>\n        </CardContent>\n        <CardFooter>\n          <div className=\"flex justify-center w-full\">\n            <p className=\"text-center text-xs text-neutral-500\">\n              ¿Ya tienes una cuenta?{\" \"}\n              {\n                onLoginClick ? (\n                  <Button\n                    variant=\"link\"\n                    className=\"p-0\"\n                    onClick={onLoginClick}\n                  >\n                    Iniciar Sesión\n                  </Button>\n                ) : (\n                  <Link\n                    href=\"/sign-in\"\n                    className=\"underline\"\n                  >\n                    Iniciar Sesión\n                  </Link>\n                )\n              }\n            </p>\n          </div>\n        </CardFooter>\n      </Card>\n    </>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA,iDAAiD;AACjD;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAxBA;;;;;;;;;;;;;;;AA0BA,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;IAC5B,WAAW,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6C;IACrF,UAAU,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA+C;IACtF,OAAO,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAA8B;IACjE,UAAU,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiD;IACxF,sBAAsB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD;IAC7B,UAAU,CAAA,GAAA,uIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAQ;KAAS,EAAE;QAAE,gBAAgB;IAAgC;AACzF,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,oBAAoB,EAAE;IAC/D,SAAS;IACT,MAAM;QAAC;KAAuB;AAChC;AAUO,SAAS,aAAa,EAAE,SAAS,EAAE,YAAY,EAAE,wBAAwB,IAAI,EAAqB;;IACvG,yDAAyD;IACzD,yEAAyE;IACzE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW;YACX,UAAU;YACV,OAAO;YACP,UAAU;YACV,sBAAsB;YACtB,UAAU;QACZ;IACF;IAEA,0EAA0E;IAC1E,sCAAsC;IACtC,gBAAgB;IAChB,sBAAsB;IACtB,uCAAuC;IACvC,iCAAiC;IACjC,kDAAkD;IAClD,SAAS;IACT,kCAAkC;IAClC,MAAM;IACN,KAAK;IAEL,wEAAwE;IACxE,8CAA8C;IAC9C,uCAAuC;IACvC,8DAA8D;IAC9D,+BAA+B;IAC/B,kCAAkC;IAClC,QAAQ;IACR,KAAK;IAEL,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;gBAC1C,yDAAyD;gBACzD,wGAAwG;gBACxG,UAAU,KAAK,QAAQ;gBACvB,6BAA6B;gBAC7B,qEAAqE;gBACrE,cAAc;gBACd,6BAA6B;gBAC7B,KAAK;gBACL,cAAc;oBACZ,YAAY,CAAC;wBACX,WAAW;wBACX,QAAQ,GAAG,CAAC,cAAc;oBAC5B;oBACA,WAAW;wBACT,WAAW;oBACb;oBACA,SAAS,CAAC;wBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO;oBAC/B;oBACA,WAAW;wBACT,6BAA6B;wBAC7B,IAAI,WAAW;4BACb;wBACF,OAAO;4BACL,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAChB;wBAEA,IAAI,uBAAuB;4BACzB,OAAO,IAAI,CAAC;wBACd;oBACF;gBACF;YACF;YACA,QAAQ,GAAG,CAAC,0BAA0B;QACxC,EAAE,OAAO,OAAO;YACd,WAAW;YACX,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,GAAG,CAAC,WAAW;QACzB;IACF;IAEA,qBACE;kBACE,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAqB;;;;;;sCAC1C,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;sCAAqB;;;;;;;;;;;;8BAIlD,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAE,GAAG,IAAI;kCACZ,cAAA,6LAAC;4BAAK,UAAU,KAAK,YAAY,CAAC;4BAAW,WAAU;;8CACrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mIAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sEACP,6LAAC,mIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEAAC,aAAY;gEAAU,GAAG,KAAK;;;;;;;;;;;sEAEvC,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sDAIlB,6LAAC,mIAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sEACP,6LAAC,mIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEAAC,aAAY;gEAAY,GAAG,KAAK;;;;;;;;;;;sEAEzC,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8CAMpB,6LAAC,mIAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8DACP,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,MAAK;wDAAQ,aAAY;wDAAsB,GAAG,KAAK;;;;;;;;;;;8DAEhE,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,6LAAC,mIAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8DACP,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,MAAK;wDAAW,aAAY;wDAAc,GAAG,KAAK;;;;;;;;;;;8DAE3D,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,6LAAC,mIAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8DACP,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,MAAK;wDAAW,aAAY;wDAAwB,GAAG,KAAK;;;;;;;;;;;8DAErE,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,6LAAC,mIAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,6IAAA,CAAA,aAAU;wDACT,eAAe,MAAM,QAAQ;wDAC7B,cAAc,MAAM,KAAK;wDACzB,WAAU;;0EAEV,6LAAC,mIAAA,CAAA,WAAQ;gEAAC,WAAU;;kFAClB,6LAAC,mIAAA,CAAA,cAAW;kFACV,cAAA,6LAAC,6IAAA,CAAA,iBAAc;4EAAC,OAAM;;;;;;;;;;;kFAExB,6LAAC,mIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAc;;;;;;;;;;;;0EAIrC,6LAAC,mIAAA,CAAA,WAAQ;gEAAC,WAAU;;kFAClB,6LAAC,mIAAA,CAAA,cAAW;kFACV,cAAA,6LAAC,6IAAA,CAAA,iBAAc;4EAAC,OAAM;;;;;;;;;;;kFAExB,6LAAC,mIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAc;;;;;;;;;;;;;;;;;;;;;;;8DAMzC,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAuClB,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;8CAET,wBACC,6LAAC,oNAAA,CAAA,UAAO;wCAAC,MAAM;wCAAI,WAAU;;;;;+CAE7B;;;;;;;;;;;;;;;;;;;;;;8BAMV,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAuC;gCAC3B;gCAErB,6BACE,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;8CACV;;;;;yDAID,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAzRgB;;QAGC,qIAAA,CAAA,YAAS;QAGX,iKAAA,CAAA,UAAO;;;KANN", "debugId": null}}, {"offset": {"line": 3608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28root%29/vehicles/%5Bid%5D/vehicle-detail-client.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState, useEffect, useMemo } from \"react\"\r\nimport { Vehicle, vehiclesApi } from \"@/lib/api/vehicles.api\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport { Card, CardContent } from \"@/components/ui/card\"\r\nimport { useRouter } from \"next/navigation\"\r\nimport { ChevronRight, /* Info, Calendar, Clock */ } from \"lucide-react\"\r\nimport Image from \"next/image\"\r\n// import Link from 'next/link'\r\nimport DateRangeModal from '@/components/vehicles/date-range-modal'\r\nimport { DateTime } from 'luxon'\r\nimport { VehicleGallery } from '@/components/vehicles/vehicle-gallery'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { useUser } from \"@/context/user-context\"\r\nimport { setCookie } from \"cookies-next\"\r\nimport { Dialog, DialogContent, DialogTitle } from \"@/components/ui/dialog\"\r\nimport { LoginForm } from \"@/components/auth/login-form\"\r\nimport { RegisterForm } from \"@/components/auth/register-form\"\r\nimport toast from \"react-hot-toast\"\r\nimport { addDays, differenceInDays, /* format */ } from \"date-fns\"\r\nimport Link from 'next/link'\r\n// import { es } from \"date-fns/locale\"\r\n// import { Badge } from \"@/components/ui/badge\"\r\n\r\nconst featureLabels: Record<string, Record<string, string>> = {\r\n  mx: {\r\n    transmission: \"Transmisión\",\r\n    fuelType: \"Combustible\",\r\n    seats: \"Asientos\",\r\n    mileage: \"Kilometraje\",\r\n    registrationNumber: \"Matrícula\",\r\n    insurancePolicy: \"Seguro\",\r\n    rules: \"Reglas\",\r\n    location: \"Ubicación\",\r\n    weeklyRate: \"Tarifa semanal\",\r\n    monthlyRate: \"Tarifa mensual\"\r\n  },\r\n  en: {\r\n    transmission: \"Transmission\",\r\n    fuelType: \"Fuel Type\",\r\n    seats: \"Seats\",\r\n    mileage: \"Mileage\",\r\n    registrationNumber: \"Registration Number\",\r\n    insurancePolicy: \"Insurance Policy\",\r\n    rules: \"Rules\",\r\n    location: \"Location\",\r\n    weeklyRate: \"Weekly Rate\",\r\n    monthlyRate: \"Monthly Rate\"\r\n  }\r\n}\r\n\r\nconst labels: Record<string, Record<string, string>> = {\r\n  mx: {\r\n    automatic: \"Automático\",\r\n    manual: \"Manual\"\r\n  },\r\n  en: {\r\n    automatic: \"Automatic\",\r\n    manual: \"Manual\"\r\n  }\r\n}\r\n\r\ninterface VehicleDetailClientProps {\r\n  params: {\r\n    id: string\r\n  }\r\n  vehicle: Vehicle\r\n}\r\n\r\nexport default function VehicleDetailClient({ params: { id }, vehicle }: VehicleDetailClientProps) {\r\n  const [dateRange, setDateRange] = useState<{ startDate: Date; endDate: Date }>({\r\n    startDate: new Date(),\r\n    endDate: addDays(new Date(), 1)\r\n  })\r\n  const [total, setTotal] = useState(0)\r\n  const router = useRouter()\r\n  const { user } = useUser()\r\n  console.log('user', user)\r\n\r\n  // Estado para el modal de autenticación\r\n  const [showAuthDialog, setShowAuthDialog] = useState(false)\r\n  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')\r\n\r\n  // const { data: vehicle, isLoading, error } = useQuery({\r\n  //   queryKey: ['vehicle', id],\r\n  //   // queryFn: () => vehiclesApi.getById(id),\r\n  //   queryFn: () => getVehicleById(id),\r\n  //   staleTime: 60 * 60 * 1000, // Caché por 1 hora\r\n  // })\r\n  // console.log('vehicle', vehicle)\r\n\r\n  // Obtener fechas no disponibles\r\n  const { data: unavailableDates } = useQuery({\r\n    queryKey: ['vehicle-unavailable-dates', id],\r\n    queryFn: () => vehiclesApi.getUnavailableDates(id),\r\n    staleTime: 60 * 60 * 1000, // Caché por 1 hora\r\n    enabled: !!id\r\n  })\r\n\r\n  // console.log('unavailableDatesData', unavailableDates)\r\n\r\n  // Obtener configuración de disponibilidad\r\n  const { data: availabilityData } = useQuery({\r\n    queryKey: ['vehicle-availability', id],\r\n    queryFn: () => vehiclesApi.getAvailabilitySettings(id),\r\n    // staleTime: 60 * 60 * 1000, // Caché por 1 hora\r\n    enabled: !!id,\r\n    retry: 1, // Limitar reintentos en caso de error\r\n    // onError: (error) => {\r\n    //   console.error(\"Error fetching availability settings:\", error);\r\n    // }\r\n  })\r\n\r\n  // const unavailableDates = unavailableDatesData?.data || []\r\n\r\n  // Configuración de disponibilidad por defecto\r\n  const availability = useMemo(() => {\r\n    return availabilityData || {\r\n      minimumRentalNights: 1,\r\n      maximumRentalNights: 30,\r\n      defaultCheckInTime: \"14:00\",\r\n      defaultCheckOutTime: \"12:00\",\r\n      mondayAvailable: true,\r\n      tuesdayAvailable: true,\r\n      wednesdayAvailable: true,\r\n      thursdayAvailable: true,\r\n      fridayAvailable: true,\r\n      saturdayAvailable: true,\r\n      sundayAvailable: true,\r\n      blockedDates: []\r\n    }\r\n  }, [availabilityData])\r\n\r\n\r\n  // Inicializar el rango de fechas por defecto basado en la configuración\r\n  useEffect(() => {\r\n    if (availability && vehicle) {\r\n      const today = new Date()\r\n      const startDate = addDays(today, 1) // Por defecto, empezar mañana\r\n      const endDate = addDays(startDate, Math.max(availability.minimumRentalNights, 0))\r\n\r\n      setDateRange({\r\n        startDate,\r\n        endDate\r\n      })\r\n\r\n      // Calcular el total inicial (días, no días)\r\n      const nights = Math.max(availability.minimumRentalNights, 0)\r\n      setTotal(vehicle.price * nights)\r\n    }\r\n  }, [availability, vehicle])\r\n\r\n  // Actualizar el total cuando cambia el rango de fechas\r\n  useEffect(() => {\r\n    if (vehicle && dateRange) {\r\n      // Calcular el número de días (días - 1)\r\n      const diffNights = differenceInDays(dateRange.endDate, dateRange.startDate)\r\n      const newTotal = vehicle.price * diffNights\r\n      setTotal(newTotal)\r\n    }\r\n  }, [dateRange, vehicle])\r\n\r\n  // Función para obtener los días de la semana no disponibles\r\n  const getUnavailableDaysOfWeek = () => {\r\n    const unavailableDays = []\r\n\r\n    if (!availability.mondayAvailable) unavailableDays.push(1) // Lunes\r\n    if (!availability.tuesdayAvailable) unavailableDays.push(2) // Martes\r\n    if (!availability.wednesdayAvailable) unavailableDays.push(3) // Miércoles\r\n    if (!availability.thursdayAvailable) unavailableDays.push(4) // Jueves\r\n    if (!availability.fridayAvailable) unavailableDays.push(5) // Viernes\r\n    if (!availability.saturdayAvailable) unavailableDays.push(6) // Sábado\r\n    if (!availability.sundayAvailable) unavailableDays.push(0) // Domingo\r\n\r\n    return unavailableDays\r\n  }\r\n\r\n  // Función para verificar si un día está disponible\r\n  const isDateDisabled = (date: Date) => {\r\n    // Verificar si es un día de la semana no disponible\r\n    const unavailableDaysOfWeek = getUnavailableDaysOfWeek()\r\n    if (unavailableDaysOfWeek.includes(date.getDay())) {\r\n      return true\r\n    }\r\n\r\n    // Verificar si está en las fechas bloqueadas manualmente\r\n    const blockedDates = Array.isArray(availability.blockedDates)\r\n      ? availability.blockedDates\r\n      : []\r\n\r\n    // Verificar si está en las fechas no disponibles (reservas)\r\n    return unavailableDates?.some(\r\n      (dateStr) => {\r\n        const unavailableDate = new Date(dateStr)\r\n        return (\r\n          date.getFullYear() === unavailableDate.getFullYear() &&\r\n          date.getMonth() === unavailableDate.getMonth() &&\r\n          date.getDate() === unavailableDate.getDate()\r\n        )\r\n      }\r\n    ) || blockedDates.some(\r\n      (blockedDate: any) => {\r\n        if (typeof blockedDate === 'string') {\r\n          const date1 = new Date(blockedDate)\r\n          return (\r\n            date.getFullYear() === date1.getFullYear() &&\r\n            date.getMonth() === date1.getMonth() &&\r\n            date.getDate() === date1.getDate()\r\n          )\r\n        } else if (blockedDate && blockedDate.date) {\r\n          const date1 = new Date(blockedDate.date)\r\n          return (\r\n            date.getFullYear() === date1.getFullYear() &&\r\n            date.getMonth() === date1.getMonth() &&\r\n            date.getDate() === date1.getDate()\r\n          )\r\n        }\r\n        return false\r\n      }\r\n    )\r\n  }\r\n  console.log('isDateDisabled', isDateDisabled(new Date()))\r\n\r\n  // Función para manejar el cambio de fechas\r\n  const handleDateChange = (range: { startDate: Date; endDate: Date }) => {\r\n    // Verificar que el rango cumple con el mínimo de días\r\n    const diffNights = differenceInDays(range.endDate, range.startDate)\r\n\r\n    if (diffNights < availability.minimumRentalNights) {\r\n      toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)\r\n      return\r\n    }\r\n\r\n    if (diffNights > availability.maximumRentalNights) {\r\n      toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)\r\n      return\r\n    }\r\n\r\n    setDateRange(range)\r\n\r\n    // Actualizar el total (no es necesario aquí ya que se actualiza en el useEffect)\r\n    // El useEffect se encargará de actualizar el total\r\n  }\r\n\r\n  // Función para manejar el botón de reserva\r\n  const handleReserveClick = () => {\r\n    if (!dateRange) {\r\n      toast.error(\"Por favor selecciona las fechas de tu reserva\")\r\n      return\r\n    }\r\n\r\n    // Asegurarse de que dateRange.startDate y dateRange.endDate son objetos Date válidos\r\n    if (!(dateRange.startDate instanceof Date) || !(dateRange.endDate instanceof Date)) {\r\n      // Intentar convertir a objetos Date si no lo son\r\n      try {\r\n        const startDate = dateRange.startDate instanceof Date ? dateRange.startDate : new Date(dateRange.startDate);\r\n        const endDate = dateRange.endDate instanceof Date ? dateRange.endDate : new Date(dateRange.endDate);\r\n\r\n        // Verificar que el rango cumple con el mínimo de días\r\n        const diffNights = differenceInDays(endDate, startDate);\r\n\r\n        if (diffNights < availability.minimumRentalNights) {\r\n          toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)\r\n          return\r\n        }\r\n\r\n        if (diffNights > availability.maximumRentalNights) {\r\n          toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)\r\n          return\r\n        }\r\n\r\n        // Redirigir con las fechas convertidas\r\n        if (!user) {\r\n          setCookie('bookingRedirect', `/booking/${id}?from=${startDate.toISOString()}&to=${endDate.toISOString()}`)\r\n          setAuthMode('login')\r\n          setShowAuthDialog(true)\r\n          return\r\n        }\r\n\r\n        router.push(`/booking/${id}?from=${startDate.toISOString()}&to=${endDate.toISOString()}`)\r\n      } catch (error) {\r\n        console.error(\"Error al procesar las fechas:\", error);\r\n        toast.error(\"Error al procesar las fechas. Por favor, selecciona las fechas nuevamente.\")\r\n        return;\r\n      }\r\n    } else {\r\n      // Verificar que el rango cumple con el mínimo de días\r\n      const diffNights = differenceInDays(dateRange.endDate, dateRange.startDate);\r\n\r\n      if (diffNights < availability.minimumRentalNights) {\r\n        toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)\r\n        return\r\n      }\r\n\r\n      if (diffNights > availability.maximumRentalNights) {\r\n        toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)\r\n        return\r\n      }\r\n\r\n      // Verificar si el usuario está autenticado\r\n      if (!user) {\r\n        // Guardar la URL de redirección para después del login\r\n        setCookie('bookingRedirect', `/booking/${id}?from=${dateRange.startDate.toISOString()}&to=${dateRange.endDate.toISOString()}`)\r\n        setAuthMode('login')\r\n        setShowAuthDialog(true)\r\n        return\r\n      }\r\n\r\n      // Si está autenticado, redirigir a la página de reserva\r\n      router.push(`/booking/${id}?from=${dateRange.startDate.toISOString()}&to=${dateRange.endDate.toISOString()}`)\r\n    }\r\n  }\r\n\r\n  // Función para cambiar entre login y registro\r\n  const toggleAuthMode = () => {\r\n    setAuthMode(authMode === 'login' ? 'register' : 'login')\r\n  }\r\n\r\n  // Función para cerrar el modal después de autenticación exitosa\r\n  const handleAuthSuccess = () => {\r\n    setShowAuthDialog(false)\r\n  }\r\n\r\n  // Obtener características principales\r\n  const parseDate = (date: string) => {\r\n    // parse it like this format: \"25 de junio de 2025\" using luxon, date is a iso string\r\n    return DateTime.fromISO(date).toFormat(\"dd 'de' MMMM 'de' yyyy\", { locale: \"es\" })\r\n  }\r\n\r\n\r\n  // Obtener imágenes del vehículo\r\n  const images = Array.isArray(vehicle.images) ? vehicle.images : [];\r\n\r\n  // Obtener características principales\r\n  // const transmission = vehicle.transmission || '';\r\n  // const transmission = featureLabels[\"mx\"][\"transmission\"] || '';\r\n  const transmission = labels[\"mx\"][vehicle.transmission] || '';\r\n  console.log('vehicle', vehicle)\r\n  const mileage = vehicle.features?.mileage || 0;\r\n  const location = vehicle.features?.location || '';\r\n  const year = vehicle.year || '';\r\n\r\n  // Obtener días no disponibles de la semana en formato legible\r\n  // const unavailableDaysOfWeek = getUnavailableDaysOfWeek()\r\n  // const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado']\r\n  // const unavailableDayNames = unavailableDaysOfWeek.map(day => dayNames[day])\r\n\r\n  // // Calcular duración de la reserva\r\n  // const rentalDuration = dateRange\r\n  //   ? differenceInDays(dateRange.endDate, dateRange.startDate) + 1\r\n  //   : availability.minimumRentalDays;\r\n\r\n  console.log('availability', availability)\r\n\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      {/* Breadcrumb */}\r\n      <div className=\"text-sm text-gray-500 mb-4 flex items-center\">\r\n        {/* <span>AUTOS DISPONIBLES</span> */}\r\n        <Link href=\"/vehicles\" className=\"hover:underline\">\r\n          AUTOS DISPONIBLES\r\n        </Link>\r\n        <ChevronRight className=\"h-4 w-4 mx-1\" />\r\n        <span>{vehicle.make}</span>\r\n        <ChevronRight className=\"h-4 w-4 mx-1\" />\r\n        <span>{vehicle.model}</span>\r\n        <ChevronRight className=\"h-4 w-4 mx-1\" />\r\n        <span>{year}</span>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n        {/* Columna izquierda - Imágenes y detalles */}\r\n        <div className=\"lg:col-span-2 space-y-6\">\r\n          {/* Etiqueta de recién publicado */}\r\n          <VehicleGallery\r\n            vehicle={vehicle}\r\n            images={images}\r\n          // currentImageIndex={currentImageIndex}\r\n          // setCurrentImageIndex={setCurrentImageIndex}\r\n          />\r\n\r\n          {/* Título y precio */}\r\n          <div className=\"flex justify-between items-start\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-bold\">\r\n                {vehicle.make} {vehicle.model} {transmission} {year}\r\n              </h1>\r\n              <div className=\"flex items-center mt-1\">\r\n                <span className=\"text-gray-600\">{mileage.toLocaleString()} km</span>\r\n                <span className=\"mx-2\">•</span>\r\n                <span className=\"text-gray-600\">{location}</span>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-sm text-gray-600\">Precio desde</div>\r\n              <div className=\"text-2xl font-bold\">${vehicle.price.toLocaleString()}</div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Características principales */}\r\n          <div className=\"mt-8\">\r\n            <h2 className=\"text-xl font-bold mb-4\">Equipamiento destacado</h2>\r\n            <div className=\"grid grid-cols-3 gap-4\">\r\n              {vehicle.features && Object.entries(vehicle.features).slice(0, 6).map(([key, value]) => (\r\n                <div key={key} className=\"bg-gray-50 p-4 rounded-lg\">\r\n                  {/* <div className=\"text-sm text-gray-500 capitalize\">{key}</div> */}\r\n                  <div className=\"text-sm text-gray-500 capitalize\">{featureLabels[\"mx\"][key]}</div>\r\n                  <div className=\"font-medium\">{String(value)}</div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Características detalladas */}\r\n          <div className=\"mt-8\">\r\n            <h2 className=\"text-xl font-bold mb-4\">Características principales</h2>\r\n\r\n            <Tabs defaultValue=\"general\">\r\n              <TabsList className=\"w-full border-b mb-4\">\r\n                <TabsTrigger value=\"general\">General</TabsTrigger>\r\n                <TabsTrigger value=\"exterior\">Exterior</TabsTrigger>\r\n                <TabsTrigger value=\"equipamiento\">Equipamiento</TabsTrigger>\r\n                <TabsTrigger value=\"seguridad\">Seguridad</TabsTrigger>\r\n                <TabsTrigger value=\"interior\">Interior</TabsTrigger>\r\n              </TabsList>\r\n\r\n              <TabsContent value=\"general\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  {vehicle.features && Object.entries(vehicle.features).map(([key, value]) => {\r\n                    // Verificar si la clave existe en featureLabels\r\n                    const label = featureLabels[\"mx\"][key] || key;\r\n\r\n                    return (\r\n                      <div key={key} className=\"bg-gray-50 p-4 rounded-lg\">\r\n                        <div className=\"text-sm text-gray-500 capitalize mb-1\">{label}</div>\r\n                        <div className=\"font-medium\">\r\n                          {typeof value === 'string' && value.length > 50\r\n                            ? `${value.substring(0, 50)}...`\r\n                            : String(value)}\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"exterior\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Tipo de Carrocería</div>\r\n                    <div className=\"font-medium\">SUV</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Color</div>\r\n                    <div className=\"font-medium\">{vehicle.color}</div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"equipamiento\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Aire acondicionado</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Bluetooth</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Navegación GPS</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"seguridad\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Airbags</div>\r\n                    <div className=\"font-medium\">6</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Control de estabilidad</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Frenos ABS</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"interior\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Tapicería</div>\r\n                    <div className=\"font-medium\">Tela</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Asientos eléctricos</div>\r\n                    <div className=\"font-medium\">No</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Volante ajustable</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n            </Tabs>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Columna derecha - Reserva */}\r\n        <div>\r\n          <div className='sticky top-6 space-y-4'>\r\n\r\n            <Card className=\"\">\r\n              <CardContent className=\"p-6\">\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <h2 className=\"text-2xl font-bold\">${vehicle.price.toLocaleString()} <span className=\"text-base font-normal\">/ día</span></h2>\r\n                  </div>\r\n\r\n                  <Separator />\r\n\r\n                  <div className=\"mb-6\">\r\n                    <div className=\"mb-4\">\r\n                      <label className=\"block text-sm font-medium mb-1\">Selecciona las fechas</label>\r\n                      <DateRangeModal\r\n                        unavailableDates={unavailableDates}\r\n                        onChange={handleDateChange}\r\n                        initialDateRange={dateRange}\r\n                        minimumRentalNights={availability.minimumRentalNights}\r\n                        maximumRentalNights={availability.maximumRentalNights}\r\n                      />\r\n                    </div>\r\n\r\n\r\n\r\n                    <div className=\"flex justify-between items-center font-bold\">\r\n                      <span>Total</span>\r\n                      <span className=\"text-sm\">${total.toLocaleString()}</span>\r\n                    </div>\r\n\r\n                    {/* Información adicional sobre la reserva */}\r\n                    <div className=\"mt-2 text-xs text-gray-500 space-y-1\">\r\n                      <div className=\"flex justify-between\">\r\n                        <span>Precio por día:</span>\r\n                        <span>${vehicle.price.toLocaleString()}</span>\r\n                      </div>\r\n                      {dateRange && (\r\n                        <div className=\"flex justify-between\">\r\n                          <span>Noches seleccionadas:</span>\r\n                          <span>{differenceInDays(dateRange.endDate, dateRange.startDate)}</span>\r\n                        </div>\r\n                      )}\r\n                      <div className=\"flex justify-between\">\r\n                        <span>Total:</span>\r\n                        <span>${total.toLocaleString()}</span>\r\n                      </div>\r\n                      <div className=\"flex justify-between\">\r\n                        <span>Mínimo de días para rentar:</span>\r\n                        <span>{availability.minimumRentalNights}</span>\r\n                      </div>\r\n                      <div className=\"flex justify-between\">\r\n                        <span>Máximo de días para rentar:</span>\r\n                        <span>{availability.maximumRentalNights}</span>\r\n                      </div>\r\n                    </div>\r\n\r\n                  </div>\r\n\r\n                  <Button\r\n                    className=\"w-full bg-[#1a2b5e] hover:bg-[#152348]\"\r\n                    onClick={handleReserveClick}\r\n                  >\r\n                    <span className=\"font-medium\">Reservar</span>\r\n                  </Button>\r\n\r\n                  <p className=\"text-sm text-muted-foreground text-center\">\r\n                    No se te cobrará nada todavía\r\n                  </p>\r\n\r\n                  <div className=\"pt-4 space-y-2\">\r\n                    <div className=\"flex items-center text-sm\">\r\n                      <svg className=\"h-5 w-5 mr-2 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                      </svg>\r\n                      <span>Auto con certificado Autoop</span>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center text-sm\">\r\n                      <svg className=\"h-5 w-5 mr-2 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                      </svg>\r\n                      <span>Alguna caracteristica aqui</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n              </CardContent>\r\n            </Card>\r\n            <div className=\"border rounded-lg p-6 hidden lg:block\">\r\n              <div className=\"flex items-center gap-4 mb-4\">\r\n                <div className=\"w-12 h-12 rounded-full bg-gray-200 overflow-hidden relative\">\r\n                  <Image\r\n                    src={vehicle.host.image || \"/placeholder.svg?height=48&width=48\"}\r\n                    alt=\"Host\"\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-medium\">{vehicle.host.name}</h3>\r\n                  <p className=\"text-sm text-gray-500\">Miembro desde {parseDate(vehicle.host.createdAt)}</p>\r\n                </div>\r\n              </div>\r\n\r\n              <Button variant=\"outline\" className=\"w-full\">\r\n                <span className=\"font-medium\">Contact Host</span>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n\r\n      {/* Modal de autenticación */}\r\n      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog} >\r\n        <DialogContent className=\"sm:max-w-md \">\r\n        <DialogTitle className='sr-only'>\r\n          {authMode === 'login' ? 'Inicia sesión para continuar' : 'Crea una cuenta para continuar'}\r\n          </DialogTitle>\r\n          <div className='mt-5'>\r\n            {authMode === 'login' ? (\r\n              <LoginForm\r\n                onSuccess={handleAuthSuccess}\r\n                onRegisterClick={toggleAuthMode}\r\n                redirectAfterLogin={true}\r\n              />\r\n            ) : (\r\n              <RegisterForm\r\n                onSuccess={handleAuthSuccess}\r\n                onLoginClick={toggleAuthMode}\r\n                redirectAfterRegister={true}\r\n              />\r\n            )}\r\n          </div>\r\n\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  )\r\n}\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAvBA;;;;;;;;;;;;;;;;;;;;;;AAwBA,uCAAuC;AACvC,gDAAgD;AAEhD,MAAM,gBAAwD;IAC5D,IAAI;QACF,cAAc;QACd,UAAU;QACV,OAAO;QACP,SAAS;QACT,oBAAoB;QACpB,iBAAiB;QACjB,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;IACf;IACA,IAAI;QACF,cAAc;QACd,UAAU;QACV,OAAO;QACP,SAAS;QACT,oBAAoB;QACpB,iBAAiB;QACjB,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;IACf;AACF;AAEA,MAAM,SAAiD;IACrD,IAAI;QACF,WAAW;QACX,QAAQ;IACV;IACA,IAAI;QACF,WAAW;QACX,QAAQ;IACV;AACF;AASe,SAAS,oBAAoB,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,OAAO,EAA4B;;IAC/F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;QAC7E,WAAW,IAAI;QACf,SAAS,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;IAC/B;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,QAAQ,GAAG,CAAC,QAAQ;IAEpB,wCAAwC;IACxC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAE/D,yDAAyD;IACzD,+BAA+B;IAC/B,+CAA+C;IAC/C,uCAAuC;IACvC,mDAAmD;IACnD,KAAK;IACL,kCAAkC;IAElC,gCAAgC;IAChC,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC1C,UAAU;YAAC;YAA6B;SAAG;QAC3C,OAAO;4CAAE,IAAM,uIAAA,CAAA,cAAW,CAAC,mBAAmB,CAAC;;QAC/C,WAAW,KAAK,KAAK;QACrB,SAAS,CAAC,CAAC;IACb;IAEA,wDAAwD;IAExD,0CAA0C;IAC1C,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC1C,UAAU;YAAC;YAAwB;SAAG;QACtC,OAAO;4CAAE,IAAM,uIAAA,CAAA,cAAW,CAAC,uBAAuB,CAAC;;QACnD,iDAAiD;QACjD,SAAS,CAAC,CAAC;QACX,OAAO;IAIT;IAEA,4DAA4D;IAE5D,8CAA8C;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAC3B,OAAO,oBAAoB;gBACzB,qBAAqB;gBACrB,qBAAqB;gBACrB,oBAAoB;gBACpB,qBAAqB;gBACrB,iBAAiB;gBACjB,kBAAkB;gBAClB,oBAAoB;gBACpB,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,iBAAiB;gBACjB,cAAc,EAAE;YAClB;QACF;oDAAG;QAAC;KAAiB;IAGrB,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,gBAAgB,SAAS;gBAC3B,MAAM,QAAQ,IAAI;gBAClB,MAAM,YAAY,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,GAAG,8BAA8B;;gBAClE,MAAM,UAAU,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,WAAW,KAAK,GAAG,CAAC,aAAa,mBAAmB,EAAE;gBAE9E,aAAa;oBACX;oBACA;gBACF;gBAEA,4CAA4C;gBAC5C,MAAM,SAAS,KAAK,GAAG,CAAC,aAAa,mBAAmB,EAAE;gBAC1D,SAAS,QAAQ,KAAK,GAAG;YAC3B;QACF;wCAAG;QAAC;QAAc;KAAQ;IAE1B,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,WAAW,WAAW;gBACxB,wCAAwC;gBACxC,MAAM,aAAa,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,OAAO,EAAE,UAAU,SAAS;gBAC1E,MAAM,WAAW,QAAQ,KAAK,GAAG;gBACjC,SAAS;YACX;QACF;wCAAG;QAAC;QAAW;KAAQ;IAEvB,4DAA4D;IAC5D,MAAM,2BAA2B;QAC/B,MAAM,kBAAkB,EAAE;QAE1B,IAAI,CAAC,aAAa,eAAe,EAAE,gBAAgB,IAAI,CAAC,GAAG,QAAQ;;QACnE,IAAI,CAAC,aAAa,gBAAgB,EAAE,gBAAgB,IAAI,CAAC,GAAG,SAAS;;QACrE,IAAI,CAAC,aAAa,kBAAkB,EAAE,gBAAgB,IAAI,CAAC,GAAG,YAAY;;QAC1E,IAAI,CAAC,aAAa,iBAAiB,EAAE,gBAAgB,IAAI,CAAC,GAAG,SAAS;;QACtE,IAAI,CAAC,aAAa,eAAe,EAAE,gBAAgB,IAAI,CAAC,GAAG,UAAU;;QACrE,IAAI,CAAC,aAAa,iBAAiB,EAAE,gBAAgB,IAAI,CAAC,GAAG,SAAS;;QACtE,IAAI,CAAC,aAAa,eAAe,EAAE,gBAAgB,IAAI,CAAC,GAAG,UAAU;;QAErE,OAAO;IACT;IAEA,mDAAmD;IACnD,MAAM,iBAAiB,CAAC;QACtB,oDAAoD;QACpD,MAAM,wBAAwB;QAC9B,IAAI,sBAAsB,QAAQ,CAAC,KAAK,MAAM,KAAK;YACjD,OAAO;QACT;QAEA,yDAAyD;QACzD,MAAM,eAAe,MAAM,OAAO,CAAC,aAAa,YAAY,IACxD,aAAa,YAAY,GACzB,EAAE;QAEN,4DAA4D;QAC5D,OAAO,kBAAkB,KACvB,CAAC;YACC,MAAM,kBAAkB,IAAI,KAAK;YACjC,OACE,KAAK,WAAW,OAAO,gBAAgB,WAAW,MAClD,KAAK,QAAQ,OAAO,gBAAgB,QAAQ,MAC5C,KAAK,OAAO,OAAO,gBAAgB,OAAO;QAE9C,MACG,aAAa,IAAI,CACpB,CAAC;YACC,IAAI,OAAO,gBAAgB,UAAU;gBACnC,MAAM,QAAQ,IAAI,KAAK;gBACvB,OACE,KAAK,WAAW,OAAO,MAAM,WAAW,MACxC,KAAK,QAAQ,OAAO,MAAM,QAAQ,MAClC,KAAK,OAAO,OAAO,MAAM,OAAO;YAEpC,OAAO,IAAI,eAAe,YAAY,IAAI,EAAE;gBAC1C,MAAM,QAAQ,IAAI,KAAK,YAAY,IAAI;gBACvC,OACE,KAAK,WAAW,OAAO,MAAM,WAAW,MACxC,KAAK,QAAQ,OAAO,MAAM,QAAQ,MAClC,KAAK,OAAO,OAAO,MAAM,OAAO;YAEpC;YACA,OAAO;QACT;IAEJ;IACA,QAAQ,GAAG,CAAC,kBAAkB,eAAe,IAAI;IAEjD,2CAA2C;IAC3C,MAAM,mBAAmB,CAAC;QACxB,sDAAsD;QACtD,MAAM,aAAa,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,OAAO,EAAE,MAAM,SAAS;QAElE,IAAI,aAAa,aAAa,mBAAmB,EAAE;YACjD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,aAAa,mBAAmB,CAAC,KAAK,CAAC;YACtF;QACF;QAEA,IAAI,aAAa,aAAa,mBAAmB,EAAE;YACjD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,aAAa,mBAAmB,CAAC,KAAK,CAAC;YAClF;QACF;QAEA,aAAa;IAEb,iFAAiF;IACjF,mDAAmD;IACrD;IAEA,2CAA2C;IAC3C,MAAM,qBAAqB;QACzB,IAAI,CAAC,WAAW;YACd,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qFAAqF;QACrF,IAAI,CAAC,CAAC,UAAU,SAAS,YAAY,IAAI,KAAK,CAAC,CAAC,UAAU,OAAO,YAAY,IAAI,GAAG;YAClF,iDAAiD;YACjD,IAAI;gBACF,MAAM,YAAY,UAAU,SAAS,YAAY,OAAO,UAAU,SAAS,GAAG,IAAI,KAAK,UAAU,SAAS;gBAC1G,MAAM,UAAU,UAAU,OAAO,YAAY,OAAO,UAAU,OAAO,GAAG,IAAI,KAAK,UAAU,OAAO;gBAElG,sDAAsD;gBACtD,MAAM,aAAa,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS;gBAE7C,IAAI,aAAa,aAAa,mBAAmB,EAAE;oBACjD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,aAAa,mBAAmB,CAAC,KAAK,CAAC;oBACtF;gBACF;gBAEA,IAAI,aAAa,aAAa,mBAAmB,EAAE;oBACjD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,aAAa,mBAAmB,CAAC,KAAK,CAAC;oBAClF;gBACF;gBAEA,uCAAuC;gBACvC,IAAI,CAAC,MAAM;oBACT,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB,CAAC,SAAS,EAAE,GAAG,MAAM,EAAE,UAAU,WAAW,GAAG,IAAI,EAAE,QAAQ,WAAW,IAAI;oBACzG,YAAY;oBACZ,kBAAkB;oBAClB;gBACF;gBAEA,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,GAAG,MAAM,EAAE,UAAU,WAAW,GAAG,IAAI,EAAE,QAAQ,WAAW,IAAI;YAC1F,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF,OAAO;YACL,sDAAsD;YACtD,MAAM,aAAa,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,OAAO,EAAE,UAAU,SAAS;YAE1E,IAAI,aAAa,aAAa,mBAAmB,EAAE;gBACjD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,aAAa,mBAAmB,CAAC,KAAK,CAAC;gBACtF;YACF;YAEA,IAAI,aAAa,aAAa,mBAAmB,EAAE;gBACjD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,aAAa,mBAAmB,CAAC,KAAK,CAAC;gBAClF;YACF;YAEA,2CAA2C;YAC3C,IAAI,CAAC,MAAM;gBACT,uDAAuD;gBACvD,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB,CAAC,SAAS,EAAE,GAAG,MAAM,EAAE,UAAU,SAAS,CAAC,WAAW,GAAG,IAAI,EAAE,UAAU,OAAO,CAAC,WAAW,IAAI;gBAC7H,YAAY;gBACZ,kBAAkB;gBAClB;YACF;YAEA,wDAAwD;YACxD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,GAAG,MAAM,EAAE,UAAU,SAAS,CAAC,WAAW,GAAG,IAAI,EAAE,UAAU,OAAO,CAAC,WAAW,IAAI;QAC9G;IACF;IAEA,8CAA8C;IAC9C,MAAM,iBAAiB;QACrB,YAAY,aAAa,UAAU,aAAa;IAClD;IAEA,gEAAgE;IAChE,MAAM,oBAAoB;QACxB,kBAAkB;IACpB;IAEA,sCAAsC;IACtC,MAAM,YAAY,CAAC;QACjB,qFAAqF;QACrF,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,0BAA0B;YAAE,QAAQ;QAAK;IAClF;IAGA,gCAAgC;IAChC,MAAM,SAAS,MAAM,OAAO,CAAC,QAAQ,MAAM,IAAI,QAAQ,MAAM,GAAG,EAAE;IAElE,sCAAsC;IACtC,mDAAmD;IACnD,kEAAkE;IAClE,MAAM,eAAe,MAAM,CAAC,KAAK,CAAC,QAAQ,YAAY,CAAC,IAAI;IAC3D,QAAQ,GAAG,CAAC,WAAW;IACvB,MAAM,UAAU,QAAQ,QAAQ,EAAE,WAAW;IAC7C,MAAM,WAAW,QAAQ,QAAQ,EAAE,YAAY;IAC/C,MAAM,OAAO,QAAQ,IAAI,IAAI;IAE7B,8DAA8D;IAC9D,2DAA2D;IAC3D,8FAA8F;IAC9F,8EAA8E;IAE9E,qCAAqC;IACrC,mCAAmC;IACnC,mEAAmE;IACnE,sCAAsC;IAEtC,QAAQ,GAAG,CAAC,gBAAgB;IAE5B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAU;kCAAkB;;;;;;kCAGnD,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,6LAAC;kCAAM,QAAQ,IAAI;;;;;;kCACnB,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,6LAAC;kCAAM,QAAQ,KAAK;;;;;;kCACpB,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,6LAAC;kCAAM;;;;;;;;;;;;0BAGT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,uJAAA,CAAA,iBAAc;gCACb,SAAS;gCACT,QAAQ;;;;;;0CAMV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;oDACX,QAAQ,IAAI;oDAAC;oDAAE,QAAQ,KAAK;oDAAC;oDAAE;oDAAa;oDAAE;;;;;;;0DAEjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DAAiB,QAAQ,cAAc;4DAAG;;;;;;;kEAC1D,6LAAC;wDAAK,WAAU;kEAAO;;;;;;kEACvB,6LAAC;wDAAK,WAAU;kEAAiB;;;;;;;;;;;;;;;;;;kDAGrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;oDAAqB;oDAAE,QAAQ,KAAK,CAAC,cAAc;;;;;;;;;;;;;;;;;;;0CAKtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,QAAQ,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACjF,6LAAC;gDAAc,WAAU;;kEAEvB,6LAAC;wDAAI,WAAU;kEAAoC,aAAa,CAAC,KAAK,CAAC,IAAI;;;;;;kEAC3E,6LAAC;wDAAI,WAAU;kEAAe,OAAO;;;;;;;+CAH7B;;;;;;;;;;;;;;;;0CAUhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyB;;;;;;kDAEvC,6LAAC,mIAAA,CAAA,OAAI;wCAAC,cAAa;;0DACjB,6LAAC,mIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;kEAAU;;;;;;kEAC7B,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;kEAAW;;;;;;kEAC9B,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;kEAAe;;;;;;kEAClC,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;kEAAY;;;;;;kEAC/B,6LAAC,mIAAA,CAAA,cAAW;wDAAC,OAAM;kEAAW;;;;;;;;;;;;0DAGhC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAU,WAAU;0DACrC,cAAA,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;wDACrE,gDAAgD;wDAChD,MAAM,QAAQ,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI;wDAE1C,qBACE,6LAAC;4DAAc,WAAU;;8EACvB,6LAAC;oEAAI,WAAU;8EAAyC;;;;;;8EACxD,6LAAC;oEAAI,WAAU;8EACZ,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,KACzC,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAC9B,OAAO;;;;;;;2DALL;;;;;oDASd;;;;;;;;;;;0DAIJ,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAW,WAAU;0DACtC,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAe,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;0DAKjD,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAe,WAAU;0DAC1C,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;0DAKnC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAY,WAAU;0DACvC,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;0DAKnC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAW,WAAU;0DACtC,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzC,6LAAC;kCACC,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DACC,cAAA,6LAAC;wDAAG,WAAU;;4DAAqB;4DAAE,QAAQ,KAAK,CAAC,cAAc;4DAAG;0EAAC,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;8DAG/G,6LAAC,wIAAA,CAAA,YAAS;;;;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAiC;;;;;;8EAClD,6LAAC,2JAAA,CAAA,UAAc;oEACb,kBAAkB;oEAClB,UAAU;oEACV,kBAAkB;oEAClB,qBAAqB,aAAa,mBAAmB;oEACrD,qBAAqB,aAAa,mBAAmB;;;;;;;;;;;;sEAMzD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;;wEAAU;wEAAE,MAAM,cAAc;;;;;;;;;;;;;sEAIlD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;;gFAAK;gFAAE,QAAQ,KAAK,CAAC,cAAc;;;;;;;;;;;;;gEAErC,2BACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;sFAAM,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,OAAO,EAAE,UAAU,SAAS;;;;;;;;;;;;8EAGlE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;;gFAAK;gFAAE,MAAM,cAAc;;;;;;;;;;;;;8EAE9B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;sFAAM,aAAa,mBAAmB;;;;;;;;;;;;8EAEzC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;sFAAM,aAAa,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;8DAM7C,6LAAC,qIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;8DAGhC,6LAAC;oDAAE,WAAU;8DAA4C;;;;;;8DAIzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAClF,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;8EAEvE,6LAAC;8EAAK;;;;;;;;;;;;sEAGR,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAClF,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;8EAEvE,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI;wDAC3B,KAAI;wDACJ,IAAI;wDACJ,WAAU;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAe,QAAQ,IAAI,CAAC,IAAI;;;;;;sEAC9C,6LAAC;4DAAE,WAAU;;gEAAwB;gEAAe,UAAU,QAAQ,IAAI,CAAC,SAAS;;;;;;;;;;;;;;;;;;;sDAIxF,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAClC,cAAA,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BAC1C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACzB,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,aAAa,UAAU,iCAAiC;;;;;;sCAEzD,6LAAC;4BAAI,WAAU;sCACZ,aAAa,wBACZ,6LAAC,8IAAA,CAAA,YAAS;gCACR,WAAW;gCACX,iBAAiB;gCACjB,oBAAoB;;;;;qDAGtB,6LAAC,iJAAA,CAAA,eAAY;gCACX,WAAW;gCACX,cAAc;gCACd,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvC;GAzkBwB;;QAMP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,UAAO;QAgBW,8KAAA,CAAA,WAAQ;QAUR,8KAAA,CAAA,WAAQ;;;KAjCrB", "debugId": null}}]}