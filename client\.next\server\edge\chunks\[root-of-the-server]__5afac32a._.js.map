{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/index.ts"], "sourcesContent": ["export const BEARER_COOKIE_NAME = \"bearer_token\";\r\nexport const PENDING_INVITATION_COOKIE = 'pending_invitation';\r\nexport const PENDING_INVITATION_EMAIL_COOKIE = 'pending_invitation_email';\r\nexport const PENDING_INVITATION_ORG_ID_COOKIE = 'pending_invitation_org_id';"], "names": [], "mappings": ";;;;;;AAAO,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAClC,MAAM,kCAAkC;AACxC,MAAM,mCAAmC"}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/api/set-private-token/route.ts"], "sourcesContent": ["import { BEARER_COOKIE_NAME } from '@/constants';\nimport { NextResponse } from 'next/server';\nimport { cookies } from 'next/headers';\nimport { setCookie } from 'cookies-next/server';\n\nexport const runtime = 'edge';\n\nexport async function POST(request: Request) {\n  // console.log('request: ', request);\n  const body = await request.json();\n  console.log('body: ', body);\n  const url = body.url;\n  const token = body.token;\n  if (token) {\n    // const res = new Response();\n    // res.headers.set('Set-Cookie', `better-auth.session_token=${token}; Path=/; HttpOnly; Secure; SameSite=None`);\n    // // set the BEARER_COOKIE_NAME\n    // res.headers.set('Set-Cookie', `bearer_token=${token}; Path=/; Secure; SameSite=None`);\n    // return res;\n\n    await setCookie(BEARER_COOKIE_NAME, token, { cookies, httpOnly: false, secure: true, sameSite: 'none', path: '/' });\n\n    const newUrlWithUrlQuery = new URL(url);\n    // newUrlWithUrlQuery.searchParams.set('url', url);\n    newUrlWithUrlQuery.searchParams.set('token', token);\n    newUrlWithUrlQuery.searchParams.set('tcl', token);\n    const redirectUrl = newUrlWithUrlQuery.toString()\n    console.log('redirectUrl: ', redirectUrl);\n    // return NextResponse.redirect(redirectUrl, {\n    //   headers: {\n    //     'Set-Cookie': `${BEARER_COOKIE_NAME}=${token}`\n    //   }\n    // });\n\n    // const enviorments = process.env;\n    // console.log('enviorments: ', enviorments);\n\n    await setCookie(BEARER_COOKIE_NAME, token, { cookies, httpOnly: false, secure: true, sameSite: 'none', path: '/' });\n    console.log('BEARER_COOKIE_NAME: ', BEARER_COOKIE_NAME);\n    // return NextResponse.json({ message: 'Token set' });\n    // tcl\n\n\n\n    return NextResponse.redirect(redirectUrl, {\n      headers: {\n        'Set-Cookie': `${BEARER_COOKIE_NAME}=${token}`\n      }\n    });\n\n  }\n  return new Response('No token provided', { status: 400 });\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AACA;;;;;AAEO,MAAM,UAAU;AAEhB,eAAe,KAAK,OAAgB;IACzC,qCAAqC;IACrC,MAAM,OAAO,MAAM,QAAQ,IAAI;IAC/B,QAAQ,GAAG,CAAC,UAAU;IACtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,QAAQ,KAAK,KAAK;IACxB,IAAI,OAAO;QACT,8BAA8B;QAC9B,gHAAgH;QAChH,gCAAgC;QAChC,yFAAyF;QACzF,cAAc;QAEd,MAAM,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,mIAAA,CAAA,qBAAkB,EAAE,OAAO;YAAE,SAAA,6KAAA,CAAA,UAAO;YAAE,UAAU;YAAO,QAAQ;YAAM,UAAU;YAAQ,MAAM;QAAI;QAEjH,MAAM,qBAAqB,IAAI,IAAI;QACnC,mDAAmD;QACnD,mBAAmB,YAAY,CAAC,GAAG,CAAC,SAAS;QAC7C,mBAAmB,YAAY,CAAC,GAAG,CAAC,OAAO;QAC3C,MAAM,cAAc,mBAAmB,QAAQ;QAC/C,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,8CAA8C;QAC9C,eAAe;QACf,qDAAqD;QACrD,MAAM;QACN,MAAM;QAEN,mCAAmC;QACnC,6CAA6C;QAE7C,MAAM,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,mIAAA,CAAA,qBAAkB,EAAE,OAAO;YAAE,SAAA,6KAAA,CAAA,UAAO;YAAE,UAAU;YAAO,QAAQ;YAAM,UAAU;YAAQ,MAAM;QAAI;QACjH,QAAQ,GAAG,CAAC,wBAAwB,mIAAA,CAAA,qBAAkB;QACtD,sDAAsD;QACtD,MAAM;QAIN,OAAO,+LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,aAAa;YACxC,SAAS;gBACP,cAAc,GAAG,mIAAA,CAAA,qBAAkB,CAAC,CAAC,EAAE,OAAO;YAChD;QACF;IAEF;IACA,OAAO,IAAI,SAAS,qBAAqB;QAAE,QAAQ;IAAI;AACzD"}}]}