{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/proxy/[...path]", "regex": "^/api/proxy/(.+?)(?:/)?$", "routeKeys": {"nxtPpath": "nxtPpath"}, "namedRegex": "^/api/proxy/(?<nxtPpath>.+?)(?:/)?$"}, {"page": "/booking/[id]", "regex": "^/booking/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/booking/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/admin/vehicles/[id]", "regex": "^/dashboard/admin/vehicles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/admin/vehicles/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/client/reservations/[id]", "regex": "^/dashboard/client/reservations/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/client/reservations/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/host/vehicles/[id]", "regex": "^/dashboard/host/vehicles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/host/vehicles/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/host/vehicles/[id]/calendar", "regex": "^/dashboard/host/vehicles/([^/]+?)/calendar(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/host/vehicles/(?<nxtPid>[^/]+?)/calendar(?:/)?$"}, {"page": "/dashboard/host/vehicles/[id]/edit", "regex": "^/dashboard/host/vehicles/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/host/vehicles/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/vehicles/[id]", "regex": "^/vehicles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/vehicles/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/admin", "regex": "^/dashboard/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin(?:/)?$"}, {"page": "/dashboard/admin/clients", "regex": "^/dashboard/admin/clients(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/clients(?:/)?$"}, {"page": "/dashboard/admin/hosts", "regex": "^/dashboard/admin/hosts(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/hosts(?:/)?$"}, {"page": "/dashboard/admin/payouts", "regex": "^/dashboard/admin/payouts(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/payouts(?:/)?$"}, {"page": "/dashboard/admin/profile", "regex": "^/dashboard/admin/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/profile(?:/)?$"}, {"page": "/dashboard/admin/reports", "regex": "^/dashboard/admin/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/reports(?:/)?$"}, {"page": "/dashboard/admin/reservations", "regex": "^/dashboard/admin/reservations(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/reservations(?:/)?$"}, {"page": "/dashboard/admin/states", "regex": "^/dashboard/admin/states(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/states(?:/)?$"}, {"page": "/dashboard/admin/support", "regex": "^/dashboard/admin/support(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/support(?:/)?$"}, {"page": "/dashboard/admin/vehicles", "regex": "^/dashboard/admin/vehicles(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/vehicles(?:/)?$"}, {"page": "/dashboard/admin/vehicles/approvals", "regex": "^/dashboard/admin/vehicles/approvals(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/vehicles/approvals(?:/)?$"}, {"page": "/dashboard/admin/verifications", "regex": "^/dashboard/admin/verifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin/verifications(?:/)?$"}, {"page": "/dashboard/client", "regex": "^/dashboard/client(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client(?:/)?$"}, {"page": "/dashboard/client/favorites", "regex": "^/dashboard/client/favorites(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client/favorites(?:/)?$"}, {"page": "/dashboard/client/history", "regex": "^/dashboard/client/history(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client/history(?:/)?$"}, {"page": "/dashboard/client/messages", "regex": "^/dashboard/client/messages(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client/messages(?:/)?$"}, {"page": "/dashboard/client/payments", "regex": "^/dashboard/client/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client/payments(?:/)?$"}, {"page": "/dashboard/client/reservations", "regex": "^/dashboard/client/reservations(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client/reservations(?:/)?$"}, {"page": "/dashboard/client/search", "regex": "^/dashboard/client/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client/search(?:/)?$"}, {"page": "/dashboard/client/settings", "regex": "^/dashboard/client/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client/settings(?:/)?$"}, {"page": "/dashboard/client/verification", "regex": "^/dashboard/client/verification(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/client/verification(?:/)?$"}, {"page": "/dashboard/host", "regex": "^/dashboard/host(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host(?:/)?$"}, {"page": "/dashboard/host/earnings", "regex": "^/dashboard/host/earnings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host/earnings(?:/)?$"}, {"page": "/dashboard/host/messages", "regex": "^/dashboard/host/messages(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host/messages(?:/)?$"}, {"page": "/dashboard/host/reports", "regex": "^/dashboard/host/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host/reports(?:/)?$"}, {"page": "/dashboard/host/reservations", "regex": "^/dashboard/host/reservations(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host/reservations(?:/)?$"}, {"page": "/dashboard/host/settings", "regex": "^/dashboard/host/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host/settings(?:/)?$"}, {"page": "/dashboard/host/vehicles", "regex": "^/dashboard/host/vehicles(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host/vehicles(?:/)?$"}, {"page": "/dashboard/host/vehicles/new", "regex": "^/dashboard/host/vehicles/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host/vehicles/new(?:/)?$"}, {"page": "/dashboard/host/verification", "regex": "^/dashboard/host/verification(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/host/verification(?:/)?$"}, {"page": "/email-verified", "regex": "^/email\\-verified(?:/)?$", "routeKeys": {}, "namedRegex": "^/email\\-verified(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/sign-in", "regex": "^/sign\\-in(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-in(?:/)?$"}, {"page": "/sign-up", "regex": "^/sign\\-up(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-up(?:/)?$"}, {"page": "/vehicles", "regex": "^/vehicles(?:/)?$", "routeKeys": {}, "namedRegex": "^/vehicles(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}