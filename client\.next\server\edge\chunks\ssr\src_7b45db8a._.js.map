{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,kMAAC,0KAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,kMAAC,0KAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,kMAAC,0KAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,kMAAC,0KAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,kMAAC,0KAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,kMAAC,gOAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,kMAAC,0KAAA,CAAA,SAAsB;kBACrB,cAAA,kMAAC,0KAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,kMAAC;;;;;8BACD,kMAAC,0KAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,kMAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,kMAAC,0KAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,kMAAC,0KAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,kMAAC;gBAAK,WAAU;0BACd,cAAA,kMAAC,0KAAA,CAAA,gBAA6B;8BAC5B,cAAA,kMAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,kMAAC,0KAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,kMAAC,0KAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,kMAAC,0KAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,kMAAC,4NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,kMAAC,0KAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,kMAAC,gOAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC"}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/api/favorites.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\nimport { Vehicle } from './vehicles.api';\n\nexport interface FavoriteResponse {\n  success: boolean;\n  message?: string;\n  data?: Vehicle[];\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nexport interface FavoriteStatusResponse {\n  success: boolean;\n  message?: string;\n  isFavorite: boolean;\n}\n\nexport interface MultipleFavoriteStatusResponse {\n  success: boolean;\n  message?: string;\n  data: Record<string, boolean>;\n}\n\nexport interface AddFavoriteResponse {\n  success: boolean;\n  message?: string;\n  data?: any;\n}\n\nexport const favoritesApi = {\n  // Obtener favoritos del usuario con paginación\n  getFavorites: async (params: { page: number; limit: number }) => {\n    const result = await apiService.get<FavoriteResponse>('/client/favorites', { params });\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Agregar vehículo a favoritos\n  addToFavorites: async (vehicleId: string) => {\n    const result = await apiService.post<AddFavoriteResponse>(`/client/favorites/${vehicleId}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Remover vehículo de favoritos\n  removeFromFavorites: async (vehicleId: string) => {\n    const result = await apiService.delete<{ success: boolean; message?: string }>(`/client/favorites/${vehicleId}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Verificar si un vehículo está en favoritos\n  isFavorite: async (vehicleId: string) => {\n    const result = await apiService.get<FavoriteStatusResponse>(`/client/favorites/${vehicleId}/status`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Obtener estados de favoritos para múltiples vehículos\n  getFavoritesStatus: async (vehicleIds: string[]) => {\n    const result = await apiService.post<MultipleFavoriteStatusResponse>('/client/favorites/status', {\n      vehicleIds\n    });\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Toggle favorito (agregar si no está, remover si está)\n  toggleFavorite: async (vehicleId: string) => {\n    try {\n      // Primero verificar el estado actual\n      const statusResult = await favoritesApi.isFavorite(vehicleId);\n      \n      if (statusResult.isFavorite) {\n        // Si está en favoritos, remover\n        return await favoritesApi.removeFromFavorites(vehicleId);\n      } else {\n        // Si no está en favoritos, agregar\n        return await favoritesApi.addToFavorites(vehicleId);\n      }\n    } catch (error: any) {\n      throw new Error(error.message || 'Error al cambiar estado de favorito');\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AAmCO,MAAM,eAAe;IAC1B,+CAA+C;IAC/C,cAAc,OAAO;QACnB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAmB,qBAAqB;YAAE;QAAO;QACpF,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,+BAA+B;IAC/B,gBAAgB,OAAO;QACrB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAsB,CAAC,kBAAkB,EAAE,WAAW;QAC1F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,gCAAgC;IAChC,qBAAqB,OAAO;QAC1B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,MAAM,CAAyC,CAAC,kBAAkB,EAAE,WAAW;QAC/G,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6CAA6C;IAC7C,YAAY,OAAO;QACjB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAyB,CAAC,kBAAkB,EAAE,UAAU,OAAO,CAAC;QACnG,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,wDAAwD;IACxD,oBAAoB,OAAO;QACzB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAiC,4BAA4B;YAC/F;QACF;QACA,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,wDAAwD;IACxD,gBAAgB,OAAO;QACrB,IAAI;YACF,qCAAqC;YACrC,MAAM,eAAe,MAAM,aAAa,UAAU,CAAC;YAEnD,IAAI,aAAa,UAAU,EAAE;gBAC3B,gCAAgC;gBAChC,OAAO,MAAM,aAAa,mBAAmB,CAAC;YAChD,OAAO;gBACL,mCAAmC;gBACnC,OAAO,MAAM,aAAa,cAAc,CAAC;YAC3C;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;IACF;AACF"}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/vehicles/favorite-button.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useContext } from \"react\"\nimport { Heart } from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\nimport { favoritesApi } from \"@/lib/api/favorites.api\"\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\"\nimport { UserContext } from \"@/context/user-context\"\nimport toast from \"react-hot-toast\"\nimport { cn } from \"@/lib/utils\"\n\n// Hook personalizado que no falla si no hay UserProvider\nfunction useSafeUser() {\n  const context = useContext(UserContext)\n  return context || { user: null, setUser: () => { }, session: null, setSession: () => { } }\n}\n\ninterface FavoriteButtonProps {\n  vehicleId: string\n  className?: string\n  size?: \"sm\" | \"default\" | \"lg\" | \"icon\"\n  variant?: \"default\" | \"secondary\" | \"ghost\" | \"outline\"\n  showToast?: boolean\n}\n\nexport default function FavoriteButton({\n  vehicleId,\n  className,\n  size = \"icon\",\n  variant = \"secondary\",\n  showToast = true\n}: FavoriteButtonProps) {\n  const { user } = useSafeUser()\n  const queryClient = useQueryClient()\n  const [isOptimistic, setIsOptimistic] = useState(false)\n\n  // Solo mostrar el botón si el usuario está autenticado y es de tipo client\n  const shouldShow = Boolean(user && user.userType === 'client')\n\n  // Query para verificar si el vehículo está en favoritos\n  const { data: favoriteStatus, isLoading } = useQuery({\n    queryKey: ['favorite-status', vehicleId],\n    queryFn: () => favoritesApi.isFavorite(vehicleId),\n    enabled: shouldShow,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n\n  // Mutation para toggle favorito\n  const toggleMutation = useMutation({\n    mutationFn: () => favoritesApi.toggleFavorite(vehicleId),\n    onMutate: async () => {\n      // Optimistic update\n      setIsOptimistic(true)\n      await queryClient.cancelQueries({ queryKey: ['favorite-status', vehicleId] })\n\n      const previousStatus = queryClient.getQueryData(['favorite-status', vehicleId])\n\n      // Actualizar optimísticamente\n      queryClient.setQueryData(['favorite-status', vehicleId], (old: any) => ({\n        ...old,\n        isFavorite: !old?.isFavorite\n      }))\n\n      return { previousStatus }\n    },\n    onError: (err, variables, context) => {\n      // Revertir en caso de error\n      if (context?.previousStatus) {\n        queryClient.setQueryData(['favorite-status', vehicleId], context.previousStatus)\n      }\n      if (showToast) {\n        toast.error('Error al actualizar favorito')\n      }\n      console.error('Error toggling favorite:', err)\n    },\n    onSuccess: (data) => {\n      // Invalidar queries relacionadas\n      queryClient.invalidateQueries({ queryKey: ['favorite-status', vehicleId] })\n      queryClient.invalidateQueries({ queryKey: ['favorites'] })\n\n      if (showToast && data?.success) {\n        toast.success(data.message || 'Favorito actualizado')\n      }\n    },\n    onSettled: () => {\n      setIsOptimistic(false)\n    }\n  })\n\n  const handleToggle = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n\n    if (!shouldShow) {\n      if (showToast) {\n        toast.error('Debes iniciar sesión como cliente para usar favoritos')\n      }\n      return\n    }\n\n    toggleMutation.mutate()\n  }\n\n  // No mostrar el botón si el usuario no está autenticado o no es client\n  if (!shouldShow) {\n    return null\n  }\n\n  const isFavorite = favoriteStatus?.isFavorite || false\n  const isProcessing = isLoading || toggleMutation.isPending || isOptimistic\n\n  return (\n    <Button\n      size={size}\n      variant={variant}\n      className={cn(\n        \"relative transition-all duration-200\",\n        isFavorite && \"bg-red-50 hover:bg-red-200 border-red-300\",\n        className\n      )}\n      onClick={handleToggle}\n      disabled={isProcessing}\n    >\n      <Heart\n        className={cn(\n          \"transition-all duration-200\",\n          size === \"sm\" ? \"h-3 w-3\" : size === \"lg\" ? \"h-6 w-6\" : \"h-4 w-4\",\n          isFavorite ? \"fill-red-500 text-red-500\" : \"text-gray-600\",\n          isProcessing && \"animate-pulse\"\n        )}\n      />\n      {isProcessing && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n    </Button>\n  )\n}\n\n// Hook personalizado para usar en otros componentes\nexport function useFavoriteStatus(vehicleId: string) {\n  const { user } = useSafeUser()\n  const shouldFetch = Boolean(user && user.userType === 'client')\n\n  return useQuery({\n    queryKey: ['favorite-status', vehicleId],\n    queryFn: () => favoritesApi.isFavorite(vehicleId),\n    enabled: shouldFetch,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n}\n\n// Hook para obtener múltiples estados de favoritos\nexport function useFavoritesStatus(vehicleIds: string[]) {\n  const { user } = useSafeUser()\n  const shouldFetch = Boolean(user && user.userType === 'client' && vehicleIds.length > 0)\n\n  return useQuery({\n    queryKey: ['favorites-status', vehicleIds],\n    queryFn: () => favoritesApi.getFavoritesStatus(vehicleIds),\n    enabled: shouldFetch,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,yDAAyD;AACzD,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,0IAAA,CAAA,cAAW;IACtC,OAAO,WAAW;QAAE,MAAM;QAAM,SAAS,KAAQ;QAAG,SAAS;QAAM,YAAY,KAAQ;IAAE;AAC3F;AAUe,SAAS,eAAe,EACrC,SAAS,EACT,SAAS,EACT,OAAO,MAAM,EACb,UAAU,WAAW,EACrB,YAAY,IAAI,EACI;IACpB,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,cAAc,CAAA,GAAA,8LAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,2EAA2E;IAC3E,MAAM,aAAa,QAAQ,QAAQ,KAAK,QAAQ,KAAK;IAErD,wDAAwD;IACxD,MAAM,EAAE,MAAM,cAAc,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,UAAU;YAAC;YAAmB;SAAU;QACxC,SAAS,IAAM,6IAAA,CAAA,eAAY,CAAC,UAAU,CAAC;QACvC,SAAS;QACT,WAAW,OAAO,KAAK;IACzB;IAEA,gCAAgC;IAChC,MAAM,iBAAiB,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD,EAAE;QACjC,YAAY,IAAM,6IAAA,CAAA,eAAY,CAAC,cAAc,CAAC;QAC9C,UAAU;YACR,oBAAoB;YACpB,gBAAgB;YAChB,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAmB;iBAAU;YAAC;YAE3E,MAAM,iBAAiB,YAAY,YAAY,CAAC;gBAAC;gBAAmB;aAAU;YAE9E,8BAA8B;YAC9B,YAAY,YAAY,CAAC;gBAAC;gBAAmB;aAAU,EAAE,CAAC,MAAa,CAAC;oBACtE,GAAG,GAAG;oBACN,YAAY,CAAC,KAAK;gBACpB,CAAC;YAED,OAAO;gBAAE;YAAe;QAC1B;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,4BAA4B;YAC5B,IAAI,SAAS,gBAAgB;gBAC3B,YAAY,YAAY,CAAC;oBAAC;oBAAmB;iBAAU,EAAE,QAAQ,cAAc;YACjF;YACA,IAAI,WAAW;gBACb,+JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;YACA,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;QACA,WAAW,CAAC;YACV,iCAAiC;YACjC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAmB;iBAAU;YAAC;YACzE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YAExD,IAAI,aAAa,MAAM,SAAS;gBAC9B,+JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,KAAK,OAAO,IAAI;YAChC;QACF;QACA,WAAW;YACT,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,CAAC,YAAY;YACf,IAAI,WAAW;gBACb,+JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;YACA;QACF;QAEA,eAAe,MAAM;IACvB;IAEA,uEAAuE;IACvE,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,MAAM,aAAa,gBAAgB,cAAc;IACjD,MAAM,eAAe,aAAa,eAAe,SAAS,IAAI;IAE9D,qBACE,kMAAC,0IAAA,CAAA,SAAM;QACL,MAAM;QACN,SAAS;QACT,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,wCACA,cAAc,6CACd;QAEF,SAAS;QACT,UAAU;;0BAEV,kMAAC,4MAAA,CAAA,QAAK;gBACJ,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,+BACA,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WACxD,aAAa,8BAA8B,iBAC3C,gBAAgB;;;;;;YAGnB,8BACC,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;AAGO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,cAAc,QAAQ,QAAQ,KAAK,QAAQ,KAAK;IAEtD,OAAO,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAmB;SAAU;QACxC,SAAS,IAAM,6IAAA,CAAA,eAAY,CAAC,UAAU,CAAC;QACvC,SAAS;QACT,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,SAAS,mBAAmB,UAAoB;IACrD,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,cAAc,QAAQ,QAAQ,KAAK,QAAQ,KAAK,YAAY,WAAW,MAAM,GAAG;IAEtF,OAAO,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAoB;SAAW;QAC1C,SAAS,IAAM,6IAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC;QAC/C,SAAS;QACT,WAAW,OAAO,KAAK;IACzB;AACF"}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { ButtonProps, buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\r\n  <nav\r\n    role=\"navigation\"\r\n    aria-label=\"pagination\"\r\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n    {...props}\r\n  />\r\n)\r\nPagination.displayName = \"Pagination\"\r\n\r\nconst PaginationContent = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    className={cn(\"flex flex-row items-center gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nPaginationContent.displayName = \"PaginationContent\"\r\n\r\nconst PaginationItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li ref={ref} className={cn(\"\", className)} {...props} />\r\n))\r\nPaginationItem.displayName = \"PaginationItem\"\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<ButtonProps, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nconst PaginationLink = ({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) => (\r\n  <a\r\n    aria-current={isActive ? \"page\" : undefined}\r\n    className={cn(\r\n      buttonVariants({\r\n        variant: isActive ? \"outline\" : \"ghost\",\r\n        size,\r\n      }),\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nPaginationLink.displayName = \"PaginationLink\"\r\n\r\nconst PaginationPrevious = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to previous page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pl-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronLeft className=\"h-4 w-4\" />\r\n    <span>Previous</span>\r\n  </PaginationLink>\r\n)\r\nPaginationPrevious.displayName = \"PaginationPrevious\"\r\n\r\nconst PaginationNext = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to next page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pr-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <span>Next</span>\r\n    <ChevronRight className=\"h-4 w-4\" />\r\n  </PaginationLink>\r\n)\r\nPaginationNext.displayName = \"PaginationNext\"\r\n\r\nconst PaginationEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"h-4 w-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n)\r\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AAAA;AAEA;AACA;;;;;;AAEA,MAAM,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,OAAoC,iBACtE,kMAAC;QACC,MAAK;QACL,cAAW;QACX,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,kCAAoB,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,+BAAiB,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;;;;;;AAEvD,eAAe,WAAW,GAAG;AAO7B,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB,iBACpB,kMAAC;QACC,gBAAc,WAAW,SAAS;QAClC,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACyC,iBAC5C,kMAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;0BAET,kMAAC,4NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,kMAAC;0BAAK;;;;;;;;;;;;AAGV,mBAAmB,WAAW,GAAG;AAEjC,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,GAAG,OACyC,iBAC5C,kMAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;0BAET,kMAAC;0BAAK;;;;;;0BACN,kMAAC,8NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,kMAAC;QACC,aAAW;QACX,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,kMAAC,wNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,kMAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG"}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/pagination-control.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { usePathname, useSearchParams } from \"next/navigation\"\nimport {\n  Pagination,\n  PaginationContent,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n  PaginationEllipsis\n} from \"@/components/ui/pagination\"\n\ninterface PaginationControlProps {\n  currentPage: number\n  totalPages: number\n  baseUrl?: string\n  className?: string\n  onPageChange?: (page: number) => void\n}\n\nexport function PaginationControl({\n  currentPage,\n  totalPages,\n  baseUrl,\n  className = \"\",\n  onPageChange\n}: PaginationControlProps) {\n  const pathname = usePathname()\n  const searchParams = useSearchParams()\n  const [windowWidth, setWindowWidth] = useState(\n    typeof window !== 'undefined' ? window.innerWidth : 0\n  )\n\n  // Actualizar el ancho de la ventana cuando cambia el tamaño\n  useEffect(() => {\n    const handleResize = () => setWindowWidth(window.innerWidth)\n    if (typeof window !== 'undefined') {\n      window.addEventListener('resize', handleResize)\n      return () => window.removeEventListener('resize', handleResize)\n    }\n  }, [])\n\n  // Función para crear una nueva URL con parámetros de búsqueda actualizados\n  const createPageUrl = (pageNumber: number) => {\n    if (onPageChange) return \"#\" // Si se usa onPageChange, el href es solo un placeholder\n\n    const params = new URLSearchParams(searchParams.toString())\n    params.set('page', pageNumber.toString())\n\n    if (baseUrl) {\n      return `${baseUrl}?${params.toString()}`\n    }\n\n    return `${pathname}?${params.toString()}`\n  }\n\n  // Calcular cuántos números de página mostrar basado en el ancho de la ventana\n  const getVisiblePageNumbers = () => {\n    if (totalPages <= 1) return [1]\n\n    let maxVisible = 1 // Mínimo siempre mostrar al menos 1\n\n    if (windowWidth > 640) maxVisible = 3 // sm\n    if (windowWidth > 768) maxVisible = 5 // md\n    if (windowWidth > 1024) maxVisible = 7 // lg\n\n    // Asegurarse de no mostrar más páginas de las que existen\n    maxVisible = Math.min(maxVisible, totalPages)\n\n    // Calcular el rango de páginas a mostrar\n    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2))\n    const endPage = Math.min(totalPages, startPage + maxVisible - 1)\n\n    // Ajustar si estamos cerca del final\n    if (endPage - startPage + 1 < maxVisible) {\n      startPage = Math.max(1, endPage - maxVisible + 1)\n    }\n\n    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i)\n  }\n\n  const handleClick = (pageNumber: number, e: React.MouseEvent) => {\n    if (onPageChange) {\n      e.preventDefault()\n      onPageChange(pageNumber)\n    }\n  }\n\n  const visiblePages = getVisiblePageNumbers()\n  const showLeftEllipsis = totalPages > 0 && visiblePages[0] > 1\n  const showRightEllipsis = totalPages > 0 && visiblePages[visiblePages.length - 1] < totalPages\n\n  if (totalPages <= 1) {\n    return null\n  }\n\n  return (\n    <Pagination className={className}>\n      <PaginationContent>\n        {currentPage > 1 && (\n          <PaginationItem>\n            <PaginationPrevious\n              href={createPageUrl(currentPage - 1)}\n              onClick={(e) => handleClick(currentPage - 1, e)}\n            />\n          </PaginationItem>\n        )}\n\n        {showLeftEllipsis && (\n          <>\n            <PaginationItem>\n              <PaginationLink\n                href={createPageUrl(1)}\n                onClick={(e) => handleClick(1, e)}\n              >\n                1\n              </PaginationLink>\n            </PaginationItem>\n            <PaginationItem>\n              <PaginationEllipsis />\n            </PaginationItem>\n          </>\n        )}\n\n        {visiblePages.map((pageNumber) => (\n          <PaginationItem key={pageNumber}>\n            <PaginationLink\n              href={createPageUrl(pageNumber)}\n              isActive={pageNumber === currentPage}\n              onClick={(e) => handleClick(pageNumber, e)}\n            >\n              {pageNumber}\n            </PaginationLink>\n          </PaginationItem>\n        ))}\n\n        {showRightEllipsis && (\n          <>\n            <PaginationItem>\n              <PaginationEllipsis />\n            </PaginationItem>\n            <PaginationItem>\n              <PaginationLink\n                href={createPageUrl(totalPages)}\n                onClick={(e) => handleClick(totalPages, e)}\n              >\n                {totalPages}\n              </PaginationLink>\n            </PaginationItem>\n          </>\n        )}\n\n        {currentPage < totalPages && (\n          <PaginationItem>\n            <PaginationNext\n              href={createPageUrl(currentPage + 1)}\n              onClick={(e) => handleClick(currentPage + 1, e)}\n            />\n          </PaginationItem>\n        )}\n      </PaginationContent>\n    </Pagination>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAsBO,SAAS,kBAAkB,EAChC,WAAW,EACX,UAAU,EACV,OAAO,EACP,YAAY,EAAE,EACd,YAAY,EACW;IACvB,MAAM,WAAW,CAAA,GAAA,iMAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,iMAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAC3C,6EAAoD;IAGtD,4DAA4D;IAC5D,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,IAAM,eAAe,OAAO,UAAU;QAC3D,uCAAmC;;QAGnC;IACF,GAAG,EAAE;IAEL,2EAA2E;IAC3E,MAAM,gBAAgB,CAAC;QACrB,IAAI,cAAc,OAAO,IAAI,yDAAyD;;QAEtF,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;QACxD,OAAO,GAAG,CAAC,QAAQ,WAAW,QAAQ;QAEtC,IAAI,SAAS;YACX,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;QAC1C;QAEA,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC3C;IAEA,8EAA8E;IAC9E,MAAM,wBAAwB;QAC5B,IAAI,cAAc,GAAG,OAAO;YAAC;SAAE;QAE/B,IAAI,aAAa,EAAE,oCAAoC;;QAEvD,IAAI,cAAc,KAAK,aAAa,EAAE,KAAK;;QAC3C,IAAI,cAAc,KAAK,aAAa,EAAE,KAAK;;QAC3C,IAAI,cAAc,MAAM,aAAa,EAAE,KAAK;;QAE5C,0DAA0D;QAC1D,aAAa,KAAK,GAAG,CAAC,YAAY;QAElC,yCAAyC;QACzC,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,aAAa;QAClE,MAAM,UAAU,KAAK,GAAG,CAAC,YAAY,YAAY,aAAa;QAE9D,qCAAqC;QACrC,IAAI,UAAU,YAAY,IAAI,YAAY;YACxC,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,aAAa;QACjD;QAEA,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU,YAAY;QAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAC/E;IAEA,MAAM,cAAc,CAAC,YAAoB;QACvC,IAAI,cAAc;YAChB,EAAE,cAAc;YAChB,aAAa;QACf;IACF;IAEA,MAAM,eAAe;IACrB,MAAM,mBAAmB,aAAa,KAAK,YAAY,CAAC,EAAE,GAAG;IAC7D,MAAM,oBAAoB,aAAa,KAAK,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,GAAG;IAEpF,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IAEA,qBACE,kMAAC,8IAAA,CAAA,aAAU;QAAC,WAAW;kBACrB,cAAA,kMAAC,8IAAA,CAAA,oBAAiB;;gBACf,cAAc,mBACb,kMAAC,8IAAA,CAAA,iBAAc;8BACb,cAAA,kMAAC,8IAAA,CAAA,qBAAkB;wBACjB,MAAM,cAAc,cAAc;wBAClC,SAAS,CAAC,IAAM,YAAY,cAAc,GAAG;;;;;;;;;;;gBAKlD,kCACC;;sCACE,kMAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,kMAAC,8IAAA,CAAA,iBAAc;gCACb,MAAM,cAAc;gCACpB,SAAS,CAAC,IAAM,YAAY,GAAG;0CAChC;;;;;;;;;;;sCAIH,kMAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,kMAAC,8IAAA,CAAA,qBAAkB;;;;;;;;;;;;gBAKxB,aAAa,GAAG,CAAC,CAAC,2BACjB,kMAAC,8IAAA,CAAA,iBAAc;kCACb,cAAA,kMAAC,8IAAA,CAAA,iBAAc;4BACb,MAAM,cAAc;4BACpB,UAAU,eAAe;4BACzB,SAAS,CAAC,IAAM,YAAY,YAAY;sCAEvC;;;;;;uBANgB;;;;;gBAWtB,mCACC;;sCACE,kMAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,kMAAC,8IAAA,CAAA,qBAAkB;;;;;;;;;;sCAErB,kMAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,kMAAC,8IAAA,CAAA,iBAAc;gCACb,MAAM,cAAc;gCACpB,SAAS,CAAC,IAAM,YAAY,YAAY;0CAEvC;;;;;;;;;;;;;gBAMR,cAAc,4BACb,kMAAC,8IAAA,CAAA,iBAAc;8BACb,cAAA,kMAAC,8IAAA,CAAA,iBAAc;wBACb,MAAM,cAAc,cAAc;wBAClC,SAAS,CAAC,IAAM,YAAY,cAAc,GAAG;;;;;;;;;;;;;;;;;;;;;;AAO3D"}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/client/favorites/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Heart, Search, Star, MapPin, Grid, List, Loader2 } from \"lucide-react\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { useQuery } from \"@tanstack/react-query\"\nimport { favoritesApi } from \"@/lib/api/favorites.api\"\nimport { useUser } from \"@/context/user-context\"\nimport FavoriteButton from \"@/components/vehicles/favorite-button\"\nimport { PaginationControl } from \"@/components/ui/pagination-control\"\nimport { useRouter, useSearchParams } from \"next/navigation\"\n\n// Mapeo de tipos de carrocería en español\nconst bodyTypeLabels: Record<string, string> = {\n  sedan: \"Sedan\",\n  suv: \"SUV\",\n  hatchback: \"Hatchback\",\n  pickup: \"Pickup\",\n  coupe: \"Coupe\",\n  convertible: \"Convertible\",\n  wagon: \"Wagon\",\n  van: \"Van\",\n  minivan: \"Minivan\",\n  targa: \"Targa\",\n  doublecab: \"Doble Cabina\",\n  truck: \"Camioneta\"\n};\n\nexport default function FavoritesPage() {\n  const { user } = useUser()\n  const router = useRouter()\n  const searchParams = useSearchParams()\n\n  // Verificar que el usuario sea de tipo client ANTES de cualquier hook\n  if (!user || user.userType !== 'client') {\n    return (\n      <div className=\"flex flex-col items-center justify-center min-h-[400px] text-center\">\n        <Heart className=\"h-16 w-16 text-gray-300 mb-4\" />\n        <h2 className=\"text-2xl font-semibold mb-2\">Acceso restringido</h2>\n        <p className=\"text-gray-600 mb-4\">\n          Solo los usuarios de tipo cliente pueden acceder a la página de favoritos.\n        </p>\n        <Button onClick={() => router.push('/dashboard')}>\n          Volver al dashboard\n        </Button>\n      </div>\n    )\n  }\n\n  // Estados para filtros y vista\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [selectedCategory, setSelectedCategory] = useState(\"all\")\n  const [selectedAvailability, setSelectedAvailability] = useState(\"all\")\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\")\n\n  // Paginación\n  const currentPage = parseInt(searchParams.get('page') || '1')\n  const limit = 12\n\n  // Query para obtener favoritos\n  const { data: favoritesData, isLoading, error } = useQuery({\n    queryKey: ['favorites', currentPage, limit],\n    queryFn: () => favoritesApi.getFavorites({ page: currentPage, limit }),\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n\n  const vehicles = favoritesData?.data || []\n  const pagination = favoritesData?.pagination\n\n  // Filtrar vehículos localmente\n  const filteredVehicles = vehicles.filter((vehicle: any) => {\n    const matchesSearch = \n      vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      vehicle.features?.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      vehicle.host?.name?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    const vehicleCategory = bodyTypeLabels[vehicle.bodyType] || vehicle.bodyType\n    const matchesCategory = selectedCategory === \"all\" || vehicleCategory === selectedCategory\n\n    const isAvailable = vehicle.status === \"active\"\n    const matchesAvailability = selectedAvailability === \"all\" ||\n      (selectedAvailability === \"available\" && isAvailable) ||\n      (selectedAvailability === \"unavailable\" && !isAvailable)\n\n    return matchesSearch && matchesCategory && matchesAvailability\n  })\n\n  const VehicleCard = ({ vehicle, isListView = false }: { vehicle: any; isListView?: boolean }) => (\n    <Link href={`/vehicles/${vehicle.id}`} key={vehicle.id}>\n      <Card className={`overflow-hidden hover:shadow-lg transition-shadow cursor-pointer ${isListView ? \"flex\" : \"\"}`}>\n        <div className={`relative ${isListView ? \"w-48 flex-shrink-0\" : \"\"}`}>\n          <Image\n            src={vehicle.images?.[0] || \"/placeholder.svg\"}\n            alt={`${vehicle.make} ${vehicle.model}`}\n            width={300}\n            height={200}\n            className={`object-cover ${isListView ? \"h-full w-full\" : \"w-full h-48\"}`}\n            unoptimized={true}\n          />\n          <div className=\"absolute top-2 right-2 z-10\" onClick={(e) => e.preventDefault()}>\n            <FavoriteButton vehicleId={vehicle.id} className=\"bg-white hover:bg-gray-50\" />\n          </div>\n          {vehicle.status !== \"active\" && (\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n              <Badge variant=\"secondary\" className=\"bg-white text-black\">\n                No disponible\n              </Badge>\n            </div>\n          )}\n        </div>\n        <CardContent className={`p-4 ${isListView ? \"flex-1\" : \"\"}`}>\n          <div className=\"flex justify-between items-start mb-2\">\n            <h3 className=\"font-semibold text-lg\">\n              {vehicle.make} {vehicle.model} {vehicle.year}\n            </h3>\n            <div className=\"text-right\">\n              <div className=\"text-xl font-bold\">${vehicle.price}</div>\n              <div className=\"text-sm text-gray-500\">por día</div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center mb-2\">\n            <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400 mr-1\" />\n            <span className=\"text-sm font-medium\">{vehicle.rating || 0}</span>\n            <span className=\"text-sm text-gray-500 ml-1\">({vehicle.reviews || 0} reseñas)</span>\n          </div>\n\n          <div className=\"flex items-center text-sm text-gray-600 mb-3\">\n            <MapPin className=\"h-4 w-4 mr-1\" />\n            {vehicle.features?.location || 'Ubicación no especificada'}\n          </div>\n\n          <div className=\"text-sm text-gray-600 mb-3\">\n            {vehicle.engineSize}L • {vehicle.transmission === 'automatic' ? 'Automático' : 'Manual'} • {bodyTypeLabels[vehicle.bodyType] || vehicle.bodyType}\n          </div>\n\n          <div className=\"flex flex-wrap gap-1 mb-3\">\n            {vehicle.amenities?.slice(0, 3).map((amenity: string, index: number) => (\n              <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                {amenity}\n              </Badge>\n            ))}\n            {vehicle.amenities?.length > 3 && (\n              <Badge variant=\"outline\" className=\"text-xs\">\n                +{vehicle.amenities.length - 3} más\n              </Badge>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </Link>\n  )\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-[400px]\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex flex-col items-center justify-center min-h-[400px] text-center\">\n        <Heart className=\"h-16 w-16 text-gray-300 mb-4\" />\n        <h2 className=\"text-2xl font-semibold mb-2\">Error al cargar favoritos</h2>\n        <p className=\"text-gray-600 mb-4\">\n          No se pudieron cargar tus vehículos favoritos. Por favor, intenta de nuevo.\n        </p>\n        <Button onClick={() => window.location.reload()}>\n          Reintentar\n        </Button>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-3xl font-bold tracking-tight\">Favoritos</h1>\n        <p className=\"text-muted-foreground\">\n          Tus vehículos guardados para futuras reservas ({pagination?.total || 0} vehículos)\n        </p>\n      </div>\n\n      {/* Filtros y controles */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">Filtros</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-col md:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Buscar por marca, modelo, ubicación...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n\n            <Select value={selectedCategory} onValueChange={setSelectedCategory}>\n              <SelectTrigger className=\"w-full md:w-48\">\n                <SelectValue placeholder=\"Categoría\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">Todas las categorías</SelectItem>\n                {Object.entries(bodyTypeLabels).map(([key, label]) => (\n                  <SelectItem key={key} value={label}>{label}</SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n\n            <Select value={selectedAvailability} onValueChange={setSelectedAvailability}>\n              <SelectTrigger className=\"w-full md:w-48\">\n                <SelectValue placeholder=\"Disponibilidad\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">Todos</SelectItem>\n                <SelectItem value=\"available\">Disponibles</SelectItem>\n                <SelectItem value=\"unavailable\">No disponibles</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <div className=\"flex gap-2\">\n              <Button\n                variant={viewMode === \"grid\" ? \"default\" : \"outline\"}\n                size=\"icon\"\n                onClick={() => setViewMode(\"grid\")}\n              >\n                <Grid className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant={viewMode === \"list\" ? \"default\" : \"outline\"}\n                size=\"icon\"\n                onClick={() => setViewMode(\"list\")}\n              >\n                <List className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Lista de vehículos */}\n      <div>\n        {filteredVehicles.length === 0 ? (\n          <Card>\n            <CardContent className=\"flex flex-col items-center justify-center py-12\">\n              <Heart className=\"h-12 w-12 text-muted-foreground mb-4\" />\n              <h3 className=\"text-lg font-semibold mb-2\">No se encontraron favoritos</h3>\n              <p className=\"text-muted-foreground text-center\">\n                {vehicles.length === 0\n                  ? \"Aún no tienes vehículos favoritos. Explora nuestro catálogo y guarda los que más te gusten.\"\n                  : \"No tienes vehículos favoritos que coincidan con los filtros seleccionados.\"\n                }\n              </p>\n              {vehicles.length === 0 && (\n                <Button className=\"mt-4\" onClick={() => router.push('/vehicles')}>\n                  Explorar vehículos\n                </Button>\n              )}\n            </CardContent>\n          </Card>\n        ) : (\n          <div className={viewMode === \"grid\" ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\" : \"space-y-4\"}>\n            {filteredVehicles.map((vehicle) => (\n              <VehicleCard key={vehicle.id} vehicle={vehicle} isListView={viewMode === \"list\"} />\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Paginación */}\n      {pagination && pagination.totalPages > 1 && (\n        <div className=\"flex justify-center\">\n          <PaginationControl\n            currentPage={pagination.page}\n            totalPages={pagination.totalPages}\n            baseUrl=\"/dashboard/client/favorites\"\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAhBA;;;;;;;;;;;;;;;;;AAkBA,0CAA0C;AAC1C,MAAM,iBAAyC;IAC7C,OAAO;IACP,KAAK;IACL,WAAW;IACX,QAAQ;IACR,OAAO;IACP,aAAa;IACb,OAAO;IACP,KAAK;IACL,SAAS;IACT,OAAO;IACP,WAAW;IACX,OAAO;AACT;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,iMAAA,CAAA,kBAAe,AAAD;IAEnC,sEAAsE;IACtE,IAAI,CAAC,QAAQ,KAAK,QAAQ,KAAK,UAAU;QACvC,qBACE,kMAAC;YAAI,WAAU;;8BACb,kMAAC,4MAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;8BACjB,kMAAC;oBAAG,WAAU;8BAA8B;;;;;;8BAC5C,kMAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,kMAAC,0IAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,OAAO,IAAI,CAAC;8BAAe;;;;;;;;;;;;IAKxD;IAEA,+BAA+B;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1D,aAAa;IACb,MAAM,cAAc,SAAS,aAAa,GAAG,CAAC,WAAW;IACzD,MAAM,QAAQ;IAEd,+BAA+B;IAC/B,MAAM,EAAE,MAAM,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QACzD,UAAU;YAAC;YAAa;YAAa;SAAM;QAC3C,SAAS,IAAM,6IAAA,CAAA,eAAY,CAAC,YAAY,CAAC;gBAAE,MAAM;gBAAa;YAAM;QACpE,WAAW,OAAO,KAAK;IACzB;IAEA,MAAM,WAAW,eAAe,QAAQ,EAAE;IAC1C,MAAM,aAAa,eAAe;IAElC,+BAA+B;IAC/B,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,gBACJ,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,QAAQ,EAAE,UAAU,cAAc,SAAS,WAAW,WAAW,OACzE,QAAQ,IAAI,EAAE,MAAM,cAAc,SAAS,WAAW,WAAW;QAEnE,MAAM,kBAAkB,cAAc,CAAC,QAAQ,QAAQ,CAAC,IAAI,QAAQ,QAAQ;QAC5E,MAAM,kBAAkB,qBAAqB,SAAS,oBAAoB;QAE1E,MAAM,cAAc,QAAQ,MAAM,KAAK;QACvC,MAAM,sBAAsB,yBAAyB,SAClD,yBAAyB,eAAe,eACxC,yBAAyB,iBAAiB,CAAC;QAE9C,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,aAAa,KAAK,EAA0C,iBAC1F,kMAAC,2KAAA,CAAA,UAAI;YAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;sBACnC,cAAA,kMAAC,wIAAA,CAAA,OAAI;gBAAC,WAAW,CAAC,iEAAiE,EAAE,aAAa,SAAS,IAAI;;kCAC7G,kMAAC;wBAAI,WAAW,CAAC,SAAS,EAAE,aAAa,uBAAuB,IAAI;;0CAClE,kMAAC,iLAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,MAAM,EAAE,CAAC,EAAE,IAAI;gCAC5B,KAAK,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;gCACvC,OAAO;gCACP,QAAQ;gCACR,WAAW,CAAC,aAAa,EAAE,aAAa,kBAAkB,eAAe;gCACzE,aAAa;;;;;;0CAEf,kMAAC;gCAAI,WAAU;gCAA8B,SAAS,CAAC,IAAM,EAAE,cAAc;0CAC3E,cAAA,kMAAC,4JAAA,CAAA,UAAc;oCAAC,WAAW,QAAQ,EAAE;oCAAE,WAAU;;;;;;;;;;;4BAElD,QAAQ,MAAM,KAAK,0BAClB,kMAAC;gCAAI,WAAU;0CACb,cAAA,kMAAC,yIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAsB;;;;;;;;;;;;;;;;;kCAMjE,kMAAC,wIAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,IAAI,EAAE,aAAa,WAAW,IAAI;;0CACzD,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAG,WAAU;;4CACX,QAAQ,IAAI;4CAAC;4CAAE,QAAQ,KAAK;4CAAC;4CAAE,QAAQ,IAAI;;;;;;;kDAE9C,kMAAC;wCAAI,WAAU;;0DACb,kMAAC;gDAAI,WAAU;;oDAAoB;oDAAE,QAAQ,KAAK;;;;;;;0DAClD,kMAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAI3C,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,0MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,kMAAC;wCAAK,WAAU;kDAAuB,QAAQ,MAAM,IAAI;;;;;;kDACzD,kMAAC;wCAAK,WAAU;;4CAA6B;4CAAE,QAAQ,OAAO,IAAI;4CAAE;;;;;;;;;;;;;0CAGtE,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,kNAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB,QAAQ,QAAQ,EAAE,YAAY;;;;;;;0CAGjC,kMAAC;gCAAI,WAAU;;oCACZ,QAAQ,UAAU;oCAAC;oCAAK,QAAQ,YAAY,KAAK,cAAc,eAAe;oCAAS;oCAAI,cAAc,CAAC,QAAQ,QAAQ,CAAC,IAAI,QAAQ,QAAQ;;;;;;;0CAGlJ,kMAAC;gCAAI,WAAU;;oCACZ,QAAQ,SAAS,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAiB,sBACpD,kMAAC,yIAAA,CAAA,QAAK;4CAAa,SAAQ;4CAAU,WAAU;sDAC5C;2CADS;;;;;oCAIb,QAAQ,SAAS,EAAE,SAAS,mBAC3B,kMAAC,yIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAU;4CACzC,QAAQ,SAAS,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;WAxDC,QAAQ,EAAE;;;;;IAiExD,IAAI,WAAW;QACb,qBACE,kMAAC;YAAI,WAAU;sBACb,cAAA,kMAAC,yNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,IAAI,OAAO;QACT,qBACE,kMAAC;YAAI,WAAU;;8BACb,kMAAC,4MAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;8BACjB,kMAAC;oBAAG,WAAU;8BAA8B;;;;;;8BAC5C,kMAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,kMAAC,0IAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;8BAAI;;;;;;;;;;;;IAKvD;IAEA,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;;kCACC,kMAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,kMAAC;wBAAE,WAAU;;4BAAwB;4BACa,YAAY,SAAS;4BAAE;;;;;;;;;;;;;0BAK3E,kMAAC,wIAAA,CAAA,OAAI;;kCACH,kMAAC,wIAAA,CAAA,aAAU;kCACT,cAAA,kMAAC,wIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,kMAAC,wIAAA,CAAA,cAAW;kCACV,cAAA,kMAAC;4BAAI,WAAU;;8CACb,kMAAC;oCAAI,WAAU;8CACb,cAAA,kMAAC;wCAAI,WAAU;;0DACb,kMAAC,8MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,kMAAC,yIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAKhB,kMAAC,0IAAA,CAAA,SAAM;oCAAC,OAAO;oCAAkB,eAAe;;sDAC9C,kMAAC,0IAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,kMAAC,0IAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,kMAAC,0IAAA,CAAA,gBAAa;;8DACZ,kMAAC,0IAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;gDACvB,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC/C,kMAAC,0IAAA,CAAA,aAAU;wDAAW,OAAO;kEAAQ;uDAApB;;;;;;;;;;;;;;;;;8CAKvB,kMAAC,0IAAA,CAAA,SAAM;oCAAC,OAAO;oCAAsB,eAAe;;sDAClD,kMAAC,0IAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,kMAAC,0IAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,kMAAC,0IAAA,CAAA,gBAAa;;8DACZ,kMAAC,0IAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,kMAAC,0IAAA,CAAA,aAAU;oDAAC,OAAM;8DAAY;;;;;;8DAC9B,kMAAC,0IAAA,CAAA,aAAU;oDAAC,OAAM;8DAAc;;;;;;;;;;;;;;;;;;8CAIpC,kMAAC;oCAAI,WAAU;;sDACb,kMAAC,0IAAA,CAAA,SAAM;4CACL,SAAS,aAAa,SAAS,YAAY;4CAC3C,MAAK;4CACL,SAAS,IAAM,YAAY;sDAE3B,cAAA,kMAAC,iNAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,kMAAC,0IAAA,CAAA,SAAM;4CACL,SAAS,aAAa,SAAS,YAAY;4CAC3C,MAAK;4CACL,SAAS,IAAM,YAAY;sDAE3B,cAAA,kMAAC,0MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,kMAAC;0BACE,iBAAiB,MAAM,KAAK,kBAC3B,kMAAC,wIAAA,CAAA,OAAI;8BACH,cAAA,kMAAC,wIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,kMAAC,4MAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,kMAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,kMAAC;gCAAE,WAAU;0CACV,SAAS,MAAM,KAAK,IACjB,gGACA;;;;;;4BAGL,SAAS,MAAM,KAAK,mBACnB,kMAAC,0IAAA,CAAA,SAAM;gCAAC,WAAU;gCAAO,SAAS,IAAM,OAAO,IAAI,CAAC;0CAAc;;;;;;;;;;;;;;;;yCAOxE,kMAAC;oBAAI,WAAW,aAAa,SAAS,yDAAyD;8BAC5F,iBAAiB,GAAG,CAAC,CAAC,wBACrB,kMAAC;4BAA6B,SAAS;4BAAS,YAAY,aAAa;2BAAvD,QAAQ,EAAE;;;;;;;;;;;;;;;YAOnC,cAAc,WAAW,UAAU,GAAG,mBACrC,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC,yJAAA,CAAA,oBAAiB;oBAChB,aAAa,WAAW,IAAI;oBAC5B,YAAY,WAAW,UAAU;oBACjC,SAAQ;;;;;;;;;;;;;;;;;AAMpB"}}]}