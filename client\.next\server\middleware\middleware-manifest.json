{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__316d0c2f._.js", "server/edge/chunks/edge-wrapper_c902b4af.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "+KMTG3juMS67G8EHA0iwvAqQA7vFRf/YWxYXm8iQ2oo=", "__NEXT_PREVIEW_MODE_ID": "ecd3903b11165ecbecddf9772fb99e24", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "029f7edfd7bf482b69f740a0153773cb37cd69175fcaf4170dec5a30cf8b6684", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "50869fd5120f4a225436157cf39daebf36d5cb931fab8b6d71193054ec125c11"}}}, "instrumentation": null, "functions": {}}