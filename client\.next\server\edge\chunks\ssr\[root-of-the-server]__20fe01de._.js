(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/ssr/[root-of-the-server]__20fe01de._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[project]/src/constants/env.ts [app-edge-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "envSchema": (()=>envSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-edge-rsc] (ecmascript)");
;
const envSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    NEXT_PUBLIC_API_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().url(),
    NODE_ENV: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
        'development',
        'production',
        'test'
    ]).default('development'),
    IS_DEV: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().transform((val)=>val === 'true')
});
// create the same object but with process.env
const env = envSchema.parse({
    NEXT_PUBLIC_API_URL: ("TURBOPACK compile-time value", "http://localhost:3000"),
    NODE_ENV: ("TURBOPACK compile-time value", "development"),
    IS_DEV: process.env.IS_DEV
});
const __TURBOPACK__default__export__ = env;
}}),
"[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7fffc66d044a7121f3de0a9c5ed63c896e5380b88c":"sendLogToLogflare"},"",""] */ __turbopack_context__.s({
    "sendLogToLogflare": (()=>sendLogToLogflare)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/build/webpack/loaders/next-flight-loader/server-reference.js [app-edge-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$server$2d$dom$2d$turbopack$2f$server$2e$edge$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-server-dom-turbopack/server.edge.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/app-render/encryption.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/build/webpack/loaders/next-flight-loader/action-validate.js [app-edge-rsc] (ecmascript)");
;
;
const LOGFLARE_API_URL = 'https://api.logflare.app/logs';
const LOGFLARE_API_KEY = process.env.LOGFLARE_API_KEY;
const LOGFLARE_SOURCE_ID = process.env.LOGFLARE_SOURCE_ID;
const IS_PAGES = process.env.PAGES === 'true';
const sendLogToLogflare = async (logEntry)=>{
    if (!IS_PAGES) return;
    const logflareEventBody = {
        event_message: logEntry.message,
        metadata: {
            ...logEntry.metadata
        }
    };
    // console.log('Logflare Event Body:', logflareEventBody);
    const init = {
        method: "POST",
        headers: {
            "X-API-KEY": LOGFLARE_API_KEY,
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            // source: sourceKey,
            log_entry: logflareEventBody.event_message,
            metadata: logflareEventBody.metadata
        })
    };
    await fetch(`${LOGFLARE_API_URL}?source=${LOGFLARE_SOURCE_ID}`, init);
};
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    sendLogToLogflare
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$server$2d$dom$2d$turbopack$2f$server$2e$edge$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(sendLogToLogflare, "7fffc66d044a7121f3de0a9c5ed63c896e5380b88c", null);
}}),
"[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f32d250dc1bdd0adce5c3c0000b707d3e6bcd2ce7":"getCookieServerByName","7f3fa197bdc82caa4a02116f6307b070d6c553f962":"setCookieServerByName","7f5229c7fe229d2322a500af6d52e732bd64101335":"deleteCookieServerByName"},"",""] */ __turbopack_context__.s({
    "deleteCookieServerByName": (()=>deleteCookieServerByName),
    "getCookieServerByName": (()=>getCookieServerByName),
    "setCookieServerByName": (()=>setCookieServerByName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/build/webpack/loaders/next-flight-loader/server-reference.js [app-edge-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$server$2d$dom$2d$turbopack$2f$server$2e$edge$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-server-dom-turbopack/server.edge.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/app-render/encryption.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$headers$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/headers.js [app-edge-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/request/cookies.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$server$2f$index$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cookies-next/lib/server/index.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/build/webpack/loaders/next-flight-loader/action-validate.js [app-edge-rsc] (ecmascript)");
;
;
;
;
const getCookieServerByName = async ({ name })=>{
    const cookie = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$server$2f$index$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["getCookie"])(name, {
        cookies: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["cookies"]
    });
    return cookie;
};
const setCookieServerByName = async ({ name, value })=>{
    console.log('setCookieServerByName', name, value);
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$server$2f$index$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["setCookie"])(name, value, {
        cookies: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["cookies"]
    });
};
const deleteCookieServerByName = async ({ name })=>{
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$server$2f$index$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["deleteCookie"])(name, {
        cookies: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["cookies"]
    });
};
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getCookieServerByName,
    setCookieServerByName,
    deleteCookieServerByName
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$server$2d$dom$2d$turbopack$2f$server$2e$edge$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCookieServerByName, "7f32d250dc1bdd0adce5c3c0000b707d3e6bcd2ce7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$server$2d$dom$2d$turbopack$2f$server$2e$edge$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(setCookieServerByName, "7f3fa197bdc82caa4a02116f6307b070d6c553f962", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$server$2d$dom$2d$turbopack$2f$server$2e$edge$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteCookieServerByName, "7f5229c7fe229d2322a500af6d52e732bd64101335", null);
}}),
"[project]/src/constants/index.ts [app-edge-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BEARER_COOKIE_NAME": (()=>BEARER_COOKIE_NAME),
    "PENDING_INVITATION_COOKIE": (()=>PENDING_INVITATION_COOKIE),
    "PENDING_INVITATION_EMAIL_COOKIE": (()=>PENDING_INVITATION_EMAIL_COOKIE),
    "PENDING_INVITATION_ORG_ID_COOKIE": (()=>PENDING_INVITATION_ORG_ID_COOKIE)
});
const BEARER_COOKIE_NAME = "bearer_token";
const PENDING_INVITATION_COOKIE = 'pending_invitation';
const PENDING_INVITATION_EMAIL_COOKIE = 'pending_invitation_email';
const PENDING_INVITATION_ORG_ID_COOKIE = 'pending_invitation_org_id';
}}),
"[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f6e5dd6d98cabce2becd25e33365cd44b2d51173d":"getServerSession"},"",""] */ __turbopack_context__.s({
    "getServerSession": (()=>getServerSession)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/build/webpack/loaders/next-flight-loader/server-reference.js [app-edge-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$server$2d$dom$2d$turbopack$2f$server$2e$edge$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-server-dom-turbopack/server.edge.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/app-render/encryption.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$headers$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/headers.js [app-edge-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/request/cookies.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$headers$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/request/headers.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$react$2e$react$2d$server$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/react.react-server.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$fetch$2f$fetch$2f$dist$2f$index$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@better-fetch/fetch/dist/index.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$env$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/env.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/index.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/navigation.react-server.js [app-edge-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$client$2f$components$2f$redirect$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/client/components/redirect.js [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/build/webpack/loaders/next-flight-loader/action-validate.js [app-edge-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const getServerSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$react$2e$react$2d$server$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (name, { shouldRedirect = true } = {})=>{
    const headersList = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$headers$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["headers"])();
    const sessionHeader = headersList.get('session');
    if (sessionHeader) {
        const session = JSON.parse(sessionHeader);
        if (session && session.user) {
            return session;
        }
    }
    const cookiesStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    const allCookies = cookiesStore.toString();
    try {
        const token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["getCookieServerByName"])({
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["BEARER_COOKIE_NAME"]
        });
        const { data: session } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$fetch$2f$fetch$2f$dist$2f$index$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["betterFetch"])("/api/auth/get-session", {
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$env$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["default"].NEXT_PUBLIC_API_URL,
            headers: {
                cookie: allCookies,
                'x-function-call': `${name}`
            },
            auth: {
                type: 'Bearer',
                token: ()=>{
                    if (token) {
                        return token; // No truncar el token
                    }
                }
            }
        });
        console.log('Session on [getServerSession]: ', session);
        if (token && !session) {
            // remove token from cookie
            cookiesStore.delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["BEARER_COOKIE_NAME"]);
        }
        // log session using sendLogToLogflare
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["sendLogToLogflare"])({
            message: 'Session on [getServerSession]',
            metadata: {
                session: session
            }
        });
        // if (!session) return redirect('/sign-in');
        if (!session && shouldRedirect) return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$client$2f$components$2f$redirect$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/sign-in');
        return session;
    } catch (error) {
        console.error(`Error getting session on [${name}]`, error);
        // return null;
        throw error;
    }
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getServerSession
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$server$2d$dom$2d$turbopack$2f$server$2e$edge$2e$js__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getServerSession, "7f6e5dd6d98cabce2becd25e33365cd44b2d51173d", null);
}}),
"[project]/.next-internal/server/app/(root)/vehicles/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)\" } [app-edge-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)");
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/(root)/vehicles/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)\" } [app-edge-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(root)/vehicles/page/actions.js { ACTIONS_MODULE0 => "[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)" } [app-edge-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(root)/vehicles/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)\" } [app-edge-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "7f32d250dc1bdd0adce5c3c0000b707d3e6bcd2ce7": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["getCookieServerByName"]),
    "7f3fa197bdc82caa4a02116f6307b070d6c553f962": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["setCookieServerByName"]),
    "7f5229c7fe229d2322a500af6d52e732bd64101335": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["deleteCookieServerByName"]),
    "7f6e5dd6d98cabce2becd25e33365cd44b2d51173d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["getServerSession"]),
    "7fffc66d044a7121f3de0a9c5ed63c896e5380b88c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__["sendLogToLogflare"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(root)/vehicles/page/actions.js { ACTIONS_MODULE0 => "[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)" } [app-edge-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(root)/vehicles/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)\" } [app-edge-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "7f32d250dc1bdd0adce5c3c0000b707d3e6bcd2ce7": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f32d250dc1bdd0adce5c3c0000b707d3e6bcd2ce7"]),
    "7f3fa197bdc82caa4a02116f6307b070d6c553f962": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f3fa197bdc82caa4a02116f6307b070d6c553f962"]),
    "7f5229c7fe229d2322a500af6d52e732bd64101335": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f5229c7fe229d2322a500af6d52e732bd64101335"]),
    "7f6e5dd6d98cabce2becd25e33365cd44b2d51173d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f6e5dd6d98cabce2becd25e33365cd44b2d51173d"]),
    "7fffc66d044a7121f3de0a9c5ed63c896e5380b88c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7fffc66d044a7121f3de0a9c5ed63c896e5380b88c"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(root)/vehicles/page/actions.js { ACTIONS_MODULE0 => "[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)" } [app-edge-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$root$292f$vehicles$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$getSession$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$log$2d$requests$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$cookies$2e$ts__$5b$app$2d$edge$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$edge$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(root)/vehicles/page/actions.js { ACTIONS_MODULE0 => "[project]/src/actions/getSession.ts [app-edge-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/lib/log-requests.ts [app-edge-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/actions/cookies.ts [app-edge-rsc] (ecmascript)" } [app-edge-rsc] (server actions loader, ecmascript) <exports>');
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__20fe01de._.js.map