{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\nimport { DateTime } from \"luxon\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n/**\r\n * Normaliza una fecha ISO a un objeto Date de JavaScript establecido a medianoche en la zona horaria local\r\n * @param dateStr Fecha en formato ISO string\r\n * @returns Objeto Date normalizado\r\n */\r\nexport function normalizeDate(dateStr: string): Date {\r\n  return DateTime.fromISO(dateStr)\r\n    .setZone('local')\r\n    .startOf('day')\r\n    .toJSDate();\r\n}\r\n\r\n/**\r\n * Convierte un array de fechas ISO a objetos Date normalizados\r\n * @param dateStrings Array de fechas en formato ISO string\r\n * @returns Array de objetos Date normalizados\r\n */\r\nexport function normalizeDates(dateStrings: string[]): Date[] {\r\n  if (!dateStrings || !Array.isArray(dateStrings)) return [];\r\n  return dateStrings.map(normalizeDate);\r\n}\r\n\r\n/**\r\n * Convierte un ISO a un string con formato \"dd de MMM de yyyy\"\r\n * @param date Fecha en formato ISO string\r\n * @returns String formateado\r\n */\r\nexport function formatISODate(date: string): string {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMM \\'de\\' yyyy', { locale: 'es' });\r\n}\r\n\r\n/**\r\n * Verifica si una fecha está en un array de fechas deshabilitadas\r\n * @param date Fecha a verificar\r\n * @param disabledDates Array de fechas deshabilitadas\r\n * @returns true si la fecha está deshabilitada, false en caso contrario\r\n */\r\nexport function isDateDisabled(date: Date, disabledDates: Date[]): boolean {\r\n  const luxonDate = DateTime.fromJSDate(date)\r\n    .setZone('local')\r\n    .startOf('day');\r\n\r\n  return disabledDates.some(disabledDate => {\r\n    const luxonDisabledDate = DateTime.fromJSDate(disabledDate);\r\n    return luxonDate.hasSame(luxonDisabledDate, 'day');\r\n  });\r\n}\r\n\r\n/**\r\n * Formatea una fecha a un string en formato: \"dd de MMM de yyyy\"\r\n * @param date Fecha a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatDate(date: string) {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMMM, yyyy', { locale: 'es' });\r\n}\r\n\r\n/** \r\n * Formatea un número a un string en formato de moneda local, por default MXN\r\n * @param value Número a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatCurrency(value: number, currency: string = 'MXN') {\r\n  return new Intl.NumberFormat('es-MX', { style: 'currency', currency }).format(value);\r\n\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAOzE,SAAS,cAAc,OAAe;IAC3C,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SACrB,OAAO,CAAC,SACR,OAAO,CAAC,OACR,QAAQ;AACb;AAOO,SAAS,eAAe,WAAqB;IAClD,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO,CAAC,cAAc,OAAO,EAAE;IAC1D,OAAO,YAAY,GAAG,CAAC;AACzB;AAOO,SAAS,cAAc,IAAY;IACxC,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,4BAA4B;QAAE,QAAQ;IAAK;AACzD;AAQO,SAAS,eAAe,IAAU,EAAE,aAAqB;IAC9D,MAAM,YAAY,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,MACnC,OAAO,CAAC,SACR,OAAO,CAAC;IAEX,OAAO,cAAc,IAAI,CAAC,CAAA;QACxB,MAAM,oBAAoB,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC9C,OAAO,UAAU,OAAO,CAAC,mBAAmB;IAC9C;AACF;AAOO,SAAS,WAAW,IAAY;IACrC,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,uBAAuB;QAAE,QAAQ;IAAK;AACpD;AAOO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY;IAAS,GAAG,MAAM,CAAC;AAEhF", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: ButtonProps) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACS;IACZ,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28root%29/vehicles/%5Bid%5D/error.tsx"], "sourcesContent": ["'use client';\nimport React from 'react'\nimport { Button } from '@/components/ui/button'\nimport { useRouter } from 'next/navigation';\n\nexport default function ErrorPage() {\n  const router = useRouter();\n  return (\n    <div className=\"container mx-auto py-8 min-h-screen flex flex-col justify-center items-center\">\n      <h2 className=\"text-2xl font-bold\">Error</h2>\n      <p className=\"text-muted-foreground\">No se pudo cargar la información del vehículo.</p>\n      <Button onClick={async () => {\n        router.push(\"/vehicles\")\n      }} className=\"mt-4\">\n        Volver a vehículos\n      </Button>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAqB;;;;;;0BACnC,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;0BACrC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS;oBACf,OAAO,IAAI,CAAC;gBACd;gBAAG,WAAU;0BAAO;;;;;;;;;;;;AAK1B;GAbwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}