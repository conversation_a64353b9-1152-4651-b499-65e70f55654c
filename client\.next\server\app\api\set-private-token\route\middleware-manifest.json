{"sorted_middleware": ["/api/set-private-token/route"], "middleware": {}, "instrumentation": null, "functions": {"/api/set-private-token/route": {"files": ["server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/api/set-private-token/route_client-reference-manifest.js", "server/edge/chunks/_next-internal_server_app_api_set-private-token_route_actions_5dd75a6a.js", "server/edge/chunks/_next-internal_server_app_api_set-private-token_route_actions_a0c9cb41.js", "server/edge/chunks/node_modules_next_dist_esm_5fd06144._.js", "server/edge/chunks/node_modules_next_dist_compiled_22b1f48f._.js", "server/edge/chunks/node_modules_next_dist_9f2bc3b3._.js", "server/edge/chunks/edge-wrapper_5972b91f.js", "server/edge/chunks/node_modules_78e5cece._.js", "server/edge/chunks/[root-of-the-server]__5afac32a._.js", "server/edge/chunks/edge-wrapper_7d9afc99.js", "server/app/api/set-private-token/route/react-loadable-manifest.js"], "name": "/api/set-private-token", "page": "/api/set-private-token/route", "matchers": [{"regexp": "^/api/set\\-private\\-token(?:/)?$", "originalSource": "/api/set-private-token"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "+KMTG3juMS67G8EHA0iwvAqQA7vFRf/YWxYXm8iQ2oo=", "__NEXT_PREVIEW_MODE_ID": "ecd3903b11165ecbecddf9772fb99e24", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "029f7edfd7bf482b69f740a0153773cb37cd69175fcaf4170dec5a30cf8b6684", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "50869fd5120f4a225436157cf39daebf36d5cb931fab8b6d71193054ec125c11"}}}}