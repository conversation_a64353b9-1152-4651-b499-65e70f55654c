{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/react-scan.tsx"], "sourcesContent": ["'use client';\r\nimport { useEffect, useState } from 'react';\r\nimport { scan } from 'react-scan';\r\n\r\nexport function ReactScan() {\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isMounted) {\r\n      scan();\r\n    }\r\n  }, [isMounted]);\r\n  return null\r\n}"], "names": ["c", "_c", "useEffect", "useState", "scan", "ReactScan", "$", "$i", "Symbol", "for", "isMounted", "setIsMounted", "t0", "t1", "t2", "t3"], "mappings": ";;;AAAa,SAAAA,CAAA,IAAAC,EAAA;AACb,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,IAAI,QAAQ,YAAY;;AAFjC,YAAY;;;;AAIL;;IAAA,MAAAE,CAAA,mLAAAL,IAAAA,AAAA,EAAA;IAAA,IAAAK,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,CAAAI,SAAA,EAAAC,YAAA,CAAA,oKAAkCR,YAAAA,AAAA,EAAA,KAAc,CAAC;IAAC,IAAAS,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAExCG,EAAA,GAAAA,CAAA;YACRD,YAAY,CAAA,IAAK,CAAC;QAAA;QACjBE,EAAA,GAAA,EAAA;QAAEP,CAAA,CAAA,EAAA,GAAAM,EAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAN,CAAA,CAAA,EAAA;QAAAO,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;sKAFLJ,YAAAA,AAAA,EAAUU,EAET,EAAEC,EAAE,CAAC;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAT,CAAA,CAAA,EAAA,KAAAI,SAAA,EAAA;QAEII,EAAA,GAAAA,CAAA;YAAA,IACJJ,SAAS,EAAA;uKACXN,OAAAA,AAAA,CAAK,CAAC;YAAA;QAAA;QAEPW,EAAA,GAAA;YAACL,SAAS;SAAA;QAACJ,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAQ,EAAA;QAAAR,CAAA,CAAA,EAAA,GAAAS,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAR,CAAA,CAAA,EAAA;QAAAS,EAAA,GAAAT,CAAA,CAAA,EAAA;IAAA;sKAJdJ,YAAAA,AAAA,EAAUY,EAIT,EAAEC,EAAW,CAAC;IAAA,OAAA;AAAA;;KAXVV,UAAA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/providers/Providers.tsx"], "sourcesContent": ["\"use client\"\r\n// Client providers:\r\nimport * as React from \"react\"\r\n// import { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\r\n\r\nconst queryClient = new QueryClient()\r\n\r\nexport function Providers({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  return (\r\n    <>\r\n      {/* <NextThemesProvider\r\n        attribute=\"class\"\r\n        defaultTheme=\"system\"\r\n        enableSystem\r\n        disableTransitionOnChange\r\n      > */}\r\n        <QueryClientProvider client={queryClient}>\r\n\r\n        <>{children}</>\r\n        </QueryClientProvider>\r\n      {/* </NextThemesProvider> */}\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA,oEAAoE;AACpE;AAAA;AAJA;;;AAMA,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW;AAE5B,SAAS,UAAU,EACxB,QAAQ,EAGT;IACC,qBACE;kBAOI,cAAA,6LAAC,yLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAE7B,cAAA;0BAAG;;;;;;;;AAKX;KApBgB", "debugId": null}}]}