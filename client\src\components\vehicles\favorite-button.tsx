"use client"

import { useState, useEffect, useContext } from "react"
import { Heart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { favoritesApi } from "@/lib/api/favorites.api"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { UserContext } from "@/context/user-context"
import toast from "react-hot-toast"
import { cn } from "@/lib/utils"

// Hook personalizado que no falla si no hay UserProvider
function useSafeUser() {
  const context = useContext(UserContext)
  return context || { user: null, setUser: () => { }, session: null, setSession: () => { } }
}

interface FavoriteButtonProps {
  vehicleId: string
  className?: string
  size?: "sm" | "default" | "lg" | "icon"
  variant?: "default" | "secondary" | "ghost" | "outline"
  showToast?: boolean
}

export default function FavoriteButton({
  vehicleId,
  className,
  size = "icon",
  variant = "secondary",
  showToast = true
}: FavoriteButtonProps) {
  const { user } = useSafeUser()
  const queryClient = useQueryClient()
  const [isOptimistic, setIsOptimistic] = useState(false)

  // Solo mostrar el botón si el usuario está autenticado y es de tipo client
  const shouldShow = user && user.userType === 'client'

  // Query para verificar si el vehículo está en favoritos
  const { data: favoriteStatus, isLoading } = useQuery({
    queryKey: ['favorite-status', vehicleId],
    queryFn: () => favoritesApi.isFavorite(vehicleId),
    enabled: shouldShow,
    staleTime: 1000 * 60 * 5, // 5 minutos
  })

  // Mutation para toggle favorito
  const toggleMutation = useMutation({
    mutationFn: () => favoritesApi.toggleFavorite(vehicleId),
    onMutate: async () => {
      // Optimistic update
      setIsOptimistic(true)
      await queryClient.cancelQueries({ queryKey: ['favorite-status', vehicleId] })

      const previousStatus = queryClient.getQueryData(['favorite-status', vehicleId])

      // Actualizar optimísticamente
      queryClient.setQueryData(['favorite-status', vehicleId], (old: any) => ({
        ...old,
        isFavorite: !old?.isFavorite
      }))

      return { previousStatus }
    },
    onError: (err, variables, context) => {
      // Revertir en caso de error
      if (context?.previousStatus) {
        queryClient.setQueryData(['favorite-status', vehicleId], context.previousStatus)
      }
      if (showToast) {
        toast.error('Error al actualizar favorito')
      }
      console.error('Error toggling favorite:', err)
    },
    onSuccess: (data) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ['favorite-status', vehicleId] })
      queryClient.invalidateQueries({ queryKey: ['favorites'] })

      if (showToast && data?.success) {
        toast.success(data.message || 'Favorito actualizado')
      }
    },
    onSettled: () => {
      setIsOptimistic(false)
    }
  })

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!shouldShow) {
      if (showToast) {
        toast.error('Debes iniciar sesión como cliente para usar favoritos')
      }
      return
    }

    toggleMutation.mutate()
  }

  // No mostrar el botón si el usuario no está autenticado o no es client
  if (!shouldShow) {
    return null
  }

  const isFavorite = favoriteStatus?.isFavorite || false
  const isProcessing = isLoading || toggleMutation.isPending || isOptimistic

  return (
    <Button
      size={size}
      variant={variant}
      className={cn(
        "relative transition-all duration-200",
        isFavorite && "bg-red-50 hover:bg-red-100 border-red-200",
        className
      )}
      onClick={handleToggle}
      disabled={isProcessing}
    >
      <Heart
        className={cn(
          "transition-all duration-200",
          size === "sm" ? "h-3 w-3" : size === "lg" ? "h-6 w-6" : "h-4 w-4",
          isFavorite ? "fill-red-500 text-red-500" : "text-gray-600",
          isProcessing && "animate-pulse"
        )}
      />
      {isProcessing && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </Button>
  )
}

// Hook personalizado para usar en otros componentes
export function useFavoriteStatus(vehicleId: string) {
  const { user } = useSafeUser()
  const shouldFetch = user && user.userType === 'client'

  return useQuery({
    queryKey: ['favorite-status', vehicleId],
    queryFn: () => favoritesApi.isFavorite(vehicleId),
    enabled: shouldFetch,
    staleTime: 1000 * 60 * 5, // 5 minutos
  })
}

// Hook para obtener múltiples estados de favoritos
export function useFavoritesStatus(vehicleIds: string[]) {
  const { user } = useSafeUser()
  const shouldFetch = user && user.userType === 'client' && vehicleIds.length > 0

  return useQuery({
    queryKey: ['favorites-status', vehicleIds],
    queryFn: () => favoritesApi.getFavoritesStatus(vehicleIds),
    enabled: shouldFetch,
    staleTime: 1000 * 60 * 5, // 5 minutos
  })
}
