{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/constants/env.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const envSchema = z.object({\r\n  NEXT_PUBLIC_API_URL: z.string().url(),\r\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\r\n  IS_DEV: z.string().optional().transform((val) => val === 'true'),\r\n});\r\n\r\nexport type Env = z.infer<typeof envSchema>;\r\n\r\n// create the same object but with process.env\r\nconst env = envSchema.parse({\r\n  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n  NODE_ENV: process.env.NODE_ENV,\r\n  IS_DEV: process.env.IS_DEV,\r\n});\r\n\r\nexport default env;\r\n"], "names": [], "mappings": ";;;;AAYuB;AAZvB;;AAEO,MAAM,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,qBAAqB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IACnC,UAAU,uIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAChE,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,MAAQ,QAAQ;AAC3D;AAIA,8CAA8C;AAC9C,MAAM,MAAM,UAAU,KAAK,CAAC;IAC1B,mBAAmB;IACnB,QAAQ;IACR,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,MAAM;AAC5B;uCAEe", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\r\nimport env from \"../constants/env\";\r\nimport { BEARER_COOKIE_NAME } from '@/constants';\r\nimport { getCookie } from 'cookies-next/client';\r\n// import { sendLogToLogflare } from '@/lib/log-requests';\r\n\r\nfunction checkIfIsClient() {\r\n    return typeof window !== 'undefined';\r\n}\r\n\r\ntype ApiResponse<T> =\r\n    {\r\n        success: true;\r\n        data: T; status:\r\n        number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: InternalAxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: undefined\r\n    }\r\n    |\r\n    {\r\n        success: false;\r\n        data: undefined;\r\n        status: number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: AxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: any\r\n    };\r\n\r\ntype ErrorHandler = (error: any) => void;\r\n\r\nconst baseURL = env.NEXT_PUBLIC_API_URL;\r\n\r\nclass ApiService {\r\n    private axiosInstance: AxiosInstance;\r\n    baseURL: string = '';\r\n\r\n    /** \r\n     * @param version - The version of the API to use. Defaults to 'v1'.\r\n     */\r\n    constructor(\r\n        {\r\n            version,\r\n            prefix\r\n        }:\r\n            {\r\n                version?: 'v1' | 'v2',\r\n                prefix?: 'api' | 'dash-utils'\r\n            } =\r\n            {\r\n                version: 'v1',\r\n                prefix: 'api'\r\n            }) {\r\n\r\n\r\n        // Version is only available for api prefix so if prefix is dash-utils, version is not used\r\n        this.baseURL = `${baseURL}/${prefix}${prefix === 'api' ? `/${version}` : ''}`;\r\n\r\n        this.axiosInstance = axios.create({\r\n            baseURL: this.baseURL,\r\n            withCredentials: true\r\n        });\r\n    }\r\n\r\n    private async setHeaders() {\r\n        const isClient = checkIfIsClient();\r\n        if (isClient) {\r\n            return {};\r\n\r\n        };\r\n        const headers = (await import('next/headers')).headers;\r\n        const rawHeaders = await headers();\r\n        const headersObj: Record<string, string> = {};\r\n        rawHeaders.forEach((value, key) => {\r\n            headersObj[key] = value;\r\n        });\r\n        return headersObj;\r\n    }\r\n\r\n    private async request<T>(\r\n        method: 'get' | 'post' | 'patch' | 'put' | 'delete',\r\n        path: string,\r\n        config?: AxiosRequestConfig,\r\n        onError?: ErrorHandler\r\n    ): Promise<ApiResponse<T>> {\r\n        try {\r\n            const isClient = checkIfIsClient();\r\n            const headers = await this.setHeaders();\r\n            let token = ''\r\n\r\n            if (isClient) {\r\n                token = getCookie(BEARER_COOKIE_NAME) as string;\r\n            } else {\r\n                const getCookieServerByName = (await import('@/actions/cookies')).getCookieServerByName;\r\n                token = await getCookieServerByName({ name: BEARER_COOKIE_NAME }) as string;\r\n                if (path.includes('/files/download')) {\r\n                    console.log('Token on request for /api/v1/files/download: ', token);\r\n                }\r\n            }\r\n\r\n            const requestHeaders = {\r\n                    cookie: isClient ? undefined : headers.cookie,\r\n                    'x-dashboard-call': 'true',\r\n                    Authorization: `Bearer ${token}`,\r\n                    ...config?.headers\r\n                }\r\n\r\n            if (path.includes('/files/download')) {\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n                console.log('Headers: ', headers);\r\n                console.log('Request on /api/v1/files/download: ', requestHeaders);\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n            }\r\n\r\n            const response = await this.axiosInstance({\r\n                method,\r\n                url: path,\r\n                ...config,\r\n                headers: requestHeaders,\r\n            });\r\n            // Simplificamos el objeto de respuesta para evitar problemas de serialización\r\n            return {\r\n                success: true,\r\n                data: response.data,\r\n                status: response.status,\r\n                statusText: response.statusText,\r\n                headers: response.headers,\r\n                config: response.config,\r\n                request: response.request,\r\n                error: undefined\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error in request:', error?.config?.url);\r\n\r\n            if (onError) onError(error);\r\n            await this.handleError(error);\r\n            return {\r\n                success: false,\r\n                data: undefined,\r\n                status: error.response?.status || 500,\r\n                statusText: error.response?.statusText || 'Unknown Error',\r\n                headers: error.response?.headers || {},\r\n                config: error.config || {},\r\n                request: error.request || {},\r\n                error: error?.response?.data || error.message,\r\n            };\r\n        }\r\n    }\r\n\r\n    public async get<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('get', path, config, onError);\r\n    }\r\n\r\n    public async post<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('post', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async patch<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('patch', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async put<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('put', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async delete<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('delete', path, config, onError);\r\n    }\r\n\r\n    private async handleError(error: any) {\r\n        console.error('API Error:', {\r\n            message: error.message,\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n        });\r\n    }\r\n}\r\n\r\nexport const apiService = new ApiService();\r\n\r\nexport const dashUtilsService = new ApiService({ prefix: 'dash-utils' });\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACA,0DAA0D;AAE1D,SAAS;IACL,OAAO,aAAkB;AAC7B;AA2BA,MAAM,UAAU,0HAAA,CAAA,UAAG,CAAC,mBAAmB;AAEvC,MAAM;IACM,cAA6B;IACrC,UAAkB,GAAG;IAErB;;KAEC,GACD,YACI,EACI,OAAO,EACP,MAAM,EAKL,GACD;QACI,SAAS;QACT,QAAQ;IACZ,CAAC,CAAE;QAGP,2FAA2F;QAC3F,IAAI,CAAC,OAAO,GAAG,GAAG,QAAQ,CAAC,EAAE,SAAS,WAAW,QAAQ,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI;QAE7E,IAAI,CAAC,aAAa,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC9B,SAAS,IAAI,CAAC,OAAO;YACrB,iBAAiB;QACrB;IACJ;IAEA,MAAc,aAAa;QACvB,MAAM,WAAW;QACjB,wCAAc;YACV,OAAO,CAAC;QAEZ;;QACA,MAAM;QACN,MAAM;QACN,MAAM;IAKV;IAEA,MAAc,QACV,MAAmD,EACnD,IAAY,EACZ,MAA2B,EAC3B,OAAsB,EACC;QACvB,IAAI;YACA,MAAM,WAAW;YACjB,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;YACrC,IAAI,QAAQ;YAEZ,wCAAc;gBACV,QAAQ,CAAA,GAAA,4JAAA,CAAA,YAAS,AAAD,EAAE,4HAAA,CAAA,qBAAkB;YACxC,OAAO;;YAMP;YAEA,MAAM,iBAAiB;gBACf,QAAQ,uCAAW;gBACnB,oBAAoB;gBACpB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,GAAG,QAAQ,OAAO;YACtB;YAEJ,IAAI,KAAK,QAAQ,CAAC,oBAAoB;gBAClC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBAEZ,QAAQ,GAAG,CAAC,aAAa;gBACzB,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;YAEhB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;gBACtC;gBACA,KAAK;gBACL,GAAG,MAAM;gBACT,SAAS;YACb;YACA,8EAA8E;YAC9E,OAAO;gBACH,SAAS;gBACT,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,QAAQ,SAAS,MAAM;gBACvB,SAAS,SAAS,OAAO;gBACzB,OAAO;YACX;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,qBAAqB,OAAO,QAAQ;YAElD,IAAI,SAAS,QAAQ;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,OAAO;gBACH,SAAS;gBACT,MAAM;gBACN,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,YAAY,MAAM,QAAQ,EAAE,cAAc;gBAC1C,SAAS,MAAM,QAAQ,EAAE,WAAW,CAAC;gBACrC,QAAQ,MAAM,MAAM,IAAI,CAAC;gBACzB,SAAS,MAAM,OAAO,IAAI,CAAC;gBAC3B,OAAO,OAAO,UAAU,QAAQ,MAAM,OAAO;YACjD;QACJ;IACJ;IAEA,MAAa,IAAO,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC5G,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,QAAQ;IAC7C;IAEA,MAAa,KAAQ,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACzH,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC3D;IAEA,MAAa,MAAS,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC1H,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC5D;IAEA,MAAa,IAAO,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACxH,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC1D;IAEA,MAAa,OAAU,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC/G,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM,QAAQ;IAChD;IAEA,MAAc,YAAY,KAAU,EAAE;QAClC,QAAQ,KAAK,CAAC,cAAc;YACxB,SAAS,MAAM,OAAO;YACtB,QAAQ,MAAM,QAAQ,EAAE;YACxB,MAAM,MAAM,QAAQ,EAAE;QAC1B;IACJ;AACJ;AAEO,MAAM,aAAa,IAAI;AAEvB,MAAM,mBAAmB,IAAI,WAAW;IAAE,QAAQ;AAAa", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/api/vehicles.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\r\nimport { cache } from 'react';\r\n\r\nexport interface VehicleFormData {\r\n  make: string;\r\n  model: string;\r\n  year: number;\r\n  color: string;\r\n  vin: string;\r\n  plate: string;\r\n  state_code: string;\r\n  country_code: string;\r\n  price: number;\r\n  description: string;\r\n\r\n  // Nuevos campos estructurados\r\n  engineSize: number;\r\n  transmission: string;\r\n  trim: string;\r\n  bodyType: string;\r\n\r\n  // Campos existentes\r\n  features: any;\r\n  amenities: string[];\r\n  // images?: string[];\r\n  status?: string;\r\n  images?: File[];\r\n  // vinDocument?: File;\r\n  // plateDocument?: File;\r\n  // registrationDocument?: File;\r\n  // insurancePolicyDocument?: File;\r\n  // titleDocument?: File;\r\n  vinDocument: File[];\r\n  plateDocument: File[];\r\n  registrationDocument: File[];\r\n  insurancePolicyDocument: File[];\r\n}\r\n\r\nexport interface Host {\r\n  id: string;\r\n  name: string;\r\n  image?: string;\r\n  email: string;\r\n  phone: string;\r\n  status: string;\r\n  isVerified: boolean;\r\n  isBlocked: boolean;\r\n\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface Vehicle {\r\n  id: string;\r\n  make: string;\r\n  model: string;\r\n  year: number;\r\n  color: string;\r\n  vin?: string;\r\n  // licensePlate?: string;\r\n  plate?: string;\r\n  state_code?: string;\r\n  country_code?: string;\r\n  price: number;\r\n  rating: number;\r\n  reviews: number;\r\n\r\n  // Nuevos campos estructurados\r\n  engineSize: number;\r\n  transmission: string;\r\n  trim: string;\r\n  bodyType: string;\r\n\r\n  approvalHistory: {\r\n    action: string;\r\n    date: string;\r\n    reason?: string;\r\n    userId?: string;\r\n    user?: {\r\n      id: string;\r\n      name: string;\r\n      email: string;\r\n    };\r\n  }[];\r\n\r\n  // Campos existentes\r\n  images: string[];\r\n  features: {\r\n    fuelType: string;\r\n    seats: number;\r\n    mileage: number;\r\n    registrationNumber: string;\r\n    insurancePolicy: string;\r\n    rules: string;\r\n    location: string;\r\n    weeklyRate: number;\r\n    monthlyRate: number;\r\n  };\r\n  description: string;\r\n  amenities: string[];\r\n  host: Host;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface VehicleResponse {\r\n  data: Vehicle[];\r\n  pagination: Pagination;\r\n}\r\n\r\nexport interface TimeSlot {\r\n  startTime: string;\r\n  endTime: string;\r\n}\r\n\r\nexport interface AvailabilitySettings {\r\n  defaultCheckInTime: string;\r\n  defaultCheckOutTime: string;\r\n  minimumRentalNights: number;\r\n  maximumRentalNights: number;\r\n  // bufferTimeBetweenRentals: number; // horas\r\n  advanceBookingPeriod: number; // días\r\n  instantBooking: boolean;\r\n  allowSameDayBooking: boolean;\r\n  cancellationPolicy: \"flexible\" | \"moderate\" | \"strict\";\r\n\r\n  mondayAvailable: boolean;\r\n  tuesdayAvailable: boolean;\r\n  wednesdayAvailable: boolean;\r\n  thursdayAvailable: boolean;\r\n  fridayAvailable: boolean;\r\n  saturdayAvailable: boolean;\r\n  sundayAvailable: boolean;\r\n  blockedDates: any[];\r\n}\r\n\r\nexport interface VehicleDocuments {\r\n  vinDocument: string;\r\n  plateDocument: string;\r\n  registrationDocument: string;\r\n  insurancePolicyDocument: string;\r\n}\r\n\r\n// Agregar esta interfaz para las estadísticas\r\nexport interface VehicleStats {\r\n  stats: {\r\n    total: number;\r\n    active: number;\r\n    rented: number;\r\n    maintenance: number;\r\n    pending: number;\r\n    totalReservations?: number;\r\n    averageRating?: number;\r\n    averageIncome?: number;\r\n  }\r\n}\r\n\r\nexport const vehiclesApi = {\r\n  // Obtener todos los vehículos (público)\r\n  getAll: async (params: { page: number; limit: number }) => {\r\n    const result = await apiService.get<VehicleResponse>('/vehicles', { params });\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener un vehículo por ID (público)\r\n  getById: async (id: string) => {\r\n    const result = await apiService.get<Vehicle>(`/vehicles/${id}`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener todos los vehículos (admin)\r\n  getAllForAdmin: async () => {\r\n    const result = await apiService.get<VehicleResponse>('/admin/vehicles');\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener vehículos del host actual\r\n  getMyVehicles: async (): Promise<VehicleResponse[]> => {\r\n    const result = await apiService.get<VehicleResponse[]>('/host/vehicles');\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Añadir método para obtener un vehículo específico del host\r\n  getMyVehicleById: async (id: string): Promise<VehicleResponse> => {\r\n    const result = await apiService.get<VehicleResponse>(`/host/vehicles/${id}`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  getAvailabilitySettings: async (vehicleId: string): Promise<AvailabilitySettings> => {\r\n    const result = await apiService.get<AvailabilitySettings>(`/vehicles/${vehicleId}/availability`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n\r\n\r\n  host: {\r\n    getAll: async (params: { page?: number; limit?: number }) => {\r\n      const result = await apiService.get<VehicleResponse>('/host/vehicles', { params });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    getById: async (id: string) => {\r\n      const result = await apiService.get<Vehicle>(`/host/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    create: async (data: VehicleFormData) => {\r\n      const result = await apiService.post<Vehicle>('/host/vehicles', data);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        // console.error('Error creating vehicle:', result.error);\r\n        throw new Error(result.error.message);\r\n      }\r\n    },\r\n\r\n    uploadFiles: async (vehicleId: string, data: Partial<VehicleFormData>) => {\r\n      const formData = new FormData();\r\n\r\n      // Agregar archivos al formData para los campos images, vinDocument, plateDocument, registrationDocument, insurancePolicyDocument\r\n      if (data.images) {\r\n        for (const file of data.images) {\r\n          formData.append('images', file);\r\n        }\r\n      }\r\n      if (data.vinDocument) {\r\n        for (const file of data.vinDocument) {\r\n          formData.append('vinDocument', file);\r\n        }\r\n      }\r\n      if (data.plateDocument) {\r\n        for (const file of data.plateDocument) {\r\n          formData.append('plateDocument', file);\r\n        }\r\n      }\r\n      if (data.registrationDocument) {\r\n        for (const file of data.registrationDocument) {\r\n          formData.append('registrationDocument', file);\r\n        }\r\n      }\r\n      if (data.insurancePolicyDocument) {\r\n        for (const file of data.insurancePolicyDocument) {\r\n          formData.append('insurancePolicyDocument', file);\r\n        }\r\n      }\r\n\r\n      const result = await apiService.patch<Vehicle>(`/host/vehicles/${vehicleId}/upload-files`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Añadir método para actualizar archivos\r\n    updateFiles: async (vehicleId: string, data: Partial<VehicleFormData>, imagesToRemove: string[] = []) => {\r\n\r\n      const formData = new FormData();\r\n\r\n      // Agregar archivos al formData solo si existen y tienen elementos\r\n      if (data.images && data.images.length > 0) {\r\n        for (const file of data.images) {\r\n          formData.append('images', file);\r\n        }\r\n      }\r\n\r\n      if (data.vinDocument && data.vinDocument.length > 0) {\r\n        for (const file of data.vinDocument) {\r\n          formData.append('vinDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.plateDocument && data.plateDocument.length > 0) {\r\n        for (const file of data.plateDocument) {\r\n          formData.append('plateDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.registrationDocument && data.registrationDocument.length > 0) {\r\n        for (const file of data.registrationDocument) {\r\n          formData.append('registrationDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.insurancePolicyDocument && data.insurancePolicyDocument.length > 0) {\r\n        for (const file of data.insurancePolicyDocument) {\r\n          formData.append('insurancePolicyDocument', file);\r\n        }\r\n      }\r\n\r\n      // Agregar imágenes a eliminar\r\n      if (imagesToRemove.length > 0) {\r\n        formData.append('imagesToRemove', JSON.stringify(imagesToRemove));\r\n      }\r\n\r\n      const result = await apiService.patch<Vehicle>(`/host/vehicles/${vehicleId}/update-files`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      console.log('Result:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error?.message || 'Ha ocurrido un error actualizando los archivos del vehículo.');\r\n      }\r\n    },\r\n\r\n    update: async (id: string, data: Partial<VehicleFormData>) => {\r\n      const result = await apiService.put<Vehicle>(`/host/vehicles/${id}`, data);\r\n      console.log('Result of update vehicle:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        console.error('Error updating vehicle:', result.error);\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Eliminar un vehículo (host)\r\n    delete: async (id: string) => {\r\n      const result = await apiService.delete(`/host/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    blockDates: async (vehicleId: string, data: {\r\n      startDate: string;\r\n      endDate: string;\r\n      reason?: string;\r\n    }): Promise<any> => {\r\n\r\n      if (!data.startDate || !data.endDate) {\r\n        throw new Error(\"Se requieren fechas válidas para bloquear\");\r\n      }\r\n\r\n      const result = await apiService.post('/host/reservations/block-dates', {\r\n        vehicleId,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        reason: data.reason || \"No disponible\"\r\n      });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Actualizar configuración de disponibilidad\r\n    updateAvailabilitySettings: async (vehicleId: string, data: Partial<AvailabilitySettings>): Promise<AvailabilitySettings> => {\r\n      const result = await apiService.put<AvailabilitySettings>(`/host/vehicles/${vehicleId}/availability`, data);\r\n      console.log('error: ', result.error);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n    }\r\n  },\r\n    getStats: async (): Promise<any> => {\r\n      const result = await apiService.get('/host/vehicles/stats');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Obtener documentos de un vehículo\r\n    getDocuments: async (vehicleId: string) => {\r\n      const result = await apiService.get<VehicleDocuments>(`/host/vehicles/${vehicleId}/documents`);\r\n      console.log('Result of get documents:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    requestReview: async (vehicleId: string) => {\r\n      const result = await apiService.post(`/host/vehicles/${vehicleId}/request-review`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  // Actualizar estado de un vehículo (host)\r\n  updateStatus: async (id: string, status: string): Promise<VehicleResponse> => {\r\n    const result = await apiService.patch<VehicleResponse>(`/host/vehicles/${id}/status`, { status });\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Funciones específicas para administradores\r\n  admin: {\r\n\r\n    getById: async (id: string) => {\r\n      const result = await apiService.get<Vehicle>(`/admin/vehicles/${id}`);\r\n      if (result.success) {\r\n        console.log('vehicle from api', result.data);\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Obtener vehículos pendientes de aprobación\r\n    getPendingVehicles: async () => {\r\n      const result = await apiService.get<Vehicle[]>('/admin/vehicles/pending');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error.message);\r\n      }\r\n    },\r\n\r\n    // Aprobar un vehículo\r\n    approveVehicle: async (id: string) => {\r\n      const result = await apiService.post<Vehicle>(`/admin/vehicles/${id}/approve`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Rechazar un vehículo\r\n    rejectVehicle: async (id: string, reason: string): Promise<any> => {\r\n      const result = await apiService.post(`/admin/vehicles/${id}/reject`, { reason });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Actualizar un vehículo (admin)\r\n    update: async (id: string, data: Partial<VehicleFormData>) => {\r\n      const result = await apiService.put<Vehicle>(`/admin/vehicles/${id}`, data);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Eliminar un vehículo (admin)\r\n    delete: async (id: string): Promise<any> => {\r\n      const result = await apiService.delete(`/admin/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Actualizar estado de un vehículo (admin)\r\n    updateStatus: async (id: string, status: string) => {\r\n      const result = await apiService.patch<Vehicle>(`/admin/vehicles/${id}/status`, { status });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Obtener todos los vehículos para el administrador\r\n    getAll: async (params: { page: number; limit: number }) => {\r\n      const result = await apiService.get<VehicleResponse>('/admin/vehicles', { params });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Nuevo método para obtener estadísticas\r\n    getStats: async (): Promise<VehicleStats> => {\r\n      const result = await apiService.get<VehicleStats>('/admin/vehicles/stats');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n  },\r\n\r\n  // Obtener fechas no disponibles para un vehículo\r\n  getUnavailableDates: async (vehicleId: string): Promise<string[]> => {\r\n    const result = await apiService.get</* string[] */\r\n      { date: string, by: string, reason?: string }[]\r\n    >(`/reservations/unavailable-dates/${vehicleId}`);\r\n    if (result.success) {\r\n      // return { data: result.data };\r\n      // return { data: result.data.map((item) => item.date) };\r\n      return result.data.map((item) => item.date);\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n};\r\n\r\nexport const getVehicleById = cache(vehiclesApi.getById);\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AA6JO,MAAM,cAAc;IACzB,wCAAwC;IACxC,QAAQ,OAAO;QACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,aAAa;YAAE;QAAO;QAC3E,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,uCAAuC;IACvC,SAAS,OAAO;QACd,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI;QAC9D,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,sCAAsC;IACtC,gBAAgB;QACd,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB;QACrD,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,oCAAoC;IACpC,eAAe;QACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAoB;QACvD,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6DAA6D;IAC7D,kBAAkB,OAAO;QACvB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,CAAC,eAAe,EAAE,IAAI;QAC3E,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,yBAAyB,OAAO;QAC9B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAuB,CAAC,UAAU,EAAE,UAAU,aAAa,CAAC;QAC/F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAIA,MAAM;QACJ,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,kBAAkB;gBAAE;YAAO;YAChF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,SAAS,OAAO;YACd,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,eAAe,EAAE,IAAI;YACnE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAU,kBAAkB;YAChE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,0DAA0D;gBAC1D,MAAM,IAAI,MAAM,OAAO,KAAK,CAAC,OAAO;YACtC;QACF;QAEA,aAAa,OAAO,WAAmB;YACrC,MAAM,WAAW,IAAI;YAErB,iIAAiI;YACjI,IAAI,KAAK,MAAM,EAAE;gBACf,KAAK,MAAM,QAAQ,KAAK,MAAM,CAAE;oBAC9B,SAAS,MAAM,CAAC,UAAU;gBAC5B;YACF;YACA,IAAI,KAAK,WAAW,EAAE;gBACpB,KAAK,MAAM,QAAQ,KAAK,WAAW,CAAE;oBACnC,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YACA,IAAI,KAAK,aAAa,EAAE;gBACtB,KAAK,MAAM,QAAQ,KAAK,aAAa,CAAE;oBACrC,SAAS,MAAM,CAAC,iBAAiB;gBACnC;YACF;YACA,IAAI,KAAK,oBAAoB,EAAE;gBAC7B,KAAK,MAAM,QAAQ,KAAK,oBAAoB,CAAE;oBAC5C,SAAS,MAAM,CAAC,wBAAwB;gBAC1C;YACF;YACA,IAAI,KAAK,uBAAuB,EAAE;gBAChC,KAAK,MAAM,QAAQ,KAAK,uBAAuB,CAAE;oBAC/C,SAAS,MAAM,CAAC,2BAA2B;gBAC7C;YACF;YAEA,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE,UAAU;gBACnG,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,yCAAyC;QACzC,aAAa,OAAO,WAAmB,MAAgC,iBAA2B,EAAE;YAElG,MAAM,WAAW,IAAI;YAErB,kEAAkE;YAClE,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;gBACzC,KAAK,MAAM,QAAQ,KAAK,MAAM,CAAE;oBAC9B,SAAS,MAAM,CAAC,UAAU;gBAC5B;YACF;YAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,MAAM,QAAQ,KAAK,WAAW,CAAE;oBACnC,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,MAAM,GAAG,GAAG;gBACvD,KAAK,MAAM,QAAQ,KAAK,aAAa,CAAE;oBACrC,SAAS,MAAM,CAAC,iBAAiB;gBACnC;YACF;YAEA,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,CAAC,MAAM,GAAG,GAAG;gBACrE,KAAK,MAAM,QAAQ,KAAK,oBAAoB,CAAE;oBAC5C,SAAS,MAAM,CAAC,wBAAwB;gBAC1C;YACF;YAEA,IAAI,KAAK,uBAAuB,IAAI,KAAK,uBAAuB,CAAC,MAAM,GAAG,GAAG;gBAC3E,KAAK,MAAM,QAAQ,KAAK,uBAAuB,CAAE;oBAC/C,SAAS,MAAM,CAAC,2BAA2B;gBAC7C;YACF;YAEA,8BAA8B;YAC9B,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,SAAS,MAAM,CAAC,kBAAkB,KAAK,SAAS,CAAC;YACnD;YAEA,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE,UAAU;gBACnG,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,WAAW;YACvB,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;YAC3C;QACF;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,eAAe,EAAE,IAAI,EAAE;YACrE,QAAQ,GAAG,CAAC,6BAA6B;YACzC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,OAAO,KAAK;gBACrD,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,8BAA8B;QAC9B,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;YAC7D,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,YAAY,OAAO,WAAmB;YAMpC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,OAAO,EAAE;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,kCAAkC;gBACrE;gBACA,WAAW,KAAK,SAAS;gBACzB,SAAS,KAAK,OAAO;gBACrB,QAAQ,KAAK,MAAM,IAAI;YACzB;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,6CAA6C;QAC7C,4BAA4B,OAAO,WAAmB;YACpD,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAuB,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE;YACtG,QAAQ,GAAG,CAAC,WAAW,OAAO,KAAK;YACnC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAChC;QACF;QACE,UAAU;YACR,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAC;YACpC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,oCAAoC;QACpC,cAAc,OAAO;YACnB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAmB,CAAC,eAAe,EAAE,UAAU,UAAU,CAAC;YAC7F,QAAQ,GAAG,CAAC,4BAA4B;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,eAAe,OAAO;YACpB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,UAAU,eAAe,CAAC;YACjF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;IACF;IAIA,0CAA0C;IAC1C,cAAc,OAAO,IAAY;QAC/B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAkB,CAAC,eAAe,EAAE,GAAG,OAAO,CAAC,EAAE;YAAE;QAAO;QAC/F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6CAA6C;IAC7C,OAAO;QAEL,SAAS,OAAO;YACd,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,gBAAgB,EAAE,IAAI;YACpE,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,GAAG,CAAC,oBAAoB,OAAO,IAAI;gBAC3C,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,6CAA6C;QAC7C,oBAAoB;YAClB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAY;YAC/C,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,CAAC,OAAO;YACtC;QACF;QAEA,sBAAsB;QACtB,gBAAgB,OAAO;YACrB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAU,CAAC,gBAAgB,EAAE,GAAG,QAAQ,CAAC;YAC7E,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,uBAAuB;QACvB,eAAe,OAAO,IAAY;YAChC,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE;YAAO;YAC9E,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,iCAAiC;QACjC,QAAQ,OAAO,IAAY;YACzB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,gBAAgB,EAAE,IAAI,EAAE;YACtE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,+BAA+B;QAC/B,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,IAAI;YAC9D,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,2CAA2C;QAC3C,cAAc,OAAO,IAAY;YAC/B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE;YAAO;YACxF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,oDAAoD;QACpD,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,mBAAmB;gBAAE;YAAO;YACjF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,yCAAyC;QACzC,UAAU;YACR,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAe;YAClD,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;IACF;IAEA,iDAAiD;IACjD,qBAAqB,OAAO;QAC1B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAEjC,CAAC,gCAAgC,EAAE,WAAW;QAChD,IAAI,OAAO,OAAO,EAAE;YAClB,gCAAgC;YAChC,yDAAyD;YACzD,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;QAC5C,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;AACF;AAEO,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD,EAAE,YAAY,OAAO", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\nimport { DateTime } from \"luxon\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n/**\r\n * Normaliza una fecha ISO a un objeto Date de JavaScript establecido a medianoche en la zona horaria local\r\n * @param dateStr Fecha en formato ISO string\r\n * @returns Objeto Date normalizado\r\n */\r\nexport function normalizeDate(dateStr: string): Date {\r\n  return DateTime.fromISO(dateStr)\r\n    .setZone('local')\r\n    .startOf('day')\r\n    .toJSDate();\r\n}\r\n\r\n/**\r\n * Convierte un array de fechas ISO a objetos Date normalizados\r\n * @param dateStrings Array de fechas en formato ISO string\r\n * @returns Array de objetos Date normalizados\r\n */\r\nexport function normalizeDates(dateStrings: string[]): Date[] {\r\n  if (!dateStrings || !Array.isArray(dateStrings)) return [];\r\n  return dateStrings.map(normalizeDate);\r\n}\r\n\r\n/**\r\n * Convierte un ISO a un string con formato \"dd de MMM de yyyy\"\r\n * @param date Fecha en formato ISO string\r\n * @returns String formateado\r\n */\r\nexport function formatISODate(date: string): string {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMM \\'de\\' yyyy', { locale: 'es' });\r\n}\r\n\r\n/**\r\n * Verifica si una fecha está en un array de fechas deshabilitadas\r\n * @param date Fecha a verificar\r\n * @param disabledDates Array de fechas deshabilitadas\r\n * @returns true si la fecha está deshabilitada, false en caso contrario\r\n */\r\nexport function isDateDisabled(date: Date, disabledDates: Date[]): boolean {\r\n  const luxonDate = DateTime.fromJSDate(date)\r\n    .setZone('local')\r\n    .startOf('day');\r\n\r\n  return disabledDates.some(disabledDate => {\r\n    const luxonDisabledDate = DateTime.fromJSDate(disabledDate);\r\n    return luxonDate.hasSame(luxonDisabledDate, 'day');\r\n  });\r\n}\r\n\r\n/**\r\n * Formatea una fecha a un string en formato: \"dd de MMM de yyyy\"\r\n * @param date Fecha a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatDate(date: string) {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMMM, yyyy', { locale: 'es' });\r\n}\r\n\r\n/** \r\n * Formatea un número a un string en formato de moneda local, por default MXN\r\n * @param value Número a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatCurrency(value: number, currency: string = 'MXN') {\r\n  return new Intl.NumberFormat('es-MX', { style: 'currency', currency }).format(value);\r\n\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAOzE,SAAS,cAAc,OAAe;IAC3C,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SACrB,OAAO,CAAC,SACR,OAAO,CAAC,OACR,QAAQ;AACb;AAOO,SAAS,eAAe,WAAqB;IAClD,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO,CAAC,cAAc,OAAO,EAAE;IAC1D,OAAO,YAAY,GAAG,CAAC;AACzB;AAOO,SAAS,cAAc,IAAY;IACxC,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,4BAA4B;QAAE,QAAQ;IAAK;AACzD;AAQO,SAAS,eAAe,IAAU,EAAE,aAAqB;IAC9D,MAAM,YAAY,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,MACnC,OAAO,CAAC,SACR,OAAO,CAAC;IAEX,OAAO,cAAc,IAAI,CAAC,CAAA;QACxB,MAAM,oBAAoB,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC9C,OAAO,UAAU,OAAO,CAAC,mBAAmB;IAC9C;AACF;AAOO,SAAS,WAAW,IAAY;IACrC,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,uBAAuB;QAAE,QAAQ;IAAK;AACpD;AAOO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY;IAAS,GAAG,MAAM,CAAC;AAEhF", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: ButtonProps) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACS;IACZ,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/vehicles/date-range-modal.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useEffect, useState, useMemo } from \"react\"\r\nimport { DateRange, type RangeKeyDict } from \"react-date-range\"\r\nimport { /* addDays, */ differenceInDays, format } from \"date-fns\"\r\nimport { es } from \"date-fns/locale\"\r\nimport { <PERSON><PERSON>, DialogContent, DialogTrigger, <PERSON><PERSON>Title, DialogFooter } from \"@/components/ui/dialog\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { CalendarIcon, Info } from \"lucide-react\"\r\nimport \"react-date-range/dist/styles.css\"\r\nimport \"react-date-range/dist/theme/default.css\"\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\nimport toast from \"react-hot-toast\"\r\nimport { normalizeDates, isDateDisabled } from \"@/lib/utils\"\r\n\r\ninterface DateRangeModalProps {\r\n  unavailableDates?: string[] // ISO date strings\r\n  onChange: (range: { startDate: Date; endDate: Date }) => void\r\n  initialDateRange?: { startDate: Date; endDate: Date }\r\n  minimumRentalNights?: number\r\n  maximumRentalNights?: number\r\n}\r\n\r\nexport default function DateRangeModal({\r\n  unavailableDates = [],\r\n  onChange,\r\n  initialDateRange,\r\n  // minimumRentalNights = 1,\r\n  // maximumRentalDays = 30\r\n  minimumRentalNights = 1,\r\n  maximumRentalNights = 30\r\n}: DateRangeModalProps) {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const unavailableDates2 = useMemo(() => {\r\n    if (!unavailableDates) return [];\r\n    return normalizeDates(unavailableDates);\r\n  }, [unavailableDates]);\r\n  // Validar initialDateRange antes de usarlo\r\n  const validInitialDateRange = initialDateRange &&\r\n    initialDateRange.startDate instanceof Date &&\r\n    initialDateRange.endDate instanceof Date &&\r\n    !isNaN(initialDateRange.startDate.getTime()) &&\r\n    !isNaN(initialDateRange.endDate.getTime())\r\n    ? initialDateRange\r\n    : null;\r\n\r\n  // Función para encontrar el próximo rango disponible\r\n  const findNextAvailableRange = () => {\r\n    // Comenzar desde mañana\r\n    const tomorrow = new Date()\r\n    tomorrow.setDate(tomorrow.getDate() + 1)\r\n    tomorrow.setHours(0, 0, 0, 0)\r\n\r\n    let startDate = new Date(tomorrow)\r\n\r\n    // Buscar el próximo día disponible\r\n    while (isDateDisabledHandler(startDate)) {\r\n      startDate.setDate(startDate.getDate() + 1)\r\n    }\r\n\r\n    // Para representar N días, necesitamos N+1 días\r\n    // Por ejemplo, para 3 días: check-in, día 1, día 2, día 3, check-out\r\n    let endDate = new Date(startDate)\r\n    let consecutiveDays = 0\r\n\r\n    // Necesitamos minimumRentalNights + 1 días para representar minimumRentalNights días\r\n    while (consecutiveDays < minimumRentalNights) {\r\n      endDate.setDate(endDate.getDate() + 1)\r\n\r\n      if (!isDateDisabledHandler(endDate)) {\r\n        consecutiveDays++\r\n      } else {\r\n        // Si encontramos un día no disponible, reiniciamos la búsqueda\r\n        startDate = new Date(endDate)\r\n        startDate.setDate(startDate.getDate() + 1)\r\n        endDate = new Date(startDate)\r\n        consecutiveDays = 0\r\n      }\r\n    }\r\n\r\n    return { startDate, endDate }\r\n  }\r\n\r\n  // Convert ISO strings to Date objects\r\n  // const disabledDates = useMemo(() => {\r\n  //   return normalizeDates(unavailableDates);\r\n  // }, [unavailableDates]);\r\n\r\n\r\n  // Función para verificar si una fecha está deshabilitada\r\n  const isDateDisabledHandler = (date: Date) => {\r\n    return isDateDisabled(date, unavailableDates2);\r\n  };\r\n\r\n  // Usar el rango de fechas inicial validado o encontrar el próximo disponible\r\n  const [state, setState] = useState<RangeKeyDict['selection'][]>(() => {\r\n    if (validInitialDateRange) {\r\n      // Verificar que el rango inicial no incluye el día actual\r\n      const tomorrow = new Date()\r\n      tomorrow.setDate(tomorrow.getDate() + 1)\r\n      tomorrow.setHours(0, 0, 0, 0)\r\n\r\n      if (validInitialDateRange.startDate.getTime() <= tomorrow.getTime() - 1) {\r\n        // Si incluye el día actual, buscar el próximo rango disponible\r\n        const { startDate, endDate } = findNextAvailableRange()\r\n        return [{\r\n          startDate,\r\n          endDate,\r\n          key: \"selection\",\r\n        }]\r\n      }\r\n\r\n      return [{\r\n        startDate: validInitialDateRange.startDate,\r\n        endDate: validInitialDateRange.endDate,\r\n        key: \"selection\",\r\n      }]\r\n    } else {\r\n      const { startDate, endDate } = findNextAvailableRange()\r\n      return [{\r\n        startDate,\r\n        endDate,\r\n        key: \"selection\",\r\n      }]\r\n    }\r\n  })\r\n\r\n  const handleSelect = (ranges: RangeKeyDict) => {\r\n    const selection = ranges.selection\r\n\r\n    // Solo validar cuando ambas fechas estén seleccionadas y sean diferentes\r\n    if (selection.startDate &&\r\n      selection.endDate &&\r\n      selection.startDate.getTime() !== selection.endDate.getTime()) {\r\n\r\n      // Para N días, necesitamos N+1 días\r\n      // Por ejemplo, check-in el día 1, check-out el día 4 = 3 días\r\n      const diffDays = differenceInDays(selection.endDate, selection.startDate)\r\n\r\n      if (diffDays < minimumRentalNights) {\r\n        toast.error(`La reserva debe ser de al menos ${minimumRentalNights} días`)\r\n        // Mantener la selección anterior\r\n        return\r\n      }\r\n\r\n      if (diffDays > maximumRentalNights) {\r\n        toast.error(`La reserva no puede exceder ${maximumRentalNights} días`)\r\n        // Mantener la selección anterior\r\n        return\r\n      }\r\n    }\r\n\r\n    setState([selection])\r\n  }\r\n\r\n  const handleApply = () => {\r\n    if (state[0].startDate && state[0].endDate) {\r\n      // Verificar nuevamente antes de aplicar\r\n      const diffDays = differenceInDays(state[0].endDate, state[0].startDate)\r\n\r\n      if (diffDays < minimumRentalNights) {\r\n        toast.error(`La reserva debe ser de al menos ${minimumRentalNights} días`)\r\n        return\r\n      }\r\n\r\n      if (diffDays > maximumRentalNights) {\r\n        toast.error(`La reserva no puede exceder ${maximumRentalNights} días`)\r\n        return\r\n      }\r\n\r\n      onChange({\r\n        startDate: state[0].startDate,\r\n        endDate: state[0].endDate,\r\n      })\r\n    }\r\n    setIsOpen(false)\r\n  }\r\n\r\n  // Responsive direction\r\n  const [direction, setDirection] = useState<\"horizontal\" | \"vertical\">(\"horizontal\")\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setDirection(window.innerWidth < 768 ? \"vertical\" : \"horizontal\")\r\n    }\r\n\r\n    handleResize()\r\n    window.addEventListener(\"resize\", handleResize)\r\n    return () => window.removeEventListener(\"resize\", handleResize)\r\n  }, [])\r\n\r\n  // Calcular días\r\n  const nights = state[0].startDate && state[0].endDate\r\n    ? differenceInDays(state[0].endDate, state[0].startDate)\r\n    : 0\r\n\r\n  // Formatear fechas para mostrar\r\n  const fechaRecogida = state[0].startDate ? format(state[0].startDate, \"dd 'de' MMMM, yyyy\", { locale: es }) : \"\"\r\n  const fechaDevolucion = state[0].endDate ? format(state[0].endDate, \"dd 'de' MMMM, yyyy\", { locale: es }) : \"\"\r\n\r\n  // Horarios por defecto\r\n  const horaRecogida = \"14:00\"\r\n  const horaDevolucion = \"12:00\"\r\n  const tolerancia = \"30 minutos\"\r\n\r\n  // Asegurar que el rango inicial cumpla con el mínimo de días\r\n  useEffect(() => {\r\n    if (validInitialDateRange) {\r\n      const diffDays = differenceInDays(validInitialDateRange.endDate, validInitialDateRange.startDate) + 1\r\n      if (diffDays < minimumRentalNights) {\r\n        // Si el rango inicial no cumple con el mínimo, ajustarlo\r\n        const { startDate, endDate } = findNextAvailableRange()\r\n        setState([{\r\n          startDate,\r\n          endDate,\r\n          key: \"selection\"\r\n        }])\r\n      }\r\n    }\r\n  }, [validInitialDateRange, minimumRentalNights, unavailableDates])\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\r\n      <DialogTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          className=\"w-full justify-start text-left font-normal h-auto py-2 px-3\"\r\n          onClick={() => setIsOpen(true)}\r\n        >\r\n          <CalendarIcon className=\"mr-2 h-4 w-4 text-gray-500\" />\r\n          <span>\r\n            {state[0].startDate && state[0].endDate\r\n              ? `${format(state[0].startDate, \"MMM dd, yyyy\")} - ${format(state[0].endDate, \"MMM dd, yyyy\")}`\r\n              : \"Selecciona las fechas\"}\r\n          </span>\r\n        </Button>\r\n      </DialogTrigger>\r\n\r\n      <DialogContent\r\n        className=\"sm:max-w-[725px] p-0 h-fit max-h-[90vh] overflow-y-auto\"\r\n      >\r\n        <DialogTitle asChild>\r\n          <div className=\"p-4 border-b\">\r\n            <div className=\"flex items-center\">\r\n              <h2 className=\"text-lg font-semibold\">Selecciona el rango de fechas</h2>\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <Button variant=\"ghost\" size=\"icon\" className=\"ml-2\">\r\n                      <Info className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent side=\"top\" align=\"center\" className=\"max-w-xs\">\r\n                    <p>\r\n                      {minimumRentalNights > 1\r\n                        ? `La reserva debe ser de al menos ${minimumRentalNights} días.`\r\n                        : \"Selecciona las fechas de tu reserva.\"}\r\n                    </p>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col gap-1 mt-2\">\r\n              <div className=\"flex justify-between text-sm text-gray-500\">\r\n                <span>Fecha de recogida</span>\r\n                <span>Fecha de devolución</span>\r\n              </div>\r\n\r\n              {state[0].startDate && state[0].endDate && (\r\n                <div className=\"text-xs text-gray-600 mt-1 bg-gray-100 rounded p-2\">\r\n                  <span>\r\n                    Puedes recoger el vehículo el <b>{fechaRecogida}</b> a las <b>{horaRecogida}</b>.<br />\r\n                    Debes devolverlo el <b>{fechaDevolucion}</b> a más tardar a las <b>{horaDevolucion}</b> (con tolerancia de {tolerancia}).\r\n                  </span>\r\n                  <br />\r\n                  <span>\r\n                    Aunque el rango seleccionado abarca {nights + 1} días, la reserva corresponde a <b>{nights} {nights === 1 ? 'día' : 'días'}</b> de uso efectivo.\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </DialogTitle>\r\n\r\n        <div\r\n          id=\"date-range-container\"\r\n          className=\"p-4 w-full overflow-x-auto flex justify-center\"\r\n          style={{ overflowY: \"visible\", whiteSpace: \"nowrap\" }}\r\n        >\r\n          <div className=\"inline-block\">\r\n            <DateRange\r\n              onChange={handleSelect}\r\n              moveRangeOnFirstSelection={false}\r\n              ranges={state}\r\n              months={2}\r\n              direction={direction}\r\n              locale={es}\r\n              disabledDay={isDateDisabledHandler}\r\n              minDate={new Date()}\r\n              rangeColors={['#1a2b5e']}\r\n              className=\"border rounded-md\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter className=\"p-4 bg-gray-50 border-t\">\r\n          <div className=\"flex justify-between items-center w-full\">\r\n            <div className=\"flex items-center\">\r\n              <span className=\"font-medium\">Duración de la reserva:</span>\r\n            </div>\r\n            <span className=\"font-bold\">{nights} {nights === 1 ? 'día' : 'días'}</span>\r\n          </div>\r\n          <div className=\"flex justify-end gap-2 mt-4 w-full\">\r\n            <Button variant=\"outline\" onClick={() => setIsOpen(false)}>\r\n              Cancelar\r\n            </Button>\r\n            <Button className=\"bg-[#1a2b5e] hover:bg-[#152348]\" onClick={handleApply}>\r\n              <span className=\"font-medium\">Aplicar</span>\r\n            </Button>\r\n          </div>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAGA;AAMA;AACA;;;AAlBA;;;;;;;;;;;;;AA4Be,SAAS,eAAe,EACrC,mBAAmB,EAAE,EACrB,QAAQ,EACR,gBAAgB,EAChB,2BAA2B;AAC3B,yBAAyB;AACzB,sBAAsB,CAAC,EACvB,sBAAsB,EAAE,EACJ;;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAChC,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAChC,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;oDAAG;QAAC;KAAiB;IACrB,2CAA2C;IAC3C,MAAM,wBAAwB,oBAC5B,iBAAiB,SAAS,YAAY,QACtC,iBAAiB,OAAO,YAAY,QACpC,CAAC,MAAM,iBAAiB,SAAS,CAAC,OAAO,OACzC,CAAC,MAAM,iBAAiB,OAAO,CAAC,OAAO,MACrC,mBACA;IAEJ,qDAAqD;IACrD,MAAM,yBAAyB;QAC7B,wBAAwB;QACxB,MAAM,WAAW,IAAI;QACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;QAE3B,IAAI,YAAY,IAAI,KAAK;QAEzB,mCAAmC;QACnC,MAAO,sBAAsB,WAAY;YACvC,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAC1C;QAEA,gDAAgD;QAChD,qEAAqE;QACrE,IAAI,UAAU,IAAI,KAAK;QACvB,IAAI,kBAAkB;QAEtB,qFAAqF;QACrF,MAAO,kBAAkB,oBAAqB;YAC5C,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;YAEpC,IAAI,CAAC,sBAAsB,UAAU;gBACnC;YACF,OAAO;gBACL,+DAA+D;gBAC/D,YAAY,IAAI,KAAK;gBACrB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;gBACxC,UAAU,IAAI,KAAK;gBACnB,kBAAkB;YACpB;QACF;QAEA,OAAO;YAAE;YAAW;QAAQ;IAC9B;IAEA,sCAAsC;IACtC,wCAAwC;IACxC,6CAA6C;IAC7C,0BAA0B;IAG1B,yDAAyD;IACzD,MAAM,wBAAwB,CAAC;QAC7B,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;IAC9B;IAEA,6EAA6E;IAC7E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;mCAA+B;YAC9D,IAAI,uBAAuB;gBACzB,0DAA0D;gBAC1D,MAAM,WAAW,IAAI;gBACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;gBACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;gBAE3B,IAAI,sBAAsB,SAAS,CAAC,OAAO,MAAM,SAAS,OAAO,KAAK,GAAG;oBACvE,+DAA+D;oBAC/D,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;oBAC/B,OAAO;wBAAC;4BACN;4BACA;4BACA,KAAK;wBACP;qBAAE;gBACJ;gBAEA,OAAO;oBAAC;wBACN,WAAW,sBAAsB,SAAS;wBAC1C,SAAS,sBAAsB,OAAO;wBACtC,KAAK;oBACP;iBAAE;YACJ,OAAO;gBACL,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;gBAC/B,OAAO;oBAAC;wBACN;wBACA;wBACA,KAAK;oBACP;iBAAE;YACJ;QACF;;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY,OAAO,SAAS;QAElC,yEAAyE;QACzE,IAAI,UAAU,SAAS,IACrB,UAAU,OAAO,IACjB,UAAU,SAAS,CAAC,OAAO,OAAO,UAAU,OAAO,CAAC,OAAO,IAAI;YAE/D,oCAAoC;YACpC,8DAA8D;YAC9D,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,OAAO,EAAE,UAAU,SAAS;YAExE,IAAI,WAAW,qBAAqB;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,oBAAoB,KAAK,CAAC;gBACzE,iCAAiC;gBACjC;YACF;YAEA,IAAI,WAAW,qBAAqB;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,oBAAoB,KAAK,CAAC;gBACrE,iCAAiC;gBACjC;YACF;QACF;QAEA,SAAS;YAAC;SAAU;IACtB;IAEA,MAAM,cAAc;QAClB,IAAI,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE;YAC1C,wCAAwC;YACxC,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS;YAEtE,IAAI,WAAW,qBAAqB;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,oBAAoB,KAAK,CAAC;gBACzE;YACF;YAEA,IAAI,WAAW,qBAAqB;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,oBAAoB,KAAK,CAAC;gBACrE;YACF;YAEA,SAAS;gBACP,WAAW,KAAK,CAAC,EAAE,CAAC,SAAS;gBAC7B,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO;YAC3B;QACF;QACA,UAAU;IACZ;IAEA,uBAAuB;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;yDAAe;oBACnB,aAAa,OAAO,UAAU,GAAG,MAAM,aAAa;gBACtD;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;mCAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,GACjD,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,IACrD;IAEJ,gCAAgC;IAChC,MAAM,gBAAgB,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,sBAAsB;QAAE,QAAQ,8IAAA,CAAA,KAAE;IAAC,KAAK;IAC9G,MAAM,kBAAkB,KAAK,CAAC,EAAE,CAAC,OAAO,GAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,sBAAsB;QAAE,QAAQ,8IAAA,CAAA,KAAE;IAAC,KAAK;IAE5G,uBAAuB;IACvB,MAAM,eAAe;IACrB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IAEnB,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,uBAAuB;gBACzB,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,sBAAsB,OAAO,EAAE,sBAAsB,SAAS,IAAI;gBACpG,IAAI,WAAW,qBAAqB;oBAClC,yDAAyD;oBACzD,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;oBAC/B,SAAS;wBAAC;4BACR;4BACA;4BACA,KAAK;wBACP;qBAAE;gBACJ;YACF;QACF;mCAAG;QAAC;QAAuB;QAAqB;KAAiB;IAEjE,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;;0BAClC,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;oBACV,SAAS,IAAM,UAAU;;sCAEzB,6LAAC,iNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;sCACxB,6LAAC;sCACE,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,GACnC,GAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,gBAAgB,GAAG,EAAE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,GAC7F;;;;;;;;;;;;;;;;;0BAKV,6LAAC,qIAAA,CAAA,gBAAa;gBACZ,WAAU;;kCAEV,6LAAC,qIAAA,CAAA,cAAW;wBAAC,OAAO;kCAClB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC,sIAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;kEACN,6LAAC,sIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;4DAAO,WAAU;sEAC5C,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGpB,6LAAC,sIAAA,CAAA,iBAAc;wDAAC,MAAK;wDAAM,OAAM;wDAAS,WAAU;kEAClD,cAAA,6LAAC;sEACE,sBAAsB,IACnB,CAAC,gCAAgC,EAAE,oBAAoB,MAAM,CAAC,GAC9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;wCAGP,KAAK,CAAC,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,kBACrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAK;sEAC0B,6LAAC;sEAAG;;;;;;wDAAkB;sEAAO,6LAAC;sEAAG;;;;;;wDAAiB;sEAAC,6LAAC;;;;;wDAAK;sEACnE,6LAAC;sEAAG;;;;;;wDAAoB;sEAAoB,6LAAC;sEAAG;;;;;;wDAAmB;wDAAqB;wDAAW;;;;;;;8DAEzH,6LAAC;;;;;8DACD,6LAAC;;wDAAK;wDACiC,SAAS;wDAAE;sEAAgC,6LAAC;;gEAAG;gEAAO;gEAAE,WAAW,IAAI,QAAQ;;;;;;;wDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3I,6LAAC;wBACC,IAAG;wBACH,WAAU;wBACV,OAAO;4BAAE,WAAW;4BAAW,YAAY;wBAAS;kCAEpD,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0JAAA,CAAA,YAAS;gCACR,UAAU;gCACV,2BAA2B;gCAC3B,QAAQ;gCACR,QAAQ;gCACR,WAAW;gCACX,QAAQ,8IAAA,CAAA,KAAE;gCACV,aAAa;gCACb,SAAS,IAAI;gCACb,aAAa;oCAAC;iCAAU;gCACxB,WAAU;;;;;;;;;;;;;;;;kCAKhB,6LAAC,qIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;kDAEhC,6LAAC;wCAAK,WAAU;;4CAAa;4CAAO;4CAAE,WAAW,IAAI,QAAQ;;;;;;;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,UAAU;kDAAQ;;;;;;kDAG3D,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;wCAAkC,SAAS;kDAC3D,cAAA,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GA9SwB;KAAA", "debugId": null}}, {"offset": {"line": 1751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/vehicles/vehicle-gallery.tsx"], "sourcesContent": ["'use client'\n\nimport { DateTime } from 'luxon'\nimport Image from 'next/image'\nimport React from 'react'\n\ninterface VehicleGalleryProps {\n  vehicle: any\n  images: string[]\n}\n\nexport function VehicleGallery({ vehicle, images, /* currentImageIndex, setCurrentImageIndex */ }: VehicleGalleryProps) {\n\n  const [currentImageIndex, setCurrentImageIndex] = React.useState(0)\n  const [isHovered, setIsHovered] = React.useState(false)\n  const isRecent = (createdAt: string) => {\n    const createdDate = DateTime.fromISO(createdAt);\n    const sevenDaysAgo = DateTime.now().minus({ days: 7 });\n    return createdDate > sevenDaysAgo;\n  }\n\n  return (\n    <>\n      <div className=\"relative\" >\n\n        {isRecent(vehicle.createdAt) && (\n          <div className=\"absolute top-4 left-4 z-10\">\n            <span className=\"bg-blue-500 text-white text-xs px-2 py-1 rounded-md\">\n              Recién publicado\n            </span>\n          </div>\n        )}\n\n        {/* Imagen principal */}\n        <div className=\"relative aspect-[4/3] bg-gray-100 rounded-lg overflow-hidden\" onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>\n          <Image\n            src={images[currentImageIndex] || \"/placeholder.svg\"}\n            alt={`${vehicle.make} ${vehicle.model}`}\n            fill\n            className=\"object-cover\"\n          />\n\n          {/* Controles de navegación */}\n          <div\n            className=\"absolute bottom-4 right-4 bg-white/80 rounded-full px-3 py-1 text-sm\"\n            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}\n          >\n            {currentImageIndex + 1} / {images.length}\n          </div>\n\n          <button\n            className=\"absolute left-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2\"\n            onClick={() => {\n              setCurrentImageIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1))\n            }}\n            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M15 18L9 12L15 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            </svg>\n          </button>\n\n          <button\n            className=\"absolute right-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2\"\n            onClick={() => setCurrentImageIndex(prev => (prev < images.length - 1 ? prev + 1 : 0))}\n            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9 18L15 12L9 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Miniaturas */}\n        <div className=\"grid grid-cols-6 gap-2 mt-2\">\n          {images.slice(0, 6).map((image, index) => (\n            <div\n              key={index}\n              className={`aspect-[4/3] rounded-md overflow-hidden cursor-pointer border-2 ${index === currentImageIndex ? 'border-blue-500' : 'border-transparent'\n                }`}\n              onClick={() => setCurrentImageIndex(index)}\n            >\n              <Image\n                src={image || \"/placeholder.svg\"}\n                alt={`${vehicle.make} ${vehicle.model} - Vista ${index + 1}`}\n                width={100}\n                height={75}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ))}\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,EAAE,OAAO,EAAE,MAAM,EAAsE;;IAEpH,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,WAAW,CAAC;QAChB,MAAM,cAAc,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC;QACrC,MAAM,eAAe,kLAAA,CAAA,WAAQ,CAAC,GAAG,GAAG,KAAK,CAAC;YAAE,MAAM;QAAE;QACpD,OAAO,cAAc;IACvB;IAEA,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;gBAEZ,SAAS,QAAQ,SAAS,mBACzB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAsD;;;;;;;;;;;8BAO1E,6LAAC;oBAAI,WAAU;oBAA+D,cAAc,IAAM,aAAa;oBAAO,cAAc,IAAM,aAAa;;sCACrJ,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,MAAM,CAAC,kBAAkB,IAAI;4BAClC,KAAK,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;4BACvC,IAAI;4BACJ,WAAU;;;;;;sCAIZ,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,SAAS,YAAY,IAAI;gCAAG,YAAY;4BAAoB;;gCAEpE,oBAAoB;gCAAE;gCAAI,OAAO,MAAM;;;;;;;sCAG1C,6LAAC;4BACC,WAAU;4BACV,SAAS;gCACP,qBAAqB,CAAC,OAAU,OAAO,IAAI,OAAO,IAAI,OAAO,MAAM,GAAG;4BACxE;4BACA,OAAO;gCAAE,SAAS,YAAY,IAAI;gCAAG,YAAY;4BAAoB;sCAErE,cAAA,6LAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;gCAAO,OAAM;0CAChE,cAAA,6LAAC;oCAAK,GAAE;oCAAmB,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;;;;;;;;;;;sCAI1G,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,qBAAqB,CAAA,OAAS,OAAO,OAAO,MAAM,GAAG,IAAI,OAAO,IAAI;4BACnF,OAAO;gCAAE,SAAS,YAAY,IAAI;gCAAG,YAAY;4BAAoB;sCAErE,cAAA,6LAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;gCAAO,OAAM;0CAChE,cAAA,6LAAC;oCAAK,GAAE;oCAAkB,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;8BAM3G,6LAAC;oBAAI,WAAU;8BACZ,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC;4BAEC,WAAW,CAAC,gEAAgE,EAAE,UAAU,oBAAoB,oBAAoB,sBAC5H;4BACJ,SAAS,IAAM,qBAAqB;sCAEpC,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,SAAS;gCACd,KAAK,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,SAAS,EAAE,QAAQ,GAAG;gCAC5D,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;2BAVP;;;;;;;;;;;;;;;;;AAkBnB;GApFgB;KAAA", "debugId": null}}, {"offset": {"line": 1949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/context/user-context.tsx"], "sourcesContent": ["'use client';\r\nimport { Session } from 'better-auth/types';\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\n\r\n\r\ninterface UserContextType {\r\n  user: User;\r\n  setUser: React.Dispatch<React.SetStateAction<User>>;\r\n  session: Session;\r\n  setSession: React.Dispatch<React.SetStateAction<any>>;\r\n}\r\n\r\nexport const UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children, session: sessionData }: { children: React.ReactNode; session: { user: User; session: Session } }) {\r\n\r\n  // console.log('sessionData: ', sessionData.user);\r\n  const [session, setSession] = useState(sessionData?.session);\r\n  const [user, setUser] = useState(sessionData?.user);\r\n\r\n  useEffect(() => {\r\n    if (sessionData) {\r\n      setUser(sessionData.user);\r\n      setSession(sessionData.session);\r\n    }\r\n  }, [sessionData]);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser, session, setSession }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error('useUser must be used within a UserProvider');\r\n  }\r\n  return context;\r\n}"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AAYO,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,WAAW,EAA4E;;IAEvI,kDAAkD;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,aAAa;gBACf,QAAQ,YAAY,IAAI;gBACxB,WAAW,YAAY,OAAO;YAChC;QACF;iCAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;YAAS;QAAW;kBAC/D;;;;;;AAGP;GAlBgB;KAAA;AAqBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/api/favorites.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\nimport { Vehicle } from './vehicles.api';\n\nexport interface FavoriteResponse {\n  success: boolean;\n  message?: string;\n  data?: Vehicle[];\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nexport interface FavoriteStatusResponse {\n  success: boolean;\n  message?: string;\n  isFavorite: boolean;\n}\n\nexport interface MultipleFavoriteStatusResponse {\n  success: boolean;\n  message?: string;\n  data: Record<string, boolean>;\n}\n\nexport interface AddFavoriteResponse {\n  success: boolean;\n  message?: string;\n  data?: any;\n}\n\nexport const favoritesApi = {\n  // Obtener favoritos del usuario con paginación\n  getFavorites: async (params: { page: number; limit: number }) => {\n    const result = await apiService.get<FavoriteResponse>('/client/favorites', { params });\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Agregar vehículo a favoritos\n  addToFavorites: async (vehicleId: string) => {\n    const result = await apiService.post<AddFavoriteResponse>(`/client/favorites/${vehicleId}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Remover vehículo de favoritos\n  removeFromFavorites: async (vehicleId: string) => {\n    const result = await apiService.delete<{ success: boolean; message?: string }>(`/client/favorites/${vehicleId}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Verificar si un vehículo está en favoritos\n  isFavorite: async (vehicleId: string) => {\n    const result = await apiService.get<FavoriteStatusResponse>(`/client/favorites/${vehicleId}/status`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Obtener estados de favoritos para múltiples vehículos\n  getFavoritesStatus: async (vehicleIds: string[]) => {\n    const result = await apiService.post<MultipleFavoriteStatusResponse>('/client/favorites/status', {\n      vehicleIds\n    });\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Toggle favorito (agregar si no está, remover si está)\n  toggleFavorite: async (vehicleId: string) => {\n    try {\n      // Primero verificar el estado actual\n      const statusResult = await favoritesApi.isFavorite(vehicleId);\n      \n      if (statusResult.isFavorite) {\n        // Si está en favoritos, remover\n        return await favoritesApi.removeFromFavorites(vehicleId);\n      } else {\n        // Si no está en favoritos, agregar\n        return await favoritesApi.addToFavorites(vehicleId);\n      }\n    } catch (error: any) {\n      throw new Error(error.message || 'Error al cambiar estado de favorito');\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AAmCO,MAAM,eAAe;IAC1B,+CAA+C;IAC/C,cAAc,OAAO;QACnB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAmB,qBAAqB;YAAE;QAAO;QACpF,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,+BAA+B;IAC/B,gBAAgB,OAAO;QACrB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAsB,CAAC,kBAAkB,EAAE,WAAW;QAC1F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,gCAAgC;IAChC,qBAAqB,OAAO;QAC1B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,MAAM,CAAyC,CAAC,kBAAkB,EAAE,WAAW;QAC/G,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6CAA6C;IAC7C,YAAY,OAAO;QACjB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAyB,CAAC,kBAAkB,EAAE,UAAU,OAAO,CAAC;QACnG,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,wDAAwD;IACxD,oBAAoB,OAAO;QACzB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAiC,4BAA4B;YAC/F;QACF;QACA,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,wDAAwD;IACxD,gBAAgB,OAAO;QACrB,IAAI;YACF,qCAAqC;YACrC,MAAM,eAAe,MAAM,aAAa,UAAU,CAAC;YAEnD,IAAI,aAAa,UAAU,EAAE;gBAC3B,gCAAgC;gBAChC,OAAO,MAAM,aAAa,mBAAmB,CAAC;YAChD,OAAO;gBACL,mCAAmC;gBACnC,OAAO,MAAM,aAAa,cAAc,CAAC;YAC3C;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;IACF;AACF", "debugId": null}}, {"offset": {"line": 2093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/vehicles/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/vehicles/favorite-button.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useContext } from \"react\"\nimport { Heart } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { favoritesApi } from \"@/lib/api/favorites.api\"\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\"\nimport { UserContext } from \"@/context/user-context\"\nimport toast from \"react-hot-toast\"\nimport { cn } from \"@/lib/utils\"\n\n// Hook personalizado que no falla si no hay UserProvider\nfunction useSafeUser() {\n  const context = useContext(UserContext)\n  return context || { user: null, setUser: () => { }, session: null, setSession: () => { } }\n}\n\ninterface FavoriteButtonProps {\n  vehicleId: string\n  className?: string\n  size?: \"sm\" | \"default\" | \"lg\" | \"icon\"\n  variant?: \"default\" | \"secondary\" | \"ghost\" | \"outline\"\n  showToast?: boolean\n}\n\nexport default function FavoriteButton({\n  vehicleId,\n  className,\n  size = \"icon\",\n  variant = \"secondary\",\n  showToast = true\n}: FavoriteButtonProps) {\n  const { user } = useSafeUser()\n  const queryClient = useQueryClient()\n  const [isOptimistic, setIsOptimistic] = useState(false)\n  console.log('user: ', user)\n  // Solo mostrar el botón si el usuario está autenticado y es de tipo client\n  const shouldShow = Boolean(user && user.userType === 'client')\n  console.log('shouldShow: ', shouldShow)\n\n  // Query para verificar si el vehículo está en favoritos\n  const { data: favoriteStatus, isLoading } = useQuery({\n    queryKey: ['favorite-status', vehicleId],\n    queryFn: () => favoritesApi.isFavorite(vehicleId),\n    enabled: shouldShow,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n\n  // Mutation para toggle favorito\n  const toggleMutation = useMutation({\n    mutationFn: () => favoritesApi.toggleFavorite(vehicleId),\n    onMutate: async () => {\n      // Optimistic update\n      setIsOptimistic(true)\n      await queryClient.cancelQueries({ queryKey: ['favorite-status', vehicleId] })\n\n      const previousStatus = queryClient.getQueryData(['favorite-status', vehicleId])\n\n      // Actualizar optimísticamente\n      queryClient.setQueryData(['favorite-status', vehicleId], (old: any) => ({\n        ...old,\n        isFavorite: !old?.isFavorite\n      }))\n\n      return { previousStatus }\n    },\n    onError: (err, variables, context) => {\n      // Revertir en caso de error\n      if (context?.previousStatus) {\n        queryClient.setQueryData(['favorite-status', vehicleId], context.previousStatus)\n      }\n      if (showToast) {\n        toast.error('Error al actualizar favorito')\n      }\n      console.error('Error toggling favorite:', err)\n    },\n    onSuccess: (data) => {\n      // Invalidar queries relacionadas\n      queryClient.invalidateQueries({ queryKey: ['favorite-status', vehicleId] })\n      queryClient.invalidateQueries({ queryKey: ['favorites'] })\n\n      if (showToast && data?.success) {\n        toast.success(data.message || 'Favorito actualizado')\n      }\n    },\n    onSettled: () => {\n      setIsOptimistic(false)\n    }\n  })\n\n  const handleToggle = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n\n    if (!shouldShow) {\n      if (showToast) {\n        toast.error('Debes iniciar sesión como cliente para usar favoritos')\n      }\n      return\n    }\n\n    toggleMutation.mutate()\n  }\n\n  // No mostrar el botón si el usuario no está autenticado o no es client\n  if (!shouldShow) {\n    return null\n  }\n\n  const isFavorite = favoriteStatus?.isFavorite || false\n  const isProcessing = isLoading || toggleMutation.isPending || isOptimistic\n\n  return (\n    <Button\n      size={size}\n      variant={variant}\n      className={cn(\n        \"relative transition-all duration-200\",\n        isFavorite && \"bg-red-50 hover:bg-red-100 border-red-200\",\n        className\n      )}\n      onClick={handleToggle}\n      disabled={isProcessing}\n    >\n      <Heart\n        className={cn(\n          \"transition-all duration-200\",\n          size === \"sm\" ? \"h-3 w-3\" : size === \"lg\" ? \"h-6 w-6\" : \"h-4 w-4\",\n          isFavorite ? \"fill-red-500 text-red-500\" : \"text-gray-600\",\n          isProcessing && \"animate-pulse\"\n        )}\n      />\n      {isProcessing && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n    </Button>\n  )\n}\n\n// Hook personalizado para usar en otros componentes\nexport function useFavoriteStatus(vehicleId: string) {\n  const { user } = useSafeUser()\n  const shouldFetch = Boolean(user && user.userType === 'client')\n\n  return useQuery({\n    queryKey: ['favorite-status', vehicleId],\n    queryFn: () => favoritesApi.isFavorite(vehicleId),\n    enabled: shouldFetch,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n}\n\n// Hook para obtener múltiples estados de favoritos\nexport function useFavoritesStatus(vehicleIds: string[]) {\n  const { user } = useSafeUser()\n  const shouldFetch = Boolean(user && user.userType === 'client' && vehicleIds.length > 0)\n\n  return useQuery({\n    queryKey: ['favorites-status', vehicleIds],\n    queryFn: () => favoritesApi.getFavoritesStatus(vehicleIds),\n    enabled: shouldFetch,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n}\n"], "names": ["c", "_c", "useState", "useContext", "Heart", "<PERSON><PERSON>", "favoritesApi", "useQuery", "useMutation", "useQueryClient", "UserContext", "toast", "cn", "useSafeUser", "$", "$i", "Symbol", "for", "context", "t0", "user", "setUser", "_temp", "session", "setSession", "_temp2", "FavoriteButton", "vehicleId", "className", "size", "t1", "variant", "t2", "showToast", "t3", "undefined", "queryClient", "isOptimistic", "setIsOptimistic", "console", "log", "shouldShow", "Boolean", "userType", "t4", "t5", "isFavorite", "t6", "query<PERSON><PERSON>", "queryFn", "enabled", "staleTime", "data", "favoriteStatus", "isLoading", "t7", "toggleFavorite", "t8", "cancelQueries", "previousStatus", "getQueryData", "setQueryData", "_temp3", "t10", "t9", "err", "variables", "error", "invalidateQueries", "success", "message", "t11", "t12", "mutationFn", "onMutate", "onError", "onSuccess", "onSettled", "toggleMutation", "t13", "e", "preventDefault", "stopPropagation", "mutate", "handleToggle", "isProcessing", "isPending", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "old", "useFavoriteStatus", "shouldFetch", "useFavoritesStatus", "vehicleIds", "length", "getFavoritesStatus"], "mappings": ";;;;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC5C,SAASC,KAAK,QAAQ,cAAc;AACpC,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,uBAAuB;;;AAC7E,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,EAAE,QAAQ,aAAa;;;AAThC,YAAY;;;;;;;;;;AAWZ,yDAAA;AACA;;IAAA,MAAAE,CAAA,mLAAAb,IAAA,AAAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACE,MAAAI,OAAA,qKAAgBf,aAAAA,AAAA,wIAAAO,cAAsB,CAAC;IAAA,IAAAS,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAI,OAAA,EAAA;QAChCC,EAAA,GAAAD,OAAO,IAAA;YAAAE,IAAA,EAAA;YAAAC,OAAA,EAAAC,KAAA;YAAAC,OAAA,EAAA;YAAAC,UAAA,EAAAC;QAAA,CAA4E;QAAAX,CAAA,CAAA,EAAA,GAAAI,OAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,OAAnFK,EAAmF;AAAA;GAF5FN,YAAA;AAAA,SAAAY,OAAA,GAAA;AAAA,SAAAH,MAAA,GAAA;AAae,wBAAAH,EAAA;;IAAA,MAAAL,CAAA,mLAAAb,IAAA,AAAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAwB,MAAA,EAAAa,SAAA,EAAAC,SAAA,EAAAC,IAAA,EAAAC,EAAA,EAAAC,OAAA,EAAAC,EAAA,EAAAC,SAAA,EAAAC,EAAAA,EAAA,GAAAf,EAMjB;IAHpB,MAAAU,IAAA,GAAAC,EAAa,KAAAK,SAAA,GAAN,MAAM,GAAbL,EAAa;IACb,MAAAC,OAAA,GAAAC,EAAqB,KAAAG,SAAA,GAAX,WAAW,GAArBH,EAAqB;IACrB,MAAAC,SAAA,GAAAC,EAAgB,KAAAC,SAAA,GAAA,OAAhBD,EAAgB;IAEhB,MAAA,EAAAd,IAAAA,EAAA;IACA,MAAAgB,WAAA,kNAAoB3B;IACpB,MAAA,CAAA4B,YAAA,EAAAC,eAAA,CAAA,qKAAwCpC,WAAA,AAAAA,EAAA,KAAc,CAAC;IACvDqC,OAAA,CAAAC,GAAA,CAAY,QAAQ,EAAEpB,IAAI,CAAC;IAE3B,MAAAqB,UAAA,GAAmBC,OAAA,CAAQtB,IAAI,IAAIA,IAAI,CAAAuB,QAAA,KAAc,QAAQ,CAAC;IAC9DJ,OAAA,CAAAC,GAAA,CAAY,cAAc,EAAEC,UAAU,CAAC;IAAA,IAAAG,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAA/B,CAAA,CAAA,EAAA,KAAAa,SAAA,EAAA;QAI3BiB,EAAA,GAAA;YAAC,iBAAiB;YAAEjB,SAAS;SAAA;QAC9BkB,EAAA,GAAAA,CAAA,4IAAMvC,eAAA,CAAAwC,UAAA,CAAwBnB,SAAS,CAAC;QAAAb,CAAA,CAAA,EAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,EAAA,GAAA8B,EAAA;QAAA9B,CAAA,CAAA,EAAA,GAAA+B,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAA9B,CAAA,CAAA,EAAA;QAAA+B,EAAA,GAAA/B,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAiC,EAAA;IAAA,IAAAjC,CAAA,CAAA,EAAA,KAAA2B,UAAA,IAAA3B,CAAA,CAAA,EAAA,KAAA8B,EAAA,IAAA9B,CAAA,CAAA,EAAA,KAAA+B,EAAA,EAAA;QAFEE,EAAA,GAAA;YAAAC,QAAA,EACzCJ,EAA8B;YAAAK,OAAA,EAC/BJ,EAAwC;YAAAK,OAAA,EACxCT,UAAU;YAAAU,SAAA,EAAA;QAAA;QAEpBrC,CAAA,CAAA,EAAA,GAAA2B,UAAA;QAAA3B,CAAA,CAAA,EAAA,GAAA8B,EAAA;QAAA9B,CAAA,CAAA,EAAA,GAAA+B,EAAA;QAAA/B,CAAA,CAAA,EAAA,GAAAiC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAjC,CAAA,CAAA,EAAA;IAAA;IALD,MAAA,EAAAsC,IAAA,EAAAC,cAAA,EAAAC,SAAAA,EAAA,iMAA4C/C,EAASwC,EAKpD,CAAC;IAAA,IAAAQ,EAAA;IAAA,IAAAzC,CAAA,CAAA,EAAA,KAAAa,SAAA,EAAA;QAIY4B,EAAA,GAAAA,CAAA,4IAAMjD,eAAA,CAAAkD,cAAA,CAA4B7B,SAAS,CAAC;QAAAb,CAAA,CAAA,EAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,EAAA,GAAAyC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAzC,CAAA,CAAA,EAAA;IAAA;IAAA,IAAA2C,EAAA;IAAA,IAAA3C,CAAA,CAAA,GAAA,KAAAsB,WAAA,IAAAtB,CAAA,CAAA,GAAA,KAAAa,SAAA,EAAA;QAC9C8B,EAAA,GAAA,MAAAA,CAAA;YAERnB,eAAe,CAAA,IAAK,CAAC;YAAA,MACfF,WAAW,CAAAsB,aAAA,CAAA;gBAAAV,QAAA,EAAA;oBAA4B,iBAAiB;oBAAErB,SAAS;iBAAA;YAAA,CAAG,CAAC;YAE7E,MAAAgC,cAAA,GAAuBvB,WAAW,CAAAwB,YAAA,CAAA;gBAAe,iBAAiB;gBAAEjC,SAAS;aAAC,CAAC;YAG/ES,WAAW,CAAAyB,YAAA,CAAA;gBAAe,iBAAiB;gBAAElC,SAAS;aAAA,EAAAmC,MAGpD,CAAC;YAAA,OAAA;gBAAAH;YAAA;QAAA;QAGJ7C,CAAA,CAAA,GAAA,GAAAsB,WAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,GAAA,GAAA2C,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA3C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiD,GAAA;IAAA,IAAAC,EAAA;IAAA,IAAAlD,CAAA,CAAA,GAAA,KAAAsB,WAAA,IAAAtB,CAAA,CAAA,GAAA,KAAAmB,SAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAa,SAAA,EAAA;QACQqC,EAAA,GAAAA,CAAAC,GAAA,EAAAC,SAAA,EAAAhD,OAAA;YAAA,IAEHA,OAAO,EAAAyC,cAAA,EAAA;gBACTvB,WAAW,CAAAyB,YAAA,CAAA;oBAAe,iBAAiB;oBAAElC,SAAS;iBAAA,EAAGT,OAAO,CAAAyC,cAAe,CAAC;YAAA;YAAA,IAE9E1B,SAAS,EAAA;2KACXtB,UAAA,CAAAwD,KAAA,CAAY,8BAA8B,CAAC;YAAA;YAE7C5B,OAAA,CAAA4B,KAAA,CAAc,0BAA0B,EAAEF,GAAG,CAAC;QAAA;QAErCF,GAAA,IAAAX,IAAA;YAEThB,WAAW,CAAAgC,iBAAA,CAAA;gBAAApB,QAAA,EAAA;oBAAgC,iBAAiB;oBAAErB,SAAS;iBAAA;YAAA,CAAG,CAAC;YAC3ES,WAAW,CAAAgC,iBAAA,CAAA;gBAAApB,QAAA,EAAA;oBAAgC,WAAW;iBAAA;YAAA,CAAG,CAAC;YAAA,IAEtDf,SAAS,IAAImB,IAAI,EAAAiB,OAAS,EAAA;2KAC5B1D,UAAA,CAAA0D,OAAA,CAAcjB,IAAI,CAAAkB,OAAA,IAAY,sBAAsB,CAAC;YAAA;QAAA;QAExDxD,CAAA,CAAA,GAAA,GAAAsB,WAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAmB,SAAA;QAAAnB,CAAA,CAAA,GAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,GAAA,GAAAiD,GAAA;QAAAjD,CAAA,CAAA,GAAA,GAAAkD,EAAA;IAAA,OAAA;QAAAD,GAAA,GAAAjD,CAAA,CAAA,GAAA;QAAAkD,EAAA,GAAAlD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyD,GAAA;IAAA,IAAAzD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACUsD,GAAA,GAAAA,CAAA;YACTjC,eAAe,CAAA,KAAM,CAAC;QAAA;QACvBxB,CAAA,CAAA,GAAA,GAAAyD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA0D,GAAA;IAAA,IAAA1D,CAAA,CAAA,GAAA,KAAAiD,GAAA,IAAAjD,CAAA,CAAA,GAAA,KAAAyC,EAAA,IAAAzC,CAAA,CAAA,GAAA,KAAA2C,EAAA,IAAA3C,CAAA,CAAA,GAAA,KAAAkD,EAAA,EAAA;QAtCgCQ,GAAA,GAAA;YAAAC,UAAA,EACrBlB,EAA4C;YAAAmB,QAAA,EAC9CjB,EAcT;YAAAkB,OAAA,EACQX,EASR;YAAAY,SAAA,EACUb,GAQV;YAAAc,SAAA,EACUN;QAEV;QACFzD,CAAA,CAAA,GAAA,GAAAiD,GAAA;QAAAjD,CAAA,CAAA,GAAA,GAAAyC,EAAA;QAAAzC,CAAA,CAAA,GAAA,GAAA2C,EAAA;QAAA3C,CAAA,CAAA,GAAA,GAAAkD,EAAA;QAAAlD,CAAA,CAAA,GAAA,GAAA0D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA1D,CAAA,CAAA,GAAA;IAAA;IAvCD,MAAAgE,cAAA,uMAAuBtE,EAAYgE,GAuClC,CAAC;IAAA,IAAAO,GAAA;IAAA,IAAAjE,CAAA,CAAA,GAAA,KAAA2B,UAAA,IAAA3B,CAAA,CAAA,GAAA,KAAAmB,SAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAgE,cAAA,EAAA;QAEmBC,GAAA,IAAAC,CAAA;YACnBA,CAAC,CAAAC,cAAA,CAAgB,CAAC;YAClBD,CAAC,CAAAE,eAAA,CAAiB,CAAC;YAAA,IAAA,CAEdzC,UAAU,EAAA;gBAAA,IACTR,SAAS,EAAA;+KACXtB,UAAA,CAAAwD,KAAA,CAAY,0DAAuD,CAAC;gBAAA;gBAAA;YAAA;YAKxEW,cAAc,CAAAK,MAAA,CAAQ,CAAC;QAAA;QACxBrE,CAAA,CAAA,GAAA,GAAA2B,UAAA;QAAA3B,CAAA,CAAA,GAAA,GAAAmB,SAAA;QAAAnB,CAAA,CAAA,GAAA,GAAAgE,cAAA;QAAAhE,CAAA,CAAA,GAAA,GAAAiE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjE,CAAA,CAAA,GAAA;IAAA;IAZD,MAAAsE,YAAA,GAAqBL,GAYpB;IAAA,IAAA,CAGItC,UAAU,EAAA;QAAA,OAAA;IAAA;IAIf,MAAAK,UAAA,GAAmBO,cAAc,EAAAP,UAAA,IAAA,KAAqB;IACtD,MAAAuC,YAAA,GAAqB/B,SAAS,IAAIwB,cAAc,CAAAQ,SAAU,IAAIjD,YAAY;IAQpE,MAAAkD,GAAA,GAAAzC,UAAU,IAAI,2CAA2C;IAAA,IAAA0C,GAAA;IAAA,IAAA1E,CAAA,CAAA,GAAA,KAAAc,SAAA,IAAAd,CAAA,CAAA,GAAA,KAAAyE,GAAA,EAAA;QAFhDC,GAAA,8HAAA5E,KAAA,AAAAA,EACT,sCAAsC,EACtC2E,GAAyD,EACzD3D,SACF,CAAC;QAAAd,CAAA,CAAA,GAAA,GAAAc,SAAA;QAAAd,CAAA,CAAA,GAAA,GAAAyE,GAAA;QAAAzE,CAAA,CAAA,GAAA,GAAA0E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA1E,CAAA,CAAA,GAAA;IAAA;IAOG,MAAA2E,GAAA,GAAA5D,IAAI,KAAK,IAAI,GAAG,SAAS,GAAGA,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG,SAAS;IACjE,MAAA6D,GAAA,GAAA5C,UAAU,GAAG,2BAA2B,GAAG,eAAe;IAC1D,MAAA6C,GAAA,GAAAN,YAAY,IAAI,eAAe;IAAA,IAAAO,GAAA;IAAA,IAAA9E,CAAA,CAAA,GAAA,KAAA2E,GAAA,IAAA3E,CAAA,CAAA,GAAA,KAAA4E,GAAA,IAAA5E,CAAA,CAAA,GAAA,KAAA6E,GAAA,EAAA;QAJtBC,GAAA,8HAAAhF,KAAAA,AAAA,EACT,6BAA6B,EAC7B6E,GAAiE,EACjEC,GAA0D,EAC1DC,GACF,CAAC;QAAA7E,CAAA,CAAA,GAAA,GAAA2E,GAAA;QAAA3E,CAAA,CAAA,GAAA,GAAA4E,GAAA;QAAA5E,CAAA,CAAA,GAAA,GAAA6E,GAAA;QAAA7E,CAAA,CAAA,GAAA,GAAA8E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9E,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+E,GAAA;IAAA,IAAA/E,CAAA,CAAA,GAAA,KAAA8E,GAAA,EAAA;QANHC,GAAA,iBAAA,qYAAC,QAAK;YACO,SAKV,CALU,CAAAD,GAKV,IACD;;;;;;QAAA9E,CAAA,CAAA,GAAA,GAAA8E,GAAA;QAAA9E,CAAA,CAAA,GAAA,GAAA+E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/E,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgF,GAAA;IAAA,IAAAhF,CAAA,CAAA,GAAA,KAAAuE,YAAA,EAAA;QACDS,GAAA,GAAAT,YAAY,kBACX,6LAAA,GAEM;YAFS,SAAmD,EAAnD,mDAAmD;oCAChE,6LAAA,GAAiG;gBAAlF,SAA+E,EAA/E,+EAA+E,GAChG,EAFA,GAEM,CACP;;;;;;;;;;;QAAAvE,CAAA,CAAA,GAAA,GAAAuE,YAAA;QAAAvE,CAAA,CAAA,GAAA,GAAAgF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhF,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiF,GAAA;IAAA,IAAAjF,CAAA,CAAA,GAAA,KAAAsE,YAAA,IAAAtE,CAAA,CAAA,GAAA,KAAAuE,YAAA,IAAAvE,CAAA,CAAA,GAAA,KAAAe,IAAA,IAAAf,CAAA,CAAA,GAAA,KAAA0E,GAAA,IAAA1E,CAAA,CAAA,GAAA,KAAA+E,GAAA,IAAA/E,CAAA,CAAA,GAAA,KAAAgF,GAAA,IAAAhF,CAAA,CAAA,GAAA,KAAAiB,OAAA,EAAA;QAvBHgE,GAAA,iBAAA,mUAAC,SAAM;YACClE,IAAI,CAAJA,CAAAA,IAAI;YACDE,OAAO,CAAPA,CAAAA,OAAO;YACL,SAIV,CAJU,CAAAyD,GAIV;YACQJ,OAAY,CAAZA,CAAAA,YAAY;YACXC,QAAY,CAAZA,CAAAA,YAAY,EAEtB;;gBAAAQ,GAOE,CACD;gBAAAC,GAIA,CACH,EAxBC,MAAM,CAwBE;;;;;;;QAAAhF,CAAA,CAAA,GAAA,GAAAsE,YAAA;QAAAtE,CAAA,CAAA,GAAA,GAAAuE,YAAA;QAAAvE,CAAA,CAAA,GAAA,GAAAe,IAAA;QAAAf,CAAA,CAAA,GAAA,GAAA0E,GAAA;QAAA1E,CAAA,CAAA,GAAA,GAAA+E,GAAA;QAAA/E,CAAA,CAAA,GAAA,GAAAgF,GAAA;QAAAhF,CAAA,CAAA,GAAA,GAAAiB,OAAA;QAAAjB,CAAA,CAAA,GAAA,GAAAiF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjF,CAAA,CAAA,GAAA;IAAA;IAAA,OAxBTiF,GAwBS;AAAA;;;QAzGMlF,WAAA,CAAY,CAAC;kMACVJ,iBAAA,CAAe,CAAC;sLAQQF,YAAA;0LAQrBC,cAAA;;;KAxBVkB;AAoHf,oDAAA;AApHe,SAAAoC,OAAAkC,GAAA;IAAA,OAAA;QAAA,GAmCJA,GAAG;QAAAlD,UAAA,EAAA,CACOkD,GAAG,EAAAlD;IAAA;AAAA;AAiFjB,2BAAAnB,SAAA;;IAAA,MAAAb,CAAA,mLAAAb,IAAAA,AAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,EAAAM,IAAAA,EAAA;IACA,MAAA8E,WAAA,GAAoBxD,OAAA,CAAQtB,IAAI,IAAIA,IAAI,CAAAuB,QAAA,KAAc,QAAQ,CAAC;IAAA,IAAAxB,EAAA;IAAA,IAAAW,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAa,SAAA,EAAA;QAGnDR,EAAA,GAAA;YAAC,iBAAiB;YAAEQ,SAAS;SAAA;QAC9BG,EAAA,GAAAA,CAAA,4IAAMxB,eAAA,CAAAwC,UAAA,CAAwBnB,SAAS,CAAC;QAAAb,CAAA,CAAA,EAAA,GAAAa,SAAA;QAAAb,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAX,EAAA,GAAAL,CAAA,CAAA,EAAA;QAAAgB,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAkB,EAAA;IAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAoF,WAAA,IAAApF,CAAA,CAAA,EAAA,KAAAK,EAAA,IAAAL,CAAA,CAAA,EAAA,KAAAgB,EAAA,EAAA;QAFnCE,EAAA,GAAA;YAAAgB,QAAA,EACJ7B,EAA8B;YAAA8B,OAAA,EAC/BnB,EAAwC;YAAAoB,OAAA,EACxCgD,WAAW;YAAA/C,SAAA,EAAA;QAAA;QAErBrC,CAAA,CAAA,EAAA,GAAAoF,WAAA;QAAApF,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAgB,EAAA;QAAAhB,CAAA,CAAA,EAAA,GAAAkB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAlB,CAAA,CAAA,EAAA;IAAA;IAAA,qMALMP,EAASyB,EAKf,CAAC;AAAA;IATGiE;;QACYpF,WAAA,CAAY,CAAC;uLAGvBN,WAAA;;;AASF,4BAAA6F,UAAA;;IAAA,MAAAtF,CAAA,mLAAAb,IAAAA,AAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,EAAAM,IAAAA,EAAA;IACA,MAAA8E,WAAA,GAAoBxD,OAAA,CAAQtB,IAAI,IAAIA,IAAI,CAAAuB,QAAA,KAAc,QAAQ,IAAIyD,UAAU,CAAAC,MAAA,GAAA,CAAW,CAAC;IAAA,IAAAlF,EAAA;IAAA,IAAAW,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAsF,UAAA,EAAA;QAG5EjF,EAAA,GAAA;YAAC,kBAAkB;YAAEiF,UAAU;SAAA;QAChCtE,EAAA,GAAAA,CAAA,4IAAMxB,eAAA,CAAAgG,kBAAA,CAAgCF,UAAU,CAAC;QAAAtF,CAAA,CAAA,EAAA,GAAAsF,UAAA;QAAAtF,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAX,EAAA,GAAAL,CAAA,CAAA,EAAA;QAAAgB,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAkB,EAAA;IAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAoF,WAAA,IAAApF,CAAA,CAAA,EAAA,KAAAK,EAAA,IAAAL,CAAA,CAAA,EAAA,KAAAgB,EAAA,EAAA;QAF5CE,EAAA,GAAA;YAAAgB,QAAA,EACJ7B,EAAgC;YAAA8B,OAAA,EACjCnB,EAAiD;YAAAoB,OAAA,EACjDgD,WAAW;YAAA/C,SAAA,EAAA;QAAA;QAErBrC,CAAA,CAAA,EAAA,GAAAoF,WAAA;QAAApF,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAAL,CAAA,CAAA,EAAA,GAAAgB,EAAA;QAAAhB,CAAA,CAAA,EAAA,GAAAkB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAlB,CAAA,CAAA,EAAA;IAAA;IAAA,qMALMP,EAASyB,EAKf,CAAC;AAAA;IATGmE;;QACYtF,WAAA,CAAY,CAAC;uLAGvBN,WAAA", "debugId": null}}, {"offset": {"line": 2543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": ["React", "cn", "Input", "t0", "$", "_c", "$i", "Symbol", "for", "className", "props", "type", "t1", "t2"], "mappings": ";;;;;AAEA,SAASC,EAAE,QAAQ,aAAa;;;;AAEhC,eAAAE,EAAA;IAAA,MAAAC,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,IAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAD,EAAA,EAAA;QAAe,CAAA,EAAAM,SAAA,EAAAE,IAAA,EAAA,GAAAD,OAAA,GAAAP,EAA4D;QAAAC,CAAA,CAAA,EAAA,GAAAD,EAAA;QAAAC,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,IAAA;IAAA,OAAA;QAAAF,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;QAAAO,IAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QAK1DG,EAAA,GAAAX,gIAAAA,AAAA,EACT,icAAic,EACjc,+EAA+E,EAC/E,wGAAwG,EACxGQ,SACF,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAS,EAAA;IAAA,IAAAT,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAQ,EAAA,IAAAR,CAAA,CAAA,EAAA,KAAAO,IAAA,EAAA;QARHE,EAAA,iBAAA,6LAAA,KAUE;YATMF,IAAI,CAAJA,CAAAA,IAAI;YACA,SAAO,IAAP,OAAO;YACN,SAKV,CALU,CAAAC,EAKV;YAAA,GACGF,KAAK,IACT;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAQ,EAAA;QAAAR,CAAA,CAAA,EAAA,GAAAO,IAAA;QAAAP,CAAA,CAAA,GAAA,GAAAS,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAT,CAAA,CAAA,GAAA;IAAA;IAAA,OAVFS,EAUE;AAAA;KAZNX", "debugId": null}}, {"offset": {"line": 2616, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 2667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium  group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 !select-text cursor-text\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qOACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 2903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/auth-client.ts"], "sourcesContent": ["import { createAuthClient } from \"better-auth/react\";\r\nimport { adminClient, /* multiSessionClient, */ organizationClient } from \"better-auth/client/plugins\";\r\nimport { getCookie } from 'cookies-next/client';\r\n\r\nimport env from \"@/constants/env\";\r\nimport { BEARER_COOKIE_NAME } from './constants';\r\n\r\nexport const authClient = createAuthClient({\r\n  baseURL: env.NEXT_PUBLIC_API_URL,\r\n\r\n  plugins: [\r\n    adminClient(),\r\n    organizationClient(),\r\n    // multiSessionClient()\r\n  ],\r\n  credentials: 'include',\r\n  fetchOptions: {\r\n    onError: (ctx) => {\r\n      console.log('Error:', ctx.error);\r\n      console.log('Response:', ctx.response.url);\r\n    },\r\n    headers: {\r\n      'x-dashboard-call': 'true'\r\n    },\r\n    auth: {\r\n      type: 'Bearer',\r\n      token: () => {\r\n        const token = getCookie(BEARER_COOKIE_NAME);\r\n        if (token) {\r\n          return token; // No truncar el token\r\n        }\r\n      }\r\n    }\r\n  },\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAEA;AACA;;;;;;AAEO,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,SAAS,0HAAA,CAAA,UAAG,CAAC,mBAAmB;IAEhC,SAAS;QACP,CAAA,GAAA,wLAAA,CAAA,cAAW,AAAD;QACV,CAAA,GAAA,wLAAA,CAAA,qBAAkB,AAAD;KAElB;IACD,aAAa;IACb,cAAc;QACZ,SAAS,CAAC;YACR,QAAQ,GAAG,CAAC,UAAU,IAAI,KAAK;YAC/B,QAAQ,GAAG,CAAC,aAAa,IAAI,QAAQ,CAAC,GAAG;QAC3C;QACA,SAAS;YACP,oBAAoB;QACtB;QACA,MAAM;YACJ,MAAM;YACN,OAAO;gBACL,MAAM,QAAQ,CAAA,GAAA,4JAAA,CAAA,YAAS,AAAD,EAAE,4HAAA,CAAA,qBAAkB;gBAC1C,IAAI,OAAO;oBACT,OAAO,OAAO,sBAAsB;gBACtC;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2952, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/auth/login-form.tsx"], "sourcesContent": ["\"use client\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport { useState } from \"react\";\nimport { Loader2 } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useRouter } from 'next/navigation';\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport * as z from \"zod\";\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\nimport { toast } from \"react-hot-toast\";\nimport { authClient } from '@/auth-client';\n\nconst signInSchema = z.object({\n  email: z.string().email({ message: \"Correo electrónico inválido\" }),\n  password: z.string().min(1, { message: \"La contraseña es requerida\" }),\n  rememberMe: z.boolean().optional(),\n});\n\ninterface LoginFormProps {\n  onSuccess?: () => void\n  redirectAfterLogin?: boolean\n  onRegisterClick?: () => void\n  callbackUrl?: string\n}\n\ntype SignInFormValues = z.infer<typeof signInSchema>;\n\nexport function LoginForm({ onSuccess, onRegisterClick, redirectAfterLogin = true }: LoginFormProps) {\n  const [loading, setLoading] = useState(false);\n  const router = useRouter();\n\n  const form = useForm<SignInFormValues>({\n    resolver: zodResolver(signInSchema),\n    defaultValues: {\n      email: \"\",\n      password: \"\",\n      rememberMe: false,\n    },\n  });\n\n  const onSubmit = async (data: SignInFormValues) => {\n    try {\n      await authClient.signIn.email(\n        {\n          email: data.email,\n          password: data.password,\n          callbackURL: window.location.origin + \"/dashboard\"\n\n        },\n        {\n          onRequest: () => {\n            setLoading(true);\n          },\n          onResponse: () => {\n            setLoading(false);\n          },\n          onError: (ctx) => {\n            console.log('Error: ', ctx);\n            toast.error(ctx.error.message || \"Error al iniciar sesión\");\n          },\n          onSuccess: async () => {\n\n            // router.push(\"/dashboard\");\n            if (onSuccess) {\n              onSuccess()\n            }\n\n            if (redirectAfterLogin) {\n              toast.success(\"Inicio de sesión exitoso. Redirigiendo al dashboard...\")\n              // Redirigir a la página de reserva si hay una redirección pendiente\n              router.push('/dashboard')\n            }\n          },\n        },\n      );\n    } catch (error) {\n      setLoading(false);\n      toast.error(\"Error al iniciar sesión\");\n      console.log('error: ', error)\n    }\n  };\n\n  return (\n    <>\n      <Card className=\"z-50 rounded-md max-w-md w-full\">\n        <CardHeader>\n          <CardTitle className=\"text-lg md:text-xl\">Iniciar Sesión</CardTitle>\n          <CardDescription className=\"text-xs md:text-sm\">\n            Ingresa tus credenciales para acceder a DriveLink\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Form {...form}>\n            <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n              <FormField\n                control={form.control}\n                name=\"email\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Correo electrónico</FormLabel>\n                    <FormControl>\n                      <Input\n                        type=\"email\"\n                        placeholder=\"<EMAIL>\"\n                        {...field}\n                      />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"password\"\n                render={({ field }) => (\n                  <FormItem>\n                    <div className=\"flex items-center justify-between\">\n                      <FormLabel>Contraseña</FormLabel>\n                      <Link\n                        href=\"/forgot-password\"\n                        className=\"text-xs text-primary hover:underline\"\n                      >\n                        ¿Olvidaste tu contraseña?\n                      </Link>\n                    </div>\n                    <FormControl>\n                      <Input\n                        type=\"password\"\n                        placeholder=\"Contraseña\"\n                        {...field}\n                      />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"rememberMe\"\n                render={({ field }) => (\n                  <FormItem className=\"flex flex-row items-center space-x-2 space-y-0\">\n                    <FormControl>\n                      <Checkbox\n                        checked={field.value}\n                        onCheckedChange={field.onChange}\n                      />\n                    </FormControl>\n                    <FormLabel className=\"text-sm font-normal\">Recordarme</FormLabel>\n                  </FormItem>\n                )}\n              />\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <Loader2 size={16} className=\"animate-spin\" />\n                ) : (\n                  \"Iniciar Sesión\"\n                )}\n              </Button>\n            </form>\n          </Form>\n\n          <div className=\"relative my-4\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <span className=\"w-full border-t\"></span>\n            </div>\n            <div className=\"relative flex justify-center text-xs uppercase\">\n              <span className=\"bg-background px-2 text-muted-foreground\">O continúa con</span>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Button\n              variant=\"outline\"\n              className=\"w-full gap-2\"\n              disabled={loading}\n              onClick={async () => {\n                try {\n                  await authClient.signIn.social(\n                    {\n                      provider: \"google\",\n                      callbackURL: window.location.origin + \"/dashboard\"\n                    },\n                    {\n                      onRequest: () => {\n                        setLoading(true);\n                      },\n                      onResponse: () => {\n                        setLoading(false);\n                      },\n                      onError: (ctx) => {\n                        toast.error(ctx.error.message || \"Error al iniciar sesión con Google\");\n                      },\n                    },\n                  );\n                } catch (error) {\n                  setLoading(false);\n                  toast.error(\"Error al iniciar sesión con Google\");\n                  console.log('error: ', error)\n                }\n              }}\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"0.98em\" height=\"1em\" viewBox=\"0 0 256 262\">\n                <path fill=\"#4285F4\" d=\"M255.878 133.451c0-10.734-.871-18.567-2.756-26.69H130.55v48.448h71.947c-1.45 12.04-9.283 30.172-26.69 42.356l-.244 1.622l38.755 30.023l2.685.268c24.659-22.774 38.875-56.282 38.875-96.027\"></path>\n                <path fill=\"#34A853\" d=\"M130.55 261.1c35.248 0 64.839-11.605 86.453-31.622l-41.196-31.913c-11.024 7.688-25.82 13.055-45.257 13.055c-34.523 0-63.824-22.773-74.269-54.25l-1.531.13l-40.298 31.187l-.527 1.465C35.393 231.798 79.49 261.1 130.55 261.1\"></path>\n                <path fill=\"#FBBC05\" d=\"M56.281 156.37c-2.756-8.123-4.351-16.827-4.351-25.82c0-8.994 1.595-17.697 4.206-25.82l-.073-1.73L15.26 71.312l-1.335.635C5.077 89.644 0 109.517 0 130.55s5.077 40.905 13.925 58.602z\"></path>\n                <path fill=\"#EB4335\" d=\"M130.55 50.479c24.514 0 41.05 10.589 50.479 19.438l36.844-35.974C195.245 12.91 165.798 0 130.55 0C79.49 0 35.393 29.301 13.925 71.947l42.211 32.783c10.59-31.477 39.891-54.251 74.414-54.251\"></path>\n              </svg>\n              Iniciar con Google\n            </Button>\n            <Button\n              variant=\"outline\"\n              className=\"w-full gap-2\"\n              disabled={loading}\n              onClick={async () => {\n                try {\n                  await authClient.signIn.social(\n                    {\n                      provider: \"facebook\",\n                      callbackURL: \"/dashboard\"\n                    },\n                    {\n                      onRequest: () => {\n                        setLoading(true);\n                      },\n                      onResponse: () => {\n                        setLoading(false);\n                      },\n                      onError: (ctx) => {\n                        toast.error(ctx.error.message || \"Error al iniciar sesión con Facebook\");\n                      },\n                    },\n                  );\n                } catch (error) {\n                  setLoading(false);\n                  toast.error(\"Error al iniciar sesión con Facebook\");\n                  console.log('error: ', error)\n                }\n              }}\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"1em\"\n                height=\"1em\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  d=\"M20 3H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h8.615v-6.96h-2.338v-2.725h2.338v-2c0-2.325 1.42-3.592 3.5-3.592c.699-.002 1.399.034 2.095.107v2.42h-1.435c-1.128 0-1.348.538-1.348 1.325v1.735h2.697l-.35 2.725h-2.348V21H20a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1z\"\n                  fill=\"currentColor\"\n                ></path>\n              </svg>\n              Iniciar con Facebook\n            </Button>\n          </div>\n        </CardContent>\n        <CardFooter>\n          <div className=\"flex justify-center w-full\">\n            <p className=\"text-center text-xs text-neutral-500\">\n              ¿No tienes una cuenta?{\" \"}\n\n              {\n                onRegisterClick ? (\n                  <Button\n                    variant=\"link\"\n                    className=\"p-0\"\n                    onClick={onRegisterClick}\n                  >\n                    Crear Cuenta\n                  </Button>\n                ) : (\n                    <Link\n                      href=\"/sign-up\"\n                      className=\"underline\"\n                    >\n                      Crear Cuenta\n                    </Link>\n                )\n              }\n            </p>\n          </div>\n        </CardFooter>\n      </Card>\n    </>\n  )\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;;;AAgBA,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;IAC5B,OAAO,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAA8B;IACjE,UAAU,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;IACpE,YAAY,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;AAClC;AAWO,SAAS,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,qBAAqB,IAAI,EAAkB;;IACjG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,UAAU;YACV,YAAY;QACd;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,CAC3B;gBACE,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,aAAa,OAAO,QAAQ,CAAC,MAAM,GAAG;YAExC,GACA;gBACE,WAAW;oBACT,WAAW;gBACb;gBACA,YAAY;oBACV,WAAW;gBACb;gBACA,SAAS,CAAC;oBACR,QAAQ,GAAG,CAAC,WAAW;oBACvB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI;gBACnC;gBACA,WAAW;oBAET,6BAA6B;oBAC7B,IAAI,WAAW;wBACb;oBACF;oBAEA,IAAI,oBAAoB;wBACtB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd,oEAAoE;wBACpE,OAAO,IAAI,CAAC;oBACd;gBACF;YACF;QAEJ,EAAE,OAAO,OAAO;YACd,WAAW;YACX,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,GAAG,CAAC,WAAW;QACzB;IACF;IAEA,qBACE;kBACE,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAqB;;;;;;sCAC1C,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;sCAAqB;;;;;;;;;;;;8BAIlD,6LAAC,mIAAA,CAAA,cAAW;;sCACV,6LAAC,mIAAA,CAAA,OAAI;4BAAE,GAAG,IAAI;sCACZ,cAAA,6LAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAW,WAAU;;kDACrD,6LAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;kEACP,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACX,GAAG,KAAK;;;;;;;;;;;kEAGb,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,6LAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;kEACP,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;0EACX;;;;;;;;;;;;kEAIH,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACX,GAAG,KAAK;;;;;;;;;;;kEAGb,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,6LAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4DACP,SAAS,MAAM,KAAK;4DACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;kEAGnC,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;;;;;;;kDAKjD,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU;kDAET,wBACC,6LAAC,oNAAA,CAAA,UAAO;4CAAC,MAAM;4CAAI,WAAU;;;;;mDAE7B;;;;;;;;;;;;;;;;;sCAMR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAI/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,UAAU;oCACV,SAAS;wCACP,IAAI;4CACF,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM,CAC5B;gDACE,UAAU;gDACV,aAAa,OAAO,QAAQ,CAAC,MAAM,GAAG;4CACxC,GACA;gDACE,WAAW;oDACT,WAAW;gDACb;gDACA,YAAY;oDACV,WAAW;gDACb;gDACA,SAAS,CAAC;oDACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI;gDACnC;4CACF;wCAEJ,EAAE,OAAO,OAAO;4CACd,WAAW;4CACX,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4CACZ,QAAQ,GAAG,CAAC,WAAW;wCACzB;oCACF;;sDAEA,6LAAC;4CAAI,OAAM;4CAA6B,OAAM;4CAAS,QAAO;4CAAM,SAAQ;;8DAC1E,6LAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,6LAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,6LAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,6LAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;;;;;;;wCACnB;;;;;;;8CAGR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,UAAU;oCACV,SAAS;wCACP,IAAI;4CACF,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM,CAC5B;gDACE,UAAU;gDACV,aAAa;4CACf,GACA;gDACE,WAAW;oDACT,WAAW;gDACb;gDACA,YAAY;oDACV,WAAW;gDACb;gDACA,SAAS,CAAC;oDACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI;gDACnC;4CACF;wCAEJ,EAAE,OAAO,OAAO;4CACd,WAAW;4CACX,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4CACZ,QAAQ,GAAG,CAAC,WAAW;wCACzB;oCACF;;sDAEA,6LAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDACC,GAAE;gDACF,MAAK;;;;;;;;;;;wCAEH;;;;;;;;;;;;;;;;;;;8BAKZ,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAuC;gCAC3B;gCAGrB,gCACE,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;8CACV;;;;;yDAIC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrB;GAvQgB;;QAEC,qIAAA,CAAA,YAAS;QAEX,iKAAA,CAAA,UAAO;;;KAJN", "debugId": null}}, {"offset": {"line": 3501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem }\r\n"], "names": ["c", "_c", "React", "RadioGroupPrimitive", "Circle", "cn", "RadioGroup", "forwardRef", "t0", "ref", "$", "$i", "Symbol", "for", "className", "props", "t1", "t2", "displayName", "Root", "RadioGroupItem", "t3", "<PERSON><PERSON>"], "mappings": ";;;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,mBAAmB,MAAM,6BAA6B;AAClE,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,EAAE,QAAQ,aAAa;AANhC,YAAY;;;;;;;AAQZ,MAAMC,UAAU,mLAAGJ,KAAK,CAACK,OAAAA,AAAU,OAGjC,CAAAC,EAAA,EAAAC,GAAA;IAAA,MAAAC,CAAA,GAAAT,oLAAAA,AAAA,EAAA;IAAA,IAAAS,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAI,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAM,SAAA,EAAA,GAAAC,OAAA,GAAAP,EAAuB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAJ,CAAA,CAAA,EAAA;QAAAK,KAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAI,SAAA,EAAA;QAGTE,EAAA,8HAAAX,KAAAA,AAAA,EAAG,YAAY,EAAES,SAAS,CAAC;QAAAJ,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,KAAA,IAAAL,CAAA,CAAA,EAAA,KAAAD,GAAA,IAAAC,CAAA,CAAA,EAAA,KAAAM,EAAA,EAAA;QADxCC,EAAA,iBAAA,6LAAA,6KAAA,CAAA,OAAA;YACa,SAA2B,CAA3B,CAAAD,EAA2B;YAAA,GAClCD,KAAK;YACJN,GAAG,CAAHA,CAAAA,GAAG,IACR;;;;;;QAAAC,CAAA,CAAA,EAAA,GAAAK,KAAA;QAAAL,CAAA,CAAA,EAAA,GAAAD,GAAA;QAAAC,CAAA,CAAA,EAAA,GAAAM,EAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,OAJFO,EAIE;AAAA,CAEL,CAAC;;AACFX,UAAU,CAACY,WAAW,iLAAGf,OAAwB,CAACe,WAAN,AAAiB,CAAhBC;AAE7C,MAAMC,cAAc,mLAAGlB,KAAK,CAACK,OAAAA,AAAU,QAGrC,CAAAC,EAAA,EAAAC,GAAA;IAAA,MAAAC,CAAA,IAAAT,mLAAAA,AAAA,EAAA;IAAA,IAAAS,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAI,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAM,SAAA,EAAA,GAAAC,OAAA,GAAAP,EAAuB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAJ,CAAA,CAAA,EAAA;QAAAK,KAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAI,SAAA,EAAA;QAITE,EAAA,8HAAAX,KAAAA,AAAA,EACT,0OAA0O,EAC1OS,SACF,CAAC;QAAAJ,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGDI,EAAA,iBAAA,6LAAA,6KAAA,CAAA,YAAA;YAAyC,SAAkC,EAAlC,kCAAkC;sBACzE,qZAAC,SAAM;gBAAW,SAAuC,EAAvC,uCAAuC,GAC3D,gCAAgC;;;;;;;;;;;QAAAP,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAW,EAAA;IAAA,IAAAX,CAAA,CAAA,EAAA,KAAAK,KAAA,IAAAL,CAAA,CAAA,EAAA,KAAAD,GAAA,IAAAC,CAAA,CAAA,EAAA,KAAAM,EAAA,EAAA;QAVlCK,EAAA,iBAAA,6LAAA,6KAAA,CAAA,OAAA;YACOZ,GAAG,CAAHA,CAAAA,GAAG;YACG,SAGV,CAHU,CAAAO,EAGV;YAAA,GACGD,KAAK,EAET;sBAAAE,EAEgC,CAClC,2BAA2B;;;;;;QAAAP,CAAA,CAAA,EAAA,GAAAK,KAAA;QAAAL,CAAA,CAAA,EAAA,GAAAD,GAAA;QAAAC,CAAA,CAAA,EAAA,GAAAM,EAAA;QAAAN,CAAA,CAAA,GAAA,GAAAW,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAX,CAAA,CAAA,GAAA;IAAA;IAAA,OAX3BW,EAW2B;AAAA,CAE9B,CAAC;;AACFD,cAAc,CAACF,WAAW,iLAAGf,OAAwB,CAACe,WAAN,AAAiB,CAAhBI", "debugId": null}}, {"offset": {"line": 3652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/auth/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/auth/register-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport {\n  <PERSON>,\n  CardContent,\n  CardDescription,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\n// import { Label } from \"@/components/ui/label\";\nimport { useState } from \"react\";\n// import Image from \"next/image\";\nimport { Loader2, /* X */ } from \"lucide-react\";\nimport { toast } from \"react-hot-toast\";\nimport { useRouter } from \"next/navigation\";\nimport Link from 'next/link';\nimport { useForm, /* Controller */ } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport * as z from \"zod\";\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\nimport { authClient } from '@/auth-client';\n\nconst signUpSchema = z.object({\n  firstName: z.string().min(2, { message: \"El nombre debe tener al menos 2 caracteres\" }),\n  lastName: z.string().min(2, { message: \"El apellido debe tener al menos 2 caracteres\" }),\n  email: z.string().email({ message: \"Correo electrónico inválido\" }),\n  password: z.string().min(8, { message: \"La contraseña debe tener al menos 8 caracteres\" }),\n  passwordConfirmation: z.string(),\n  userType: z.enum([\"host\", \"client\"], { required_error: \"Seleccione un tipo de usuario\" }),\n}).refine((data) => data.password === data.passwordConfirmation, {\n  message: \"Las contraseñas no coinciden\",\n  path: [\"passwordConfirmation\"],\n});\n\ntype SignUpFormValues = z.infer<typeof signUpSchema>;\n\ninterface RegisterFormProps {\n  onSuccess?: () => void\n  redirectAfterRegister?: boolean\n  onLoginClick?: () => void\n}\n\nexport function RegisterForm({ onSuccess, onLoginClick, redirectAfterRegister = true }: RegisterFormProps) {\n  // const [image, setImage] = useState<File | null>(null);\n  // const [imagePreview, setImagePreview] = useState<string | null>(null);\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n\n  const form = useForm<SignUpFormValues>({\n    resolver: zodResolver(signUpSchema),\n    defaultValues: {\n      firstName: \"\",\n      lastName: \"\",\n      email: \"\",\n      password: \"\",\n      passwordConfirmation: \"\",\n      userType: \"client\",\n    },\n  });\n\n  // const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n  //   const file = e.target.files?.[0];\n  //   if (file) {\n  //     setImage(file);\n  //     const reader = new FileReader();\n  //     reader.onloadend = () => {\n  //       setImagePreview(reader.result as string);\n  //     };\n  //     reader.readAsDataURL(file);\n  //   }\n  // };\n\n  // const convertImageToBase64 = async (file: File): Promise<string> => {\n  //   return new Promise((resolve, reject) => {\n  //     const reader = new FileReader();\n  //     reader.onload = () => resolve(reader.result as string);\n  //     reader.onerror = reject;\n  //     reader.readAsDataURL(file);\n  //   });\n  // };\n\n  const onSubmit = async (data: SignUpFormValues) => {\n    setLoading(true);\n    try {\n      const response = await authClient.signUp.email({\n        email: data.email,\n        password: data.password,\n        name: `${data.firstName} ${data.lastName}`,\n        // image: image ? await convertImageToBase64(image) : \"\",\n        // @ts-expect-error userType is not defined in the authClient.signUp.email type but required for backend\n        userType: data.userType,\n        // callbackURL: \"/dashboard\",\n        // callbackURL: callbackUrl || window.location.origin + \"/dashboard\",\n        // metadata: {\n        //   userType: data.userType,\n        // },\n        fetchOptions: {\n          onResponse: (ctx) => {\n            setLoading(false);\n            console.log('onResponse', ctx)\n          },\n          onRequest: () => {\n            setLoading(true);\n          },\n          onError: (ctx) => {\n            toast.error(ctx.error.message);\n          },\n          onSuccess: async () => {\n            // router.push(\"/dashboard\");\n            if (onSuccess) {\n              onSuccess()\n            } else {\n              toast.success(\"Cuenta creada exitosamente. Se envió un email de verificación a tu correo. \\n Una vez verificado, serás redirigido al dashboard.\");\n            }\n\n            if (redirectAfterRegister) {\n              router.push('/dashboard')\n            }\n          },\n        },\n      });\n      console.log('response after hooks: ', response)\n    } catch (error) {\n      setLoading(false);\n      toast.error(\"Error al crear la cuenta\");\n      console.log('error: ', error)\n    }\n  };\n\n  return (\n    <>\n      <Card className=\"z-50 rounded-md max-w-md w-full\">\n        <CardHeader>\n          <CardTitle className=\"text-lg md:text-xl\">Crear Cuenta</CardTitle>\n          <CardDescription className=\"text-xs md:text-sm\">\n            Ingresa tus datos para registrarte en DriveLink\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Form {...form}>\n            <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <FormField\n                  control={form.control}\n                  name=\"firstName\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Nombre</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"Nombre\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n                <FormField\n                  control={form.control}\n                  name=\"lastName\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Apellido</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"Apellido\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <FormField\n                control={form.control}\n                name=\"email\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Correo electrónico</FormLabel>\n                    <FormControl>\n                      <Input type=\"email\" placeholder=\"<EMAIL>\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"password\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Contraseña</FormLabel>\n                    <FormControl>\n                      <Input type=\"password\" placeholder=\"Contraseña\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"passwordConfirmation\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Confirmar Contraseña</FormLabel>\n                    <FormControl>\n                      <Input type=\"password\" placeholder=\"Confirmar Contraseña\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"userType\"\n                render={({ field }) => (\n                  <FormItem className=\"space-y-3\">\n                    <FormLabel>Tipo de Usuario</FormLabel>\n                    <FormControl>\n                      <RadioGroup\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                        className=\"flex flex-col space-y-1\"\n                      >\n                        <FormItem className=\"flex items-center space-x-3 space-y-0\">\n                          <FormControl>\n                            <RadioGroupItem value=\"client\" />\n                          </FormControl>\n                          <FormLabel className=\"font-normal\">\n                            Cliente (Quiero rentar autos)\n                          </FormLabel>\n                        </FormItem>\n                        <FormItem className=\"flex items-center space-x-3 space-y-0\">\n                          <FormControl>\n                            <RadioGroupItem value=\"host\" />\n                          </FormControl>\n                          <FormLabel className=\"font-normal\">\n                            Anfitrión (Quiero ofrecer mis autos)\n                          </FormLabel>\n                        </FormItem>\n                      </RadioGroup>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* <div className=\"space-y-2\">\n                <Label htmlFor=\"image\">Foto de Perfil (opcional)</Label>\n                <div className=\"flex items-end gap-4\">\n                  {imagePreview && (\n                    <div className=\"relative w-16 h-16 rounded-sm overflow-hidden\">\n                      <Image\n                        src={imagePreview}\n                        alt=\"Profile preview\"\n                        fill\n                        style={{ objectFit: \"cover\" }}\n                      />\n                    </div>\n                  )}\n                  <div className=\"flex items-center gap-2 w-full\">\n                    <Input\n                      id=\"image\"\n                      type=\"file\"\n                      accept=\"image/*\"\n                      onChange={handleImageChange}\n                      className=\"w-full\"\n                    />\n                    {imagePreview && (\n                      <X\n                        className=\"cursor-pointer\"\n                        onClick={() => {\n                          setImage(null);\n                          setImagePreview(null);\n                        }}\n                      />\n                    )}\n                  </div>\n                </div>\n              </div> */}\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <Loader2 size={16} className=\"animate-spin\" />\n                ) : (\n                  \"Crear Cuenta\"\n                )}\n              </Button>\n            </form>\n          </Form>\n        </CardContent>\n        <CardFooter>\n          <div className=\"flex justify-center w-full\">\n            <p className=\"text-center text-xs text-neutral-500\">\n              ¿Ya tienes una cuenta?{\" \"}\n              {\n                onLoginClick ? (\n                  <Button\n                    variant=\"link\"\n                    className=\"p-0\"\n                    onClick={onLoginClick}\n                  >\n                    Iniciar Sesión\n                  </Button>\n                ) : (\n                  <Link\n                    href=\"/sign-in\"\n                    className=\"underline\"\n                  >\n                    Iniciar Sesión\n                  </Link>\n                )\n              }\n            </p>\n          </div>\n        </CardFooter>\n      </Card>\n    </>\n  )\n}"], "names": ["c", "_c", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardDescription", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Input", "useState", "Loader2", "toast", "useRouter", "Link", "useForm", "zodResolver", "z", "RadioGroup", "RadioGroupItem", "Form", "FormControl", "FormField", "FormItem", "FormLabel", "FormMessage", "authClient", "signUpSchema", "object", "firstName", "string", "min", "message", "lastName", "email", "password", "passwordConfirmation", "userType", "enum", "required_error", "refine", "data", "path", "RegisterForm", "t0", "$", "$i", "Symbol", "for", "onSuccess", "onLoginClick", "redirectAfterRegister", "t1", "undefined", "router", "loading", "setLoading", "t2", "resolver", "defaultValues", "form", "t3", "response", "signUp", "name", "fetchOptions", "onResponse", "ctx", "console", "log", "onRequest", "onError", "_temp", "success", "push", "t4", "error", "onSubmit", "t5", "handleSubmit", "t10", "t6", "t7", "t8", "t9", "control", "_temp2", "_temp3", "_temp4", "_temp5", "_temp6", "_temp7", "t11", "t12", "t13", "t14", "t15", "t16", "field", "field_4", "onChange", "value", "field_3", "field_2", "field_1", "field_0", "ctx_0"], "mappings": ";;;;AAAa,SAAAA,CAAA,IAAAC,EAAA;AAEb,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SACEC,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,UAAU,EACVC,UAAU,EACVC,SAAS,QACJ,sBAAsB;AAC7B,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,iDAAA;AACA,SAASC,QAAQ,QAAQ,OAAO;AAChC,kCAAA;AACA,SAASC,OAAO,CAAE,eAAe,cAAc;AAC/C,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,IAAI,MAAM,WAAW;AAC5B,SAASC,OAAO,CAAE,wBAAwB,iBAAiB;AAC3D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,CAAC,MAAM,KAAK;AACxB,SAASC,UAAU,EAAEC,cAAc,QAAQ,6BAA6B;AACxE,SAASC,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,sBAAsB;AACrG,SAASC,UAAU,QAAQ,eAAe;;;AAxB1C,YAAY;;;;;;;;;;;;;;;;AA0BZ,MAAMC,YAAY,+IAAGV,CAAC,CAACW,OAAAA,AAAM,EAAC;IAC5BC,SAAS,8IAAEZ,CAAC,CAACa,OAAAA,AAAM,CAAC,CAAC,EAACC,GAAG,CAAC,CAAC,EAAE;QAAEC,OAAO,EAAE;IAA6C,CAAC,CAAC;IACvFC,QAAQ,8IAAEhB,CAAC,CAACa,OAAAA,AAAM,CAAC,CAAC,EAACC,GAAG,CAAC,CAAC,EAAE;QAAEC,OAAO,EAAE;IAA+C,CAAC,CAAC;IACxFE,KAAK,8IAAEjB,CAAC,CAACa,OAAM,AAANA,CAAO,CAAC,EAACI,KAAK,CAAC;QAAEF,OAAO,EAAE;IAA8B,CAAC,CAAC;IACnEG,QAAQ,8IAAElB,CAAC,CAACa,OAAAA,AAAM,CAAC,CAAC,EAACC,GAAG,CAAC,CAAC,EAAE;QAAEC,OAAO,EAAE;IAAiD,CAAC,CAAC;IAC1FI,oBAAoB,MAAEnB,CAAC,CAACa,+IAAM,AAANA,CAAO,CAAC;IAChCO,QAAQ,8IAAEpB,CAAC,CAACqB,KAAAA,AAAI,EAAC;QAAC,MAAM;QAAE,QAAQ;KAAC,EAAE;QAAEC,cAAc,EAAE;IAAgC,CAAC;AAC1F,CAAC,CAAC,CAACC,MAAM,EAAEC,IAAI,GAAKA,IAAI,CAACN,QAAQ,KAAKM,IAAI,CAACL,oBAAoB,EAAE;IAC/DJ,OAAO,EAAE,8BAA8B;IACvCU,IAAI,EAAE;QAAC,sBAAsB;KAAA;AAC/B,CAAC,CAAC;AAUK,sBAAAE,EAAA;;IAAA,MAAAC,CAAA,mLAAA5C,IAAAA,AAAA,EAAA;IAAA,IAAA4C,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAsB,MAAA,EAAAI,SAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,EAAAA,EAAA,GAAAR,EAA4E;IAAjD,MAAAO,qBAAA,GAAAC,EAA4B,KAAAC,SAAA,GAAA,OAA5BD,EAA4B;IAGlF,MAAAE,MAAA,yJAAezC;IACf,MAAA,CAAA0C,OAAA,EAAAC,UAAA,CAAA,qKAA8B9C,WAAAA,AAAA,EAAA,KAAc,CAAC;IAAC,IAAA+C,EAAA;IAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEPS,EAAA,GAAA;YAAAC,QAAA,wKAC3B1C,cAAAA,AAAA,EAAAW,YAAwB,CAAC;YAAAgC,aAAA,EAAA;gBAAA9B,SAAA,EAEtB,EAAE;gBAAAI,QAAA,EACH,EAAE;gBAAAC,KAAA,EACL,EAAE;gBAAAC,QAAA,EACC,EAAE;gBAAAC,oBAAA,EACU,EAAE;gBAAAC,QAAA,EACd;YAAQ;QAAA;QAErBQ,CAAA,CAAA,EAAA,GAAAY,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAZ,CAAA,CAAA,EAAA;IAAA;IAVD,MAAAe,IAAA,GAAa7C,gLAAAA,EAA0B0C,EAUtC,CAAC;IAAC,IAAAI,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAI,SAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAM,qBAAA,IAAAN,CAAA,CAAA,EAAA,KAAAS,MAAA,EAAA;QAuBcO,EAAA,GAAA,OAAApB,IAAA;YACfe,UAAU,CAAA,IAAK,CAAC;;YAAA,IAAA;gBAEd,MAAAM,QAAA,GAAA,MAAuBpC,sIAAA,CAAAqC,MAAA,CAAA7B,KAAA,CAAA;oBAAAA,KAAA,EACdO,IAAI,CAAAP,KAAA;oBAAAC,QAAA,EACDM,IAAI,CAAAN,QAAA;oBAAA6B,IAAA,EACR,GAAGvB,IAAI,CAAAZ,SAAA,CAAA,CAAA,EAAcY,IAAI,CAAAR,QAAA,EAAW;oBAAAI,QAAA,EAGhCI,IAAI,CAAAJ,QAAA;oBAAA4B,YAAA,EAAA;wBAAAC,UAAA,GAAAC,GAAA;4BAQVX,UAAU,CAAA,KAAM,CAAC;4BACjBY,OAAA,CAAAC,GAAA,CAAY,YAAY,EAAEF,GAAG,CAAC;wBAAA;wBAAAG,SAAA,EAAAA,CAAA;4BAG9Bd,UAAU,CAAA,IAAK,CAAC;wBAAA;wBAAAe,OAAA,EAAAC,KAAA;wBAAAvB,SAAA,EAAA,MAAAA,CAAA;4BAAA,IAOZA,SAAS,EAAA;gCACXA,SAAS,CAAC,CAAC;4BAAA,OAAA;2LAEXrC,QAAA,CAAA6D,OAAA,CAAc,2IAAkI,CAAC;4BAAA;4BAAA,IAG/ItB,qBAAqB,EAAA;gCACvBG,MAAM,CAAAoB,IAAA,CAAM,YAAY,CAAC;4BAAA;wBAAA;oBAAA;gBAAA,CAIhC,CAAC;gBACFN,OAAA,CAAAC,GAAA,CAAY,wBAAwB,EAAEP,QAAQ,CAAC;YAAA,EAAA,OAAAa,EAAA,EAAA;gBACxCC,KAAA,CAAAA,KAAA,CAAAA,CAAA,CAAAA,EAAK;gBACZpB,UAAU,CAAA,KAAM,CAAC;2KACjB5C,QAAA,CAAAgE,KAAA,CAAY,0BAA0B,CAAC;gBACvCR,OAAA,CAAAC,GAAA,CAAY,SAAS,EAAEO,KAAK,CAAC;YAAA;QAAA;QAEhC/B,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAM,qBAAA;QAAAN,CAAA,CAAA,EAAA,GAAAS,MAAA;QAAAT,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IA9CD,MAAAgC,QAAA,GAAiBhB,EA8ChB;IAAC,IAAAc,EAAA;IAAA,IAAA9B,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAKI2B,EAAA,iBAAA,6LAAC,iJAAU;;8BACT,iUAAC,YAAS;oBAAW,SAAoB,EAApB,oBAAoB;8BAAC,YAAY,EAArD,SAAS;;;;;;8BACV,6LAAC,sJAAe;oBAAW,SAAoB,EAApB,oBAAoB;8BAAC,+CAEhD,EAFC,eAAe,CAGlB,EALC,UAAU,CAKE;;;;;;;;;;;;QAAA9B,CAAA,CAAA,EAAA,GAAA8B,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA9B,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAiC,EAAA;IAAA,IAAAjC,CAAA,CAAA,EAAA,KAAAe,IAAA,IAAAf,CAAA,CAAA,EAAA,KAAAgC,QAAA,EAAA;QAGOC,EAAA,GAAAlB,IAAI,CAAAmB,YAAA,CAAcF,QAAQ,CAAC;QAAAhC,CAAA,CAAA,EAAA,GAAAe,IAAA;QAAAf,CAAA,CAAA,EAAA,GAAAgC,QAAA;QAAAhC,CAAA,CAAA,EAAA,GAAAiC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAjC,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAmC,GAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAvC,CAAA,CAAA,GAAA,KAAAe,IAAA,CAAAyB,OAAA,EAAA;QACzCJ,EAAA,iBAAA,6LAAA,GA2BM;YA3BS,SAAwB,EAAxB,wBAAwB;;8BACrC,iUAAC,YAAS;oBACC,OAAY,CAAZ,CAAArB,IAAI,CAAAyB,OAAA,CAAQ;oBAChB,IAAW,EAAX,WAAW;oBACR,MAQP,CARO,CAAAC,MAAA,CAQP;;;;;;8BAEH,iUAAC,YAAS;oBACC,OAAY,CAAZ,CAAA1B,IAAI,CAAAyB,OAAA,CAAQ;oBAChB,IAAU,EAAV,UAAU;oBACP,MAQP,CARO,CAAAE,MAAA,CAQP,GAEL,EA3BA,GA2BM;;;;;;;;;;;;QAENL,EAAA,iBAAA,iUAAC,YAAS;YACC,OAAY,CAAZ,CAAAtB,IAAI,CAAAyB,OAAA,CAAQ;YAChB,IAAO,EAAP,OAAO;YACJ,MAQP,CARO,CAAAG,MAAA,CAQP,GACD;;;;;;QAEFL,EAAA,iBAAA,iUAAC,YAAS;YACC,OAAY,CAAZ,CAAAvB,IAAI,CAAAyB,OAAA,CAAQ;YAChB,IAAU,EAAV,UAAU;YACP,MAQP,CARO,CAAAI,MAAA,CAQP,GACD;;;;;;QAEFL,EAAA,iBAAA,iUAAC,YAAS;YACC,OAAY,CAAZ,CAAAxB,IAAI,CAAAyB,OAAA,CAAQ;YAChB,IAAsB,EAAtB,sBAAsB;YACnB,MAQP,CARO,CAAAK,MAAA,CAQP,GACD;;;;;;QAEFV,GAAA,iBAAA,6LAAC,gJAAS;YACC,OAAY,CAAZ,CAAApB,IAAI,CAAAyB,OAAA,CAAQ;YAChB,IAAU,EAAV,UAAU;YACP,MA6BP,CA7BO,CAAAM,MAAA,CA6BP,GACD;;;;;;QAAA9C,CAAA,CAAA,GAAA,GAAAe,IAAA,CAAAyB,OAAA;QAAAxC,CAAA,CAAA,GAAA,GAAAmC,GAAA;QAAAnC,CAAA,CAAA,GAAA,GAAAoC,EAAA;QAAApC,CAAA,CAAA,GAAA,GAAAqC,EAAA;QAAArC,CAAA,CAAA,GAAA,GAAAsC,EAAA;QAAAtC,CAAA,CAAA,GAAA,GAAAuC,EAAA;IAAA,OAAA;QAAAJ,GAAA,GAAAnC,CAAA,CAAA,GAAA;QAAAoC,EAAA,GAAApC,CAAA,CAAA,GAAA;QAAAqC,EAAA,GAAArC,CAAA,CAAA,GAAA;QAAAsC,EAAA,GAAAtC,CAAA,CAAA,GAAA;QAAAuC,EAAA,GAAAvC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+C,GAAA;IAAA,IAAA/C,CAAA,CAAA,GAAA,KAAAU,OAAA,EAAA;QAyCCqC,GAAA,GAAArC,OAAO,iBACN,kZAAC,UAAO;YAAO,IAAE,CAAF,CAAA,GAAE;YAAY,SAAc,EAAd,cAAc,GAAG;;;;;mBAE9C,cACD;QAAAV,CAAA,CAAA,GAAA,GAAAU,OAAA;QAAAV,CAAA,CAAA,GAAA,GAAA+C,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgD,GAAA;IAAA,IAAAhD,CAAA,CAAA,GAAA,KAAAU,OAAA,IAAAV,CAAA,CAAA,GAAA,KAAA+C,GAAA,EAAA;QATHC,GAAA,iBAAA,mUAAC,SAAM;YACA,IAAQ,EAAR,QAAQ;YACH,SAAQ,EAAR,QAAQ;YACRtC,QAAO,CAAPA,CAAAA,OAAO,EAEhB;sBAAAqC,GAIA,CACH,EAVC,MAAM,CAUE;;;;;;QAAA/C,CAAA,CAAA,GAAA,GAAAU,OAAA;QAAAV,CAAA,CAAA,GAAA,GAAA+C,GAAA;QAAA/C,CAAA,CAAA,GAAA,GAAAgD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiD,GAAA;IAAA,IAAAjD,CAAA,CAAA,GAAA,KAAAmC,GAAA,IAAAnC,CAAA,CAAA,GAAA,KAAAgD,GAAA,IAAAhD,CAAA,CAAA,GAAA,KAAAiC,EAAA,IAAAjC,CAAA,CAAA,GAAA,KAAAoC,EAAA,IAAApC,CAAA,CAAA,GAAA,KAAAqC,EAAA,IAAArC,CAAA,CAAA,GAAA,KAAAsC,EAAA,IAAAtC,CAAA,CAAA,GAAA,KAAAuC,EAAA,EAAA;QAvJXU,GAAA,iBAAA,6LAAA,IAwJO;YAxJS,QAA2B,CAA3B,CAAAhB,EAA2B;YAAY,SAAW,EAAX,WAAW,CAChE;;gBAAAG,EA2BM,CAEN;gBAAAC,EAYE,CAEF;gBAAAC,EAYE,CAEF;gBAAAC,EAYE,CAEF;gBAAAJ,GAiCE,CAoCF;gBAAAa,GAUS,CACX,EAxJA,IAwJO;;;;;;;QAAAhD,CAAA,CAAA,GAAA,GAAAmC,GAAA;QAAAnC,CAAA,CAAA,GAAA,GAAAgD,GAAA;QAAAhD,CAAA,CAAA,GAAA,GAAAiC,EAAA;QAAAjC,CAAA,CAAA,GAAA,GAAAoC,EAAA;QAAApC,CAAA,CAAA,GAAA,GAAAqC,EAAA;QAAArC,CAAA,CAAA,GAAA,GAAAsC,EAAA;QAAAtC,CAAA,CAAA,GAAA,GAAAuC,EAAA;QAAAvC,CAAA,CAAA,GAAA,GAAAiD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAkD,GAAA;IAAA,IAAAlD,CAAA,CAAA,GAAA,KAAAe,IAAA,IAAAf,CAAA,CAAA,GAAA,KAAAiD,GAAA,EAAA;QA1JXC,GAAA,iBAAA,6LAAC,kJAAW;oCACV,iUAAC,OAAI;gBAAA,GAAKnC,IAAI,EACZ;0BAAAkC,GAwJO,CACT,EA1JC,IAAI,CA2JP,EA5JC,WAAW,CA4JE;;;;;;;;;;;QAAAjD,CAAA,CAAA,GAAA,GAAAe,IAAA;QAAAf,CAAA,CAAA,GAAA,GAAAiD,GAAA;QAAAjD,CAAA,CAAA,GAAA,GAAAkD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAlD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAmD,GAAA;IAAA,IAAAnD,CAAA,CAAA,GAAA,KAAAK,YAAA,EAAA;QACd8C,GAAA,iBAAA,iUAAC,aAAU;sBACT,2MAAA,GAsBM;gBAtBS,SAA4B,EAA5B,4BAA4B;wCACzC,6LAAA,CAoBI;oBApBS,SAAsC,EAAtC,sCAAsC;;wBAAC,sBAC3B;wBAAA,GAAG,CAExB;wBAAA9C,YAAY,iBACV,6LAAC,+IAAM;4BACG,OAAM,EAAN,MAAM;4BACJ,SAAK,EAAL,KAAK;4BACNA,OAAY,CAAZA,CAAAA,YAAY;sCACtB,cAED,EANC,MAAM,CAME;;;;;iDAET,4VAAC,WAAI;4BACE,IAAU,EAAV,UAAU;4BACL,SAAW,EAAX,WAAW;sCACtB,cAED,EALC,IAAI,CAMN,CAEL,EApBA,CAoBI,CACN,EAtBA,GAsBM,CACR,EAxBC,UAAU,CAwBE;;;;;;;;;;;;;;;;;;;;;;QAAAL,CAAA,CAAA,GAAA,GAAAK,YAAA;QAAAL,CAAA,CAAA,GAAA,GAAAmD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAnD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAoD,GAAA;IAAA,IAAApD,CAAA,CAAA,GAAA,KAAAkD,GAAA,IAAAlD,CAAA,CAAA,GAAA,KAAAmD,GAAA,EAAA;QA7LjBC,GAAA,iBAAA;oCACE,iUAAC,OAAI;gBAAW,SAAiC,EAAjC,iCAAiC,CAC/C;;oBAAAtB,EAKa,CACb;oBAAAoB,GA4Jc,CACd;oBAAAC,GAwBa,CACf,EA7LC,IAAI,CA6LE;;;;;;;;QACNnD,CAAA,CAAA,GAAA,GAAAkD,GAAA;QAAAlD,CAAA,CAAA,GAAA,GAAAmD,GAAA;QAAAnD,CAAA,CAAA,GAAA,GAAAoD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAApD,CAAA,CAAA,GAAA;IAAA;IAAA,OA/LHoD,GA+LG;AAAA;;;8IApRUpF,YAAA,CAAU,CAAC;oLAGb;;;KANR8B;AAAA,SAAAgD,OAAA/C,EAAA;IA6KkB,MAAA,EAAAsD,KAAA,EAAAC,OAAAA,EAAA,GAAAvD,EAAS;IAAA,qBAChB,iUAAC,WAAQ;QAAW,SAAW,EAAX,WAAW;;0BAC7B,iUAAC,YAAS;0BAAC,eAAe,EAAzB,SAAS;;;;;;0BACV,6LAAC,kJAAW;wCACV,2UAAC,aAAU;oBACM,aAAc,CAAd,CAAAsD,OAAK,CAAAE,QAAA,CAAS;oBACf,YAAW,CAAX,CAAAF,OAAK,CAAAG,KAAA,CAAM;oBACf,SAAyB,EAAzB,yBAAyB;;sCAEnC,iUAAC,WAAQ;4BAAW,SAAuC,EAAvC,uCAAuC;;8CACzD,iUAAC,cAAW;4DACV,6LAAC,+JAAc;wCAAO,KAAQ,EAAR,QAAQ,GAChC,EAFC,WAAW;;;;;;;;;;;8CAGZ,iUAAC,YAAS;oCAAW,SAAa,EAAb,aAAa;8CAAC,6BAEnC,EAFC,SAAS,CAGZ,EAPC,QAAQ;;;;;;;;;;;;sCAQT,iUAAC,WAAQ;4BAAW,SAAuC,EAAvC,uCAAuC;;8CACzD,iUAAC,cAAW;4DACV,0UAAC,kBAAc;wCAAO,KAAM,EAAN,MAAM,GAC9B,EAFC,WAAW;;;;;;;;;;;8CAGZ,iUAAC,YAAS;oCAAW,SAAa,EAAb,aAAa;8CAAC,oCAEnC,EAFC,SAAS,CAGZ,EAPC,QAAQ,CAQX,EArBC,UAAU,CAsBb,EAvBC,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAwBZ,iUAAC,cAAW,GACd,EA3BC,QAAQ,CA2BE;;;;;;;;;;;AAAA;AAzMtB,SAAAX,OAAA9C,EAAA;IA+JkB,MAAA,EAAAsD,KAAA,EAAAI,OAAAA,EAAA,GAAA1D,EAAS;IAAA,qBAChB,iUAAC,WAAQ;;0BACP,iUAAC,YAAS;0BAAC,oBAAoB,EAA9B,SAAS;;;;;;0BACV,gUAAC,eAAW;wCACV,kUAAC,QAAK;oBAAM,IAAU,EAAV,UAAU;oBAAa,WAAsB,CAAtB,CAAA,yBAAsB;oBAAA,GAAKsD,OAAK,IACrE,EAFC,WAAW;;;;;;;;;;;0BAGZ,iUAAC,cAAW,GACd,EANC,QAAQ,CAME;;;;;;;;;;;AAAA;AAtKtB,SAAAT,OAAA7C,EAAA;IAiJkB,MAAA,EAAAsD,KAAA,EAAAK,OAAAA,EAAA,GAAA3D,EAAS;IAAA,qBAChB,gUAAC,YAAQ;;0BACP,iUAAC,YAAS;0BAAC,UAAU,EAApB,SAAS;;;;;;0BACV,iUAAC,cAAW;wCACV,6LAAC,6IAAK;oBAAM,IAAU,EAAV,UAAU;oBAAa,WAAY,CAAZ,CAAA,eAAY;oBAAA,GAAKsD,OAAK,IAC3D,EAFC,WAAW;;;;;;;;;;;0BAGZ,iUAAC,cAAW,GACd,EANC,QAAQ,CAME;;;;;;;;;;;AAAA;AAxJtB,SAAAV,OAAA5C,EAAA;IAmIkB,MAAA,EAAAsD,KAAA,EAAAM,OAAAA,EAAA,GAAA5D,EAAS;IAAA,qBAChB,iUAAC,WAAQ;;0BACP,iUAAC,YAAS;0BAAC,kBAAkB,EAA5B,SAAS;;;;;;0BACV,iUAAC,cAAW;wCACV,kUAAC,QAAK;oBAAM,IAAO,EAAP,OAAO;oBAAa,WAAoB,EAApB,oBAAoB;oBAAA,GAAKsD,OAAK,IAChE,EAFC,WAAW;;;;;;;;;;;0BAGZ,iUAAC,cAAW,GACd,EANC,QAAQ,CAME;;;;;;;;;;;AAAA;AA1ItB,SAAAX,OAAA3C,EAAA;IAoHoB,MAAA,EAAAsD,KAAA,EAAAO,OAAAA,EAAA,GAAA7D,EAAS;IAAA,qBAChB,iUAAC,WAAQ;;0BACP,gUAAC,aAAS;0BAAC,QAAQ,EAAlB,SAAS;;;;;;0BACV,iUAAC,cAAW;wCACV,kUAAC,QAAK;oBAAa,WAAU,EAAV,UAAU;oBAAA,GAAKsD,OAAK,IACzC,EAFC,WAAW;;;;;;;;;;;0BAGZ,iUAAC,cAAW,GACd,EANC,QAAQ,CAME;;;;;;;;;;;AAAA;AA3HxB,SAAAZ,OAAA1C,EAAA;IAuGoB,MAAA,EAAAsD,KAAAA,EAAA,GAAAtD,EAAS;IAAA,qBAChB,iUAAC,WAAQ;;0BACP,iUAAC,YAAS;0BAAC,MAAM,EAAhB,SAAS;;;;;;0BACV,iUAAC,cAAW;wCACV,kUAAC,QAAK;oBAAa,WAAQ,EAAR,QAAQ;oBAAA,GAAKsD,KAAK,IACvC,EAFC,WAAW;;;;;;;;;;;0BAGZ,iUAAC,cAAW,GACd,EANC,QAAQ,CAME;;;;;;;;;;;AAAA;AA9GxB,SAAA1B,MAAAkC,KAAA;+JA+DK9F,QAAA,CAAAgE,KAAA,CAAYT,KAAG,CAAAS,KAAA,CAAA5C,OAAc,CAAC;AAAA", "debugId": null}}, {"offset": {"line": 4384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28root%29/vehicles/%5Bid%5D/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28root%29/vehicles/%5Bid%5D/vehicle-detail-client.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState, useEffect, useMemo } from \"react\"\r\nimport { Vehicle, vehiclesApi } from \"@/lib/api/vehicles.api\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from \"@/components/ui/tabs\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport { Card, CardContent } from \"@/components/ui/card\"\r\nimport { useRouter } from \"next/navigation\"\r\nimport { ChevronRight, /* Info, Calendar, Clock */ } from \"lucide-react\"\r\nimport Image from \"next/image\"\r\n// import Link from 'next/link'\r\nimport DateRangeModal from '@/components/vehicles/date-range-modal'\r\nimport { DateTime } from 'luxon'\r\nimport { VehicleGallery } from '@/components/vehicles/vehicle-gallery'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { useUser } from \"@/context/user-context\"\r\nimport FavoriteButton from '@/components/vehicles/favorite-button'\r\nimport { setCookie } from \"cookies-next\"\r\nimport { <PERSON><PERSON>, DialogContent, DialogTitle } from \"@/components/ui/dialog\"\r\nimport { LoginForm } from \"@/components/auth/login-form\"\r\nimport { RegisterForm } from \"@/components/auth/register-form\"\r\nimport toast from \"react-hot-toast\"\r\nimport { addDays, differenceInDays, /* format */ } from \"date-fns\"\r\nimport Link from 'next/link'\r\n// import { es } from \"date-fns/locale\"\r\n// import { Badge } from \"@/components/ui/badge\"\r\n\r\nconst featureLabels: Record<string, Record<string, string>> = {\r\n  mx: {\r\n    transmission: \"Transmisión\",\r\n    fuelType: \"Combustible\",\r\n    seats: \"Asientos\",\r\n    mileage: \"Kilometraje\",\r\n    registrationNumber: \"Matrícula\",\r\n    insurancePolicy: \"Seguro\",\r\n    rules: \"Reglas\",\r\n    location: \"Ubicación\",\r\n    weeklyRate: \"Tarifa semanal\",\r\n    monthlyRate: \"Tarifa mensual\"\r\n  },\r\n  en: {\r\n    transmission: \"Transmission\",\r\n    fuelType: \"Fuel Type\",\r\n    seats: \"Seats\",\r\n    mileage: \"Mileage\",\r\n    registrationNumber: \"Registration Number\",\r\n    insurancePolicy: \"Insurance Policy\",\r\n    rules: \"Rules\",\r\n    location: \"Location\",\r\n    weeklyRate: \"Weekly Rate\",\r\n    monthlyRate: \"Monthly Rate\"\r\n  }\r\n}\r\n\r\nconst labels: Record<string, Record<string, string>> = {\r\n  mx: {\r\n    automatic: \"Automático\",\r\n    manual: \"Manual\"\r\n  },\r\n  en: {\r\n    automatic: \"Automatic\",\r\n    manual: \"Manual\"\r\n  }\r\n}\r\n\r\ninterface VehicleDetailClientProps {\r\n  params: {\r\n    id: string\r\n  }\r\n  vehicle: Vehicle\r\n}\r\n\r\nexport default function VehicleDetailClient({ params: { id }, vehicle }: VehicleDetailClientProps) {\r\n  const [dateRange, setDateRange] = useState<{ startDate: Date; endDate: Date }>({\r\n    startDate: new Date(),\r\n    endDate: addDays(new Date(), 1)\r\n  })\r\n  const [total, setTotal] = useState(0)\r\n  const router = useRouter()\r\n  const { user } = useUser()\r\n  console.log('user', user)\r\n\r\n  // Estado para el modal de autenticación\r\n  const [showAuthDialog, setShowAuthDialog] = useState(false)\r\n  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')\r\n\r\n  // const { data: vehicle, isLoading, error } = useQuery({\r\n  //   queryKey: ['vehicle', id],\r\n  //   // queryFn: () => vehiclesApi.getById(id),\r\n  //   queryFn: () => getVehicleById(id),\r\n  //   staleTime: 60 * 60 * 1000, // Caché por 1 hora\r\n  // })\r\n  // console.log('vehicle', vehicle)\r\n\r\n  // Obtener fechas no disponibles\r\n  const { data: unavailableDates } = useQuery({\r\n    queryKey: ['vehicle-unavailable-dates', id],\r\n    queryFn: () => vehiclesApi.getUnavailableDates(id),\r\n    staleTime: 60 * 60 * 1000, // Caché por 1 hora\r\n    enabled: !!id\r\n  })\r\n\r\n  // console.log('unavailableDatesData', unavailableDates)\r\n\r\n  // Obtener configuración de disponibilidad\r\n  const { data: availabilityData } = useQuery({\r\n    queryKey: ['vehicle-availability', id],\r\n    queryFn: () => vehiclesApi.getAvailabilitySettings(id),\r\n    // staleTime: 60 * 60 * 1000, // Caché por 1 hora\r\n    enabled: !!id,\r\n    retry: 1, // Limitar reintentos en caso de error\r\n    // onError: (error) => {\r\n    //   console.error(\"Error fetching availability settings:\", error);\r\n    // }\r\n  })\r\n\r\n  // const unavailableDates = unavailableDatesData?.data || []\r\n\r\n  // Configuración de disponibilidad por defecto\r\n  const availability = useMemo(() => {\r\n    return availabilityData || {\r\n      minimumRentalNights: 1,\r\n      maximumRentalNights: 30,\r\n      defaultCheckInTime: \"14:00\",\r\n      defaultCheckOutTime: \"12:00\",\r\n      mondayAvailable: true,\r\n      tuesdayAvailable: true,\r\n      wednesdayAvailable: true,\r\n      thursdayAvailable: true,\r\n      fridayAvailable: true,\r\n      saturdayAvailable: true,\r\n      sundayAvailable: true,\r\n      blockedDates: []\r\n    }\r\n  }, [availabilityData])\r\n\r\n\r\n  // Inicializar el rango de fechas por defecto basado en la configuración\r\n  useEffect(() => {\r\n    if (availability && vehicle) {\r\n      const today = new Date()\r\n      const startDate = addDays(today, 1) // Por defecto, empezar mañana\r\n      const endDate = addDays(startDate, Math.max(availability.minimumRentalNights, 0))\r\n\r\n      setDateRange({\r\n        startDate,\r\n        endDate\r\n      })\r\n\r\n      // Calcular el total inicial (días, no días)\r\n      const nights = Math.max(availability.minimumRentalNights, 0)\r\n      setTotal(vehicle.price * nights)\r\n    }\r\n  }, [availability, vehicle])\r\n\r\n  // Actualizar el total cuando cambia el rango de fechas\r\n  useEffect(() => {\r\n    if (vehicle && dateRange) {\r\n      // Calcular el número de días (días - 1)\r\n      const diffNights = differenceInDays(dateRange.endDate, dateRange.startDate)\r\n      const newTotal = vehicle.price * diffNights\r\n      setTotal(newTotal)\r\n    }\r\n  }, [dateRange, vehicle])\r\n\r\n  // Función para obtener los días de la semana no disponibles\r\n  const getUnavailableDaysOfWeek = () => {\r\n    const unavailableDays = []\r\n\r\n    if (!availability.mondayAvailable) unavailableDays.push(1) // Lunes\r\n    if (!availability.tuesdayAvailable) unavailableDays.push(2) // Martes\r\n    if (!availability.wednesdayAvailable) unavailableDays.push(3) // Miércoles\r\n    if (!availability.thursdayAvailable) unavailableDays.push(4) // Jueves\r\n    if (!availability.fridayAvailable) unavailableDays.push(5) // Viernes\r\n    if (!availability.saturdayAvailable) unavailableDays.push(6) // Sábado\r\n    if (!availability.sundayAvailable) unavailableDays.push(0) // Domingo\r\n\r\n    return unavailableDays\r\n  }\r\n\r\n  // Función para verificar si un día está disponible\r\n  const isDateDisabled = (date: Date) => {\r\n    // Verificar si es un día de la semana no disponible\r\n    const unavailableDaysOfWeek = getUnavailableDaysOfWeek()\r\n    if (unavailableDaysOfWeek.includes(date.getDay())) {\r\n      return true\r\n    }\r\n\r\n    // Verificar si está en las fechas bloqueadas manualmente\r\n    const blockedDates = Array.isArray(availability.blockedDates)\r\n      ? availability.blockedDates\r\n      : []\r\n\r\n    // Verificar si está en las fechas no disponibles (reservas)\r\n    return unavailableDates?.some(\r\n      (dateStr) => {\r\n        const unavailableDate = new Date(dateStr)\r\n        return (\r\n          date.getFullYear() === unavailableDate.getFullYear() &&\r\n          date.getMonth() === unavailableDate.getMonth() &&\r\n          date.getDate() === unavailableDate.getDate()\r\n        )\r\n      }\r\n    ) || blockedDates.some(\r\n      (blockedDate: any) => {\r\n        if (typeof blockedDate === 'string') {\r\n          const date1 = new Date(blockedDate)\r\n          return (\r\n            date.getFullYear() === date1.getFullYear() &&\r\n            date.getMonth() === date1.getMonth() &&\r\n            date.getDate() === date1.getDate()\r\n          )\r\n        } else if (blockedDate && blockedDate.date) {\r\n          const date1 = new Date(blockedDate.date)\r\n          return (\r\n            date.getFullYear() === date1.getFullYear() &&\r\n            date.getMonth() === date1.getMonth() &&\r\n            date.getDate() === date1.getDate()\r\n          )\r\n        }\r\n        return false\r\n      }\r\n    )\r\n  }\r\n  console.log('isDateDisabled', isDateDisabled(new Date()))\r\n\r\n  // Función para manejar el cambio de fechas\r\n  const handleDateChange = (range: { startDate: Date; endDate: Date }) => {\r\n    // Verificar que el rango cumple con el mínimo de días\r\n    const diffNights = differenceInDays(range.endDate, range.startDate)\r\n\r\n    if (diffNights < availability.minimumRentalNights) {\r\n      toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)\r\n      return\r\n    }\r\n\r\n    if (diffNights > availability.maximumRentalNights) {\r\n      toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)\r\n      return\r\n    }\r\n\r\n    setDateRange(range)\r\n\r\n    // Actualizar el total (no es necesario aquí ya que se actualiza en el useEffect)\r\n    // El useEffect se encargará de actualizar el total\r\n  }\r\n\r\n  // Función para manejar el botón de reserva\r\n  const handleReserveClick = () => {\r\n    if (!dateRange) {\r\n      toast.error(\"Por favor selecciona las fechas de tu reserva\")\r\n      return\r\n    }\r\n\r\n    // Asegurarse de que dateRange.startDate y dateRange.endDate son objetos Date válidos\r\n    if (!(dateRange.startDate instanceof Date) || !(dateRange.endDate instanceof Date)) {\r\n      // Intentar convertir a objetos Date si no lo son\r\n      try {\r\n        const startDate = dateRange.startDate instanceof Date ? dateRange.startDate : new Date(dateRange.startDate);\r\n        const endDate = dateRange.endDate instanceof Date ? dateRange.endDate : new Date(dateRange.endDate);\r\n\r\n        // Verificar que el rango cumple con el mínimo de días\r\n        const diffNights = differenceInDays(endDate, startDate);\r\n\r\n        if (diffNights < availability.minimumRentalNights) {\r\n          toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)\r\n          return\r\n        }\r\n\r\n        if (diffNights > availability.maximumRentalNights) {\r\n          toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)\r\n          return\r\n        }\r\n\r\n        // Redirigir con las fechas convertidas\r\n        if (!user) {\r\n          setCookie('bookingRedirect', `/booking/${id}?from=${startDate.toISOString()}&to=${endDate.toISOString()}`)\r\n          setAuthMode('login')\r\n          setShowAuthDialog(true)\r\n          return\r\n        }\r\n\r\n        router.push(`/booking/${id}?from=${startDate.toISOString()}&to=${endDate.toISOString()}`)\r\n      } catch (error) {\r\n        console.error(\"Error al procesar las fechas:\", error);\r\n        toast.error(\"Error al procesar las fechas. Por favor, selecciona las fechas nuevamente.\")\r\n        return;\r\n      }\r\n    } else {\r\n      // Verificar que el rango cumple con el mínimo de días\r\n      const diffNights = differenceInDays(dateRange.endDate, dateRange.startDate);\r\n\r\n      if (diffNights < availability.minimumRentalNights) {\r\n        toast.error(`La reserva debe ser de al menos ${availability.minimumRentalNights} días`)\r\n        return\r\n      }\r\n\r\n      if (diffNights > availability.maximumRentalNights) {\r\n        toast.error(`La reserva no puede exceder ${availability.maximumRentalNights} días`)\r\n        return\r\n      }\r\n\r\n      // Verificar si el usuario está autenticado\r\n      if (!user) {\r\n        // Guardar la URL de redirección para después del login\r\n        setCookie('bookingRedirect', `/booking/${id}?from=${dateRange.startDate.toISOString()}&to=${dateRange.endDate.toISOString()}`)\r\n        setAuthMode('login')\r\n        setShowAuthDialog(true)\r\n        return\r\n      }\r\n\r\n      // Si está autenticado, redirigir a la página de reserva\r\n      router.push(`/booking/${id}?from=${dateRange.startDate.toISOString()}&to=${dateRange.endDate.toISOString()}`)\r\n    }\r\n  }\r\n\r\n  // Función para cambiar entre login y registro\r\n  const toggleAuthMode = () => {\r\n    setAuthMode(authMode === 'login' ? 'register' : 'login')\r\n  }\r\n\r\n  // Función para cerrar el modal después de autenticación exitosa\r\n  const handleAuthSuccess = () => {\r\n    setShowAuthDialog(false)\r\n  }\r\n\r\n  // Obtener características principales\r\n  const parseDate = (date: string) => {\r\n    // parse it like this format: \"25 de junio de 2025\" using luxon, date is a iso string\r\n    return DateTime.fromISO(date).toFormat(\"dd 'de' MMMM 'de' yyyy\", { locale: \"es\" })\r\n  }\r\n\r\n\r\n  // Obtener imágenes del vehículo\r\n  const images = Array.isArray(vehicle.images) ? vehicle.images : [];\r\n\r\n  // Obtener características principales\r\n  // const transmission = vehicle.transmission || '';\r\n  // const transmission = featureLabels[\"mx\"][\"transmission\"] || '';\r\n  const transmission = labels[\"mx\"][vehicle.transmission] || '';\r\n  console.log('vehicle', vehicle)\r\n  const mileage = vehicle.features?.mileage || 0;\r\n  const location = vehicle.features?.location || '';\r\n  const year = vehicle.year || '';\r\n\r\n  // Obtener días no disponibles de la semana en formato legible\r\n  // const unavailableDaysOfWeek = getUnavailableDaysOfWeek()\r\n  // const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado']\r\n  // const unavailableDayNames = unavailableDaysOfWeek.map(day => dayNames[day])\r\n\r\n  // // Calcular duración de la reserva\r\n  // const rentalDuration = dateRange\r\n  //   ? differenceInDays(dateRange.endDate, dateRange.startDate) + 1\r\n  //   : availability.minimumRentalDays;\r\n\r\n  console.log('availability', availability)\r\n\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      {/* Breadcrumb */}\r\n      <div className=\"text-sm text-gray-500 mb-4 flex items-center\">\r\n        {/* <span>AUTOS DISPONIBLES</span> */}\r\n        <Link href=\"/vehicles\" className=\"hover:underline\">\r\n          AUTOS DISPONIBLES\r\n        </Link>\r\n        <ChevronRight className=\"h-4 w-4 mx-1\" />\r\n        <span>{vehicle.make}</span>\r\n        <ChevronRight className=\"h-4 w-4 mx-1\" />\r\n        <span>{vehicle.model}</span>\r\n        <ChevronRight className=\"h-4 w-4 mx-1\" />\r\n        <span>{year}</span>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n        {/* Columna izquierda - Imágenes y detalles */}\r\n        <div className=\"lg:col-span-2 space-y-6\">\r\n          {/* Etiqueta de recién publicado */}\r\n          <VehicleGallery\r\n            vehicle={vehicle}\r\n            images={images}\r\n          // currentImageIndex={currentImageIndex}\r\n          // setCurrentImageIndex={setCurrentImageIndex}\r\n          />\r\n\r\n          {/* Título y precio */}\r\n          <div className=\"flex justify-between items-start\">\r\n            <div className=\"flex-1\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <h1 className=\"text-2xl font-bold\">\r\n                  {vehicle.make} {vehicle.model} {transmission} {year}\r\n                </h1>\r\n                {/* Botón de favoritos */}\r\n                <FavoriteButton\r\n                  vehicleId={vehicle.id}\r\n                  size=\"lg\"\r\n                  variant=\"outline\"\r\n                  className=\"flex-shrink-0\"\r\n                />\r\n              </div>\r\n              <div className=\"flex items-center mt-1\">\r\n                <span className=\"text-gray-600\">{mileage.toLocaleString()} km</span>\r\n                <span className=\"mx-2\">•</span>\r\n                <span className=\"text-gray-600\">{location}</span>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-sm text-gray-600\">Precio desde</div>\r\n              <div className=\"text-2xl font-bold\">${vehicle.price.toLocaleString()}</div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Características principales */}\r\n          <div className=\"mt-8\">\r\n            <h2 className=\"text-xl font-bold mb-4\">Equipamiento destacado</h2>\r\n            <div className=\"grid grid-cols-3 gap-4\">\r\n              {vehicle.features && Object.entries(vehicle.features).slice(0, 6).map(([key, value]) => (\r\n                <div key={key} className=\"bg-gray-50 p-4 rounded-lg\">\r\n                  {/* <div className=\"text-sm text-gray-500 capitalize\">{key}</div> */}\r\n                  <div className=\"text-sm text-gray-500 capitalize\">{featureLabels[\"mx\"][key]}</div>\r\n                  <div className=\"font-medium\">{String(value)}</div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Características detalladas */}\r\n          <div className=\"mt-8\">\r\n            <h2 className=\"text-xl font-bold mb-4\">Características principales</h2>\r\n\r\n            <Tabs defaultValue=\"general\">\r\n              <TabsList className=\"w-full border-b mb-4\">\r\n                <TabsTrigger value=\"general\">General</TabsTrigger>\r\n                <TabsTrigger value=\"exterior\">Exterior</TabsTrigger>\r\n                <TabsTrigger value=\"equipamiento\">Equipamiento</TabsTrigger>\r\n                <TabsTrigger value=\"seguridad\">Seguridad</TabsTrigger>\r\n                <TabsTrigger value=\"interior\">Interior</TabsTrigger>\r\n              </TabsList>\r\n\r\n              <TabsContent value=\"general\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  {vehicle.features && Object.entries(vehicle.features).map(([key, value]) => {\r\n                    // Verificar si la clave existe en featureLabels\r\n                    const label = featureLabels[\"mx\"][key] || key;\r\n\r\n                    return (\r\n                      <div key={key} className=\"bg-gray-50 p-4 rounded-lg\">\r\n                        <div className=\"text-sm text-gray-500 capitalize mb-1\">{label}</div>\r\n                        <div className=\"font-medium\">\r\n                          {typeof value === 'string' && value.length > 50\r\n                            ? `${value.substring(0, 50)}...`\r\n                            : String(value)}\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"exterior\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Tipo de Carrocería</div>\r\n                    <div className=\"font-medium\">SUV</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Color</div>\r\n                    <div className=\"font-medium\">{vehicle.color}</div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"equipamiento\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Aire acondicionado</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Bluetooth</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Navegación GPS</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"seguridad\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Airbags</div>\r\n                    <div className=\"font-medium\">6</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Control de estabilidad</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Frenos ABS</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"interior\" className=\"space-y-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Tapicería</div>\r\n                    <div className=\"font-medium\">Tela</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Asientos eléctricos</div>\r\n                    <div className=\"font-medium\">No</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <div className=\"text-sm text-gray-500 mb-1\">Volante ajustable</div>\r\n                    <div className=\"font-medium\">Sí</div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n            </Tabs>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Columna derecha - Reserva */}\r\n        <div>\r\n          <div className='sticky top-6 space-y-4'>\r\n\r\n            <Card className=\"\">\r\n              <CardContent className=\"p-6\">\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <h2 className=\"text-2xl font-bold\">${vehicle.price.toLocaleString()} <span className=\"text-base font-normal\">/ día</span></h2>\r\n                  </div>\r\n\r\n                  <Separator />\r\n\r\n                  <div className=\"mb-6\">\r\n                    <div className=\"mb-4\">\r\n                      <label className=\"block text-sm font-medium mb-1\">Selecciona las fechas</label>\r\n                      <DateRangeModal\r\n                        unavailableDates={unavailableDates}\r\n                        onChange={handleDateChange}\r\n                        initialDateRange={dateRange}\r\n                        minimumRentalNights={availability.minimumRentalNights}\r\n                        maximumRentalNights={availability.maximumRentalNights}\r\n                      />\r\n                    </div>\r\n\r\n\r\n\r\n                    <div className=\"flex justify-between items-center font-bold\">\r\n                      <span>Total</span>\r\n                      <span className=\"text-sm\">${total.toLocaleString()}</span>\r\n                    </div>\r\n\r\n                    {/* Información adicional sobre la reserva */}\r\n                    <div className=\"mt-2 text-xs text-gray-500 space-y-1\">\r\n                      <div className=\"flex justify-between\">\r\n                        <span>Precio por día:</span>\r\n                        <span>${vehicle.price.toLocaleString()}</span>\r\n                      </div>\r\n                      {dateRange && (\r\n                        <div className=\"flex justify-between\">\r\n                          <span>Noches seleccionadas:</span>\r\n                          <span>{differenceInDays(dateRange.endDate, dateRange.startDate)}</span>\r\n                        </div>\r\n                      )}\r\n                      <div className=\"flex justify-between\">\r\n                        <span>Total:</span>\r\n                        <span>${total.toLocaleString()}</span>\r\n                      </div>\r\n                      <div className=\"flex justify-between\">\r\n                        <span>Mínimo de días para rentar:</span>\r\n                        <span>{availability.minimumRentalNights}</span>\r\n                      </div>\r\n                      <div className=\"flex justify-between\">\r\n                        <span>Máximo de días para rentar:</span>\r\n                        <span>{availability.maximumRentalNights}</span>\r\n                      </div>\r\n                    </div>\r\n\r\n                  </div>\r\n\r\n                  <Button\r\n                    className=\"w-full bg-[#1a2b5e] hover:bg-[#152348]\"\r\n                    onClick={handleReserveClick}\r\n                  >\r\n                    <span className=\"font-medium\">Reservar</span>\r\n                  </Button>\r\n\r\n                  <p className=\"text-sm text-muted-foreground text-center\">\r\n                    No se te cobrará nada todavía\r\n                  </p>\r\n\r\n                  <div className=\"pt-4 space-y-2\">\r\n                    <div className=\"flex items-center text-sm\">\r\n                      <svg className=\"h-5 w-5 mr-2 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                      </svg>\r\n                      <span>Auto con certificado Autoop</span>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center text-sm\">\r\n                      <svg className=\"h-5 w-5 mr-2 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                      </svg>\r\n                      <span>Alguna caracteristica aqui</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n              </CardContent>\r\n            </Card>\r\n            <div className=\"border rounded-lg p-6 hidden lg:block\">\r\n              <div className=\"flex items-center gap-4 mb-4\">\r\n                <div className=\"w-12 h-12 rounded-full bg-gray-200 overflow-hidden relative\">\r\n                  <Image\r\n                    src={vehicle.host.image || \"/placeholder.svg?height=48&width=48\"}\r\n                    alt=\"Host\"\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-medium\">{vehicle.host.name}</h3>\r\n                  <p className=\"text-sm text-gray-500\">Miembro desde {parseDate(vehicle.host.createdAt)}</p>\r\n                </div>\r\n              </div>\r\n\r\n              <Button variant=\"outline\" className=\"w-full\">\r\n                <span className=\"font-medium\">Contact Host</span>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n\r\n      {/* Modal de autenticación */}\r\n      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog} >\r\n        <DialogContent className=\"sm:max-w-md \">\r\n        <DialogTitle className='sr-only'>\r\n          {authMode === 'login' ? 'Inicia sesión para continuar' : 'Crea una cuenta para continuar'}\r\n          </DialogTitle>\r\n          <div className='mt-5'>\r\n            {authMode === 'login' ? (\r\n              <LoginForm\r\n                onSuccess={handleAuthSuccess}\r\n                onRegisterClick={toggleAuthMode}\r\n                redirectAfterLogin={true}\r\n              />\r\n            ) : (\r\n              <RegisterForm\r\n                onSuccess={handleAuthSuccess}\r\n                onLoginClick={toggleAuthMode}\r\n                redirectAfterRegister={true}\r\n              />\r\n            )}\r\n          </div>\r\n\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  )\r\n}\r\n\r\n\r\n\r\n\r\n"], "names": ["useState", "useEffect", "useMemo", "vehiclesApi", "<PERSON><PERSON>", "Tabs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabsList", "TabsTrigger", "Separator", "Card", "<PERSON><PERSON><PERSON><PERSON>", "useRouter", "ChevronRight", "Image", "DateRangeModal", "DateTime", "VehicleGallery", "useQuery", "useUser", "FavoriteButton", "<PERSON><PERSON><PERSON><PERSON>", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "LoginForm", "RegisterForm", "toast", "addDays", "differenceInDays", "Link", "featureLabels", "mx", "transmission", "fuelType", "seats", "mileage", "registrationNumber", "insurancePolicy", "rules", "location", "weeklyRate", "monthlyRate", "en", "labels", "automatic", "manual", "VehicleDetailClient", "params", "id", "vehicle", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "Date", "endDate", "total", "setTotal", "router", "user", "console", "log", "showAuthDialog", "setShowAuthDialog", "authMode", "setAuthMode", "data", "unavailableDates", "query<PERSON><PERSON>", "queryFn", "getUnavailableDates", "staleTime", "enabled", "availabilityData", "getAvailabilitySettings", "retry", "availability", "minimumRentalNights", "maximumRentalNights", "defaultCheckInTime", "defaultCheckOutTime", "mondayAvailable", "tuesdayAvailable", "wednesdayAvailable", "thursdayAvailable", "fridayAvailable", "saturdayAvailable", "sundayAvailable", "blockedDates", "today", "Math", "max", "nights", "price", "diffNights", "newTotal", "getUnavailableDaysOfWeek", "unavailableDays", "push", "isDateDisabled", "date", "unavailableDaysOfWeek", "includes", "getDay", "Array", "isArray", "some", "dateStr", "unavailableDate", "getFullYear", "getMonth", "getDate", "blockedDate", "date1", "handleDateChange", "range", "error", "handleReserveClick", "toISOString", "toggleAuthMode", "handleAuthSuccess", "parseDate", "fromISO", "toFormat", "locale", "images", "features", "year", "make", "model", "toLocaleString", "Object", "entries", "slice", "map", "key", "value", "String", "label", "length", "substring", "color", "host", "image", "name", "createdAt"], "mappings": ";;;;AAEA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AACpD,SAAkBC,WAAW,QAAQ,wBAAwB;AAC7D,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,IAAI,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,sBAAsB;AAC/E,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,IAAI,EAAEC,WAAW,QAAQ,sBAAsB;AACxD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,YAAY,CAAE,mCAAmC,cAAc;AACxE,OAAOC,KAAK,MAAM,YAAY;AAC9B,+BAAA;AACA,OAAOC,cAAc,MAAM,wCAAwC;;AACnE,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,MAAM,EAAEC,aAAa,EAAEC,WAAW,QAAQ,wBAAwB;AAC3E,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,OAAOC,KAAK,MAAM,iBAAiB;;AACnC,SAASC,OAAO,EAAEC,gBAAgB,CAAE,oBAAoB,UAAU;AAClE,OAAOC,IAAI,MAAM,WAAW;;;AAxB5B,YAAY;;;;;;;;;;;;;;;;;;;;;;;AAyBZ,uCAAA;AACA,gDAAA;AAEA,MAAMC,aAAqD,GAAG;IAC5DC,EAAE,EAAE;QACFC,YAAY,EAAE,aAAa;QAC3BC,QAAQ,EAAE,aAAa;QACvBC,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE,aAAa;QACtBC,kBAAkB,EAAE,WAAW;QAC/BC,eAAe,EAAE,QAAQ;QACzBC,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE,WAAW;QACrBC,UAAU,EAAE,gBAAgB;QAC5BC,WAAW,EAAE;IACf,CAAC;IACDC,EAAE,EAAE;QACFV,YAAY,EAAE,cAAc;QAC5BC,QAAQ,EAAE,WAAW;QACrBC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,SAAS;QAClBC,kBAAkB,EAAE,qBAAqB;QACzCC,eAAe,EAAE,kBAAkB;QACnCC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE;IACf;AACF,CAAC;AAED,MAAME,MAA8C,GAAG;IACrDZ,EAAE,EAAE;QACFa,SAAS,EAAE,YAAY;QACvBC,MAAM,EAAE;IACV,CAAC;IACDH,EAAE,EAAE;QACFE,SAAS,EAAE,WAAW;QACtBC,MAAM,EAAE;IACV;AACF,CAAC;AASc,SAASC,mBAAmBA,CAAC,EAAEC,MAAM,EAAE,EAAEC,EAAAA,EAAI,EAAEC,OAAAA,EAAmC,EAAE;;IACjG,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,qKAAGpD,WAAAA,AAAQ,EAAqC;QAC7EqD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,OAAO,gJAAE3B,UAAAA,AAAO,EAAC,IAAI0B,IAAI,CAAC,CAAC,EAAE,CAAC;IAChC,CAAC,CAAC;IACF,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,qKAAGzD,WAAAA,AAAQ,EAAC,CAAC,CAAC;IACrC,MAAM0D,MAAM,OAAG9C,kJAAAA;IACf,MAAM,EAAE+C,IAAAA,EAAM,uJAAU,AAAPxC,CAAQ,CAAC;IAC1ByC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEF,IAAI,CAAC;IAEzB,wCAAA;IACA,MAAM,CAACG,cAAc,EAAEC,iBAAiB,CAAC,qKAAG/D,WAAAA,AAAQ,EAAC,KAAK,CAAC;IAC3D,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,qKAAGjE,WAAQ,AAARA,EAA+B,OAAO,CAAC;IAEvE,yDAAA;IACA,+BAAA;IACA,+CAAA;IACA,uCAAA;IACA,mDAAA;IACA,KAAA;IACA,kCAAA;IAEA,gCAAA;IACA,MAAM,EAAEkE,IAAI,EAAEC,gBAAAA,EAAkB,iMAAGjD,EAAS;QAC1CkD,QAAQ,EAAE;YAAC,2BAA2B;YAAEnB,EAAE;SAAC;QAC3CoB,OAAO;4CAAEA,CAAA,2IAAMlE,cAAW,CAACmE,mBAAmB,CAACrB,EAAE,CAAC;;QAClDsB,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QAAE,mBAAA;QAC3BC,OAAO,EAAE,CAAC,CAACvB;IACb,CAAC,CAAC;IAEF,wDAAA;IAEA,0CAAA;IACA,MAAM,EAAEiB,IAAI,EAAEO,gBAAAA,EAAkB,iMAAGvD,EAAS;QAC1CkD,QAAQ,EAAE;YAAC,sBAAsB;YAAEnB,EAAE;SAAC;QACtCoB,OAAO;4CAAEA,CAAA,GAAMlE,sJAAW,CAACuE,uBAAuB,CAACzB,EAAE,CAAC;;QACtD,iDAAA;QACAuB,OAAO,EAAE,CAAC,CAACvB,EAAE;QACb0B,KAAK,EAAE,CAAC,CAAE,sCAAA;IAIZ,CAAC,CAAC;IAEF,4DAAA;IAEA,8CAAA;IACA,MAAMC,YAAY,qKAAG1E,UAAAA,AAAO;qDAAC,MAAM;YACjC,OAAOuE,gBAAgB,IAAI;gBACzBI,mBAAmB,EAAE,CAAC;gBACtBC,mBAAmB,EAAE,EAAE;gBACvBC,kBAAkB,EAAE,OAAO;gBAC3BC,mBAAmB,EAAE,OAAO;gBAC5BC,eAAe,EAAE,IAAI;gBACrBC,gBAAgB,EAAE,IAAI;gBACtBC,kBAAkB,EAAE,IAAI;gBACxBC,iBAAiB,EAAE,IAAI;gBACvBC,eAAe,EAAE,IAAI;gBACrBC,iBAAiB,EAAE,IAAI;gBACvBC,eAAe,EAAE,IAAI;gBACrBC,YAAY,EAAE,EAAA;YAChB,CAAC;QACH,CAAC;oDAAE;QAACf,gBAAgB;KAAC,CAAC;IAGtB,wEAAA;sKACAxE,YAAAA,AAAS;yCAAC,MAAM;YACd,IAAI2E,YAAY,IAAI1B,OAAO,EAAE;gBAC3B,MAAMuC,KAAK,GAAG,IAAInC,IAAI,CAAC,CAAC;gBACxB,MAAMD,SAAS,GAAGzB,wJAAAA,AAAO,EAAC6D,KAAK,EAAE,CAAC,CAAC,EAAC,8BAAA;gBACpC,MAAMlC,OAAO,iJAAG3B,UAAO,AAAPA,EAAQyB,SAAS,EAAEqC,IAAI,CAACC,GAAG,CAACf,YAAY,CAACC,mBAAmB,EAAE,CAAC,CAAC,CAAC;gBAEjFzB,YAAY,CAAC;oBACXC,SAAS;oBACTE;gBACF,CAAC,CAAC;gBAEF,4CAAA;gBACA,MAAMqC,MAAM,GAAGF,IAAI,CAACC,GAAG,CAACf,YAAY,CAACC,mBAAmB,EAAE,CAAC,CAAC;gBAC5DpB,QAAQ,CAACP,OAAO,CAAC2C,KAAK,GAAGD,MAAM,CAAC;YAClC;QACF,CAAC;wCAAE;QAAChB,YAAY;QAAE1B,OAAO;KAAC,CAAC;IAE3B,uDAAA;sKACAjD,YAAAA,AAAS;yCAAC,MAAM;YACd,IAAIiD,OAAO,IAAIC,SAAS,EAAE;gBACxB,wCAAA;gBACA,MAAM2C,UAAU,0JAAGjE,mBAAAA,AAAgB,EAACsB,SAAS,CAACI,OAAO,EAAEJ,SAAS,CAACE,SAAS,CAAC;gBAC3E,MAAM0C,QAAQ,GAAG7C,OAAO,CAAC2C,KAAK,GAAGC,UAAU;gBAC3CrC,QAAQ,CAACsC,QAAQ,CAAC;YACpB;QACF,CAAC;wCAAE;QAAC5C,SAAS;QAAED,OAAO;KAAC,CAAC;IAExB,4DAAA;IACA,MAAM8C,wBAAwB,GAAGA,CAAA,KAAM;QACrC,MAAMC,eAAe,GAAG,EAAE;QAE1B,IAAI,CAACrB,YAAY,CAACK,eAAe,EAAEgB,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAC,QAAA;QAC3D,IAAI,CAACtB,YAAY,CAACM,gBAAgB,EAAEe,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAC,SAAA;QAC5D,IAAI,CAACtB,YAAY,CAACO,kBAAkB,EAAEc,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAC,YAAA;QAC9D,IAAI,CAACtB,YAAY,CAACQ,iBAAiB,EAAEa,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAC,SAAA;QAC7D,IAAI,CAACtB,YAAY,CAACS,eAAe,EAAEY,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAC,UAAA;QAC3D,IAAI,CAACtB,YAAY,CAACU,iBAAiB,EAAEW,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAC,SAAA;QAC7D,IAAI,CAACtB,YAAY,CAACW,eAAe,EAAEU,eAAe,CAACC,IAAI,CAAC,CAAC,CAAC,EAAC,UAAA;QAE3D,OAAOD,eAAe;IACxB,CAAC;IAED,mDAAA;IACA,MAAME,cAAc,IAAIC,IAAU,IAAK;QACrC,oDAAA;QACA,MAAMC,qBAAqB,GAAGL,wBAAwB,CAAC,CAAC;QACxD,IAAIK,qBAAqB,CAACC,QAAQ,CAACF,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,EAAE;YACjD,OAAO,IAAI;QACb;QAEA,yDAAA;QACA,MAAMf,YAAY,GAAGgB,KAAK,CAACC,OAAO,CAAC7B,YAAY,CAACY,YAAY,CAAC,GACzDZ,YAAY,CAACY,YAAY,GACzB,EAAE;QAEN,4DAAA;QACA,OAAOrB,gBAAgB,EAAEuC,IAAI,EAC1BC,OAAO,IAAK;YACX,MAAMC,eAAe,GAAG,IAAItD,IAAI,CAACqD,OAAO,CAAC;YACzC,OACEP,IAAI,CAACS,WAAW,CAAC,CAAC,KAAKD,eAAe,CAACC,WAAW,CAAC,CAAC,IACpDT,IAAI,CAACU,QAAQ,CAAC,CAAC,KAAKF,eAAe,CAACE,QAAQ,CAAC,CAAC,IAC9CV,IAAI,CAACW,OAAO,CAAC,CAAC,KAAKH,eAAe,CAACG,OAAO,CAAC,CAAC;QAEhD,CACF,CAAC,IAAIvB,YAAY,CAACkB,IAAI,EACnBM,WAAgB,IAAK;YACpB,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;gBACnC,MAAMC,KAAK,GAAG,IAAI3D,IAAI,CAAC0D,WAAW,CAAC;gBACnC,OACEZ,IAAI,CAACS,WAAW,CAAC,CAAC,KAAKI,KAAK,CAACJ,WAAW,CAAC,CAAC,IAC1CT,IAAI,CAACU,QAAQ,CAAC,CAAC,KAAKG,KAAK,CAACH,QAAQ,CAAC,CAAC,IACpCV,IAAI,CAACW,OAAO,CAAC,CAAC,KAAKE,KAAK,CAACF,OAAO,CAAC,CAAC;YAEtC,CAAC,MAAM,IAAIC,WAAW,IAAIA,WAAW,CAACZ,IAAI,EAAE;gBAC1C,MAAMa,OAAK,GAAG,IAAI3D,IAAI,CAAC0D,WAAW,CAACZ,IAAI,CAAC;gBACxC,OACEA,IAAI,CAACS,WAAW,CAAC,CAAC,KAAKI,OAAK,CAACJ,WAAW,CAAC,CAAC,IAC1CT,IAAI,CAACU,QAAQ,CAAC,CAAC,KAAKG,OAAK,CAACH,QAAQ,CAAC,CAAC,IACpCV,IAAI,CAACW,OAAO,CAAC,CAAC,KAAKE,OAAK,CAACF,OAAO,CAAC,CAAC;YAEtC;YACA,OAAO,KAAK;QACd,CACF,CAAC;IACH,CAAC;IACDnD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEsC,cAAc,CAAC,IAAI7C,IAAI,CAAC,CAAC,CAAC,CAAC;IAEzD,2CAAA;IACA,MAAM4D,gBAAgB,IAAIC,KAAyC,IAAK;QACtE,sDAAA;QACA,MAAMrB,YAAU,0JAAGjE,mBAAAA,AAAgB,EAACsF,KAAK,CAAC5D,OAAO,EAAE4D,KAAK,CAAC9D,SAAS,CAAC;QAEnE,IAAIyC,YAAU,GAAGlB,YAAY,CAACC,mBAAmB,EAAE;uKACjDlD,UAAK,CAACyF,KAAK,CAAE,CAAA,gCAAA,EAAkCxC,YAAY,CAACC,mBAAoB,CAAA,KAAA,CAAM,CAAC;YACvF;QACF;QAEA,IAAIiB,YAAU,GAAGlB,YAAY,CAACE,mBAAmB,EAAE;uKACjDnD,UAAK,CAACyF,KAAK,CAAE,CAAA,4BAAA,EAA8BxC,YAAY,CAACE,mBAAoB,CAAA,KAAA,CAAM,CAAC;YACnF;QACF;QAEA1B,YAAY,CAAC+D,KAAK,CAAC;IAEnB,iFAAA;IACA,mDAAA;IACF,CAAC;IAED,2CAAA;IACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;QAC/B,IAAI,CAAClE,SAAS,EAAE;uKACdxB,UAAK,CAACyF,KAAK,CAAC,+CAA+C,CAAC;YAC5D;QACF;QAEA,qFAAA;QACA,IAAI,CAAA,CAAEjE,SAAS,CAACE,SAAS,YAAYC,IAAI,CAAC,IAAI,CAAA,CAAEH,SAAS,CAACI,OAAO,YAAYD,IAAI,CAAC,EAAE;YAClF,iDAAA;YACA,IAAI;gBACF,MAAMD,WAAS,GAAGF,SAAS,CAACE,SAAS,YAAYC,IAAI,GAAGH,SAAS,CAACE,SAAS,GAAG,IAAIC,IAAI,CAACH,SAAS,CAACE,SAAS,CAAC;gBAC3G,MAAME,SAAO,GAAGJ,SAAS,CAACI,OAAO,YAAYD,IAAI,GAAGH,SAAS,CAACI,OAAO,GAAG,IAAID,IAAI,CAACH,SAAS,CAACI,OAAO,CAAC;gBAEnG,sDAAA;gBACA,MAAMuC,YAAU,IAAGjE,yKAAAA,AAAgB,EAAC0B,SAAO,EAAEF,WAAS,CAAC;gBAEvD,IAAIyC,YAAU,GAAGlB,YAAY,CAACC,mBAAmB,EAAE;+KACjDlD,UAAK,CAACyF,KAAK,CAAE,CAAA,gCAAA,EAAkCxC,YAAY,CAACC,mBAAoB,CAAA,KAAA,CAAM,CAAC;oBACvF;gBACF;gBAEA,IAAIiB,YAAU,GAAGlB,YAAY,CAACE,mBAAmB,EAAE;+KACjDnD,UAAK,CAACyF,KAAK,CAAE,CAAA,4BAAA,EAA8BxC,YAAY,CAACE,mBAAoB,CAAA,KAAA,CAAM,CAAC;oBACnF;gBACF;gBAEA,uCAAA;gBACA,IAAI,CAACnB,IAAI,EAAE;2KACTtC,YAAAA,AAAS,EAAC,iBAAiB,EAAG,CAAA,SAAA,EAAW4B,EAAG,CAAA,MAAA,EAAQI,WAAS,CAACiE,WAAW,CAAC,CAAE,CAAA,IAAA,EAAM/D,SAAO,CAAC+D,WAAW,CAAC,CAAE,EAAC,CAAC;oBAC1GrD,WAAW,CAAC,OAAO,CAAC;oBACpBF,iBAAiB,CAAC,IAAI,CAAC;oBACvB;gBACF;gBAEAL,MAAM,CAACwC,IAAI,CAAE,CAAA,SAAA,EAAWjD,EAAG,CAAA,MAAA,EAAQI,WAAS,CAACiE,WAAW,CAAC,CAAE,CAAA,IAAA,EAAM/D,SAAO,CAAC+D,WAAW,CAAC,CAAE,EAAC,CAAC;YAC3F,CAAC,CAAC,OAAOF,KAAK,EAAE;gBACdxD,OAAO,CAACwD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;2KACrDzF,UAAK,CAACyF,KAAK,CAAC,4EAA4E,CAAC;gBACzF;YACF;QACF,CAAC,MAAM;YACL,sDAAA;YACA,MAAMtB,YAAU,0JAAGjE,mBAAAA,AAAgB,EAACsB,SAAS,CAACI,OAAO,EAAEJ,SAAS,CAACE,SAAS,CAAC;YAE3E,IAAIyC,YAAU,GAAGlB,YAAY,CAACC,mBAAmB,EAAE;2KACjDlD,UAAK,CAACyF,KAAK,CAAE,CAAA,gCAAA,EAAkCxC,YAAY,CAACC,mBAAoB,CAAA,KAAA,CAAM,CAAC;gBACvF;YACF;YAEA,IAAIiB,YAAU,GAAGlB,YAAY,CAACE,mBAAmB,EAAE;2KACjDnD,UAAK,CAACyF,KAAK,CAAE,CAAA,4BAAA,EAA8BxC,YAAY,CAACE,mBAAoB,CAAA,KAAA,CAAM,CAAC;gBACnF;YACF;YAEA,2CAAA;YACA,IAAI,CAACnB,IAAI,EAAE;gBACT,uDAAA;gBACAtC,mKAAAA,AAAS,EAAC,iBAAiB,EAAG,CAAA,SAAA,EAAW4B,EAAG,CAAA,MAAA,EAAQE,SAAS,CAACE,SAAS,CAACiE,WAAW,CAAC,CAAE,CAAA,IAAA,EAAMnE,SAAS,CAACI,OAAO,CAAC+D,WAAW,CAAC,CAAE,EAAC,CAAC;gBAC9HrD,WAAW,CAAC,OAAO,CAAC;gBACpBF,iBAAiB,CAAC,IAAI,CAAC;gBACvB;YACF;YAEA,wDAAA;YACAL,MAAM,CAACwC,IAAI,CAAE,CAAA,SAAA,EAAWjD,EAAG,CAAA,MAAA,EAAQE,SAAS,CAACE,SAAS,CAACiE,WAAW,CAAC,CAAE,CAAA,IAAA,EAAMnE,SAAS,CAACI,OAAO,CAAC+D,WAAW,CAAC,CAAE,EAAC,CAAC;QAC/G;IACF,CAAC;IAED,8CAAA;IACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;QAC3BtD,WAAW,CAACD,QAAQ,KAAK,OAAO,GAAG,UAAU,GAAG,OAAO,CAAC;IAC1D,CAAC;IAED,gEAAA;IACA,MAAMwD,iBAAiB,GAAGA,CAAA,KAAM;QAC9BzD,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,sCAAA;IACA,MAAM0D,SAAS,IAAIrB,MAAY,IAAK;QAClC,qFAAA;QACA,yLAAOpF,YAAQ,CAAC0G,OAAO,CAACtB,MAAI,CAAC,CAACuB,QAAQ,CAAC,wBAAwB,EAAE;YAAEC,MAAM,EAAE;QAAK,CAAC,CAAC;IACpF,CAAC;IAGD,gCAAA;IACA,MAAMC,MAAM,GAAGrB,KAAK,CAACC,OAAO,CAACvD,OAAO,CAAC2E,MAAM,CAAC,GAAG3E,OAAO,CAAC2E,MAAM,GAAG,EAAE;IAElE,sCAAA;IACA,mDAAA;IACA,kEAAA;IACA,MAAM5F,YAAY,GAAGW,MAAM,CAAC,IAAI,CAAC,CAACM,OAAO,CAACjB,YAAY,CAAC,IAAI,EAAE;IAC7D2B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEX,OAAO,CAAC;IAC/B,MAAMd,OAAO,GAAGc,OAAO,CAAC4E,QAAQ,EAAE1F,OAAO,IAAI,CAAC;IAC9C,MAAMI,QAAQ,GAAGU,OAAO,CAAC4E,QAAQ,EAAEtF,QAAQ,IAAI,EAAE;IACjD,MAAMuF,IAAI,GAAG7E,OAAO,CAAC6E,IAAI,IAAI,EAAE;IAE/B,8DAAA;IACA,2DAAA;IACA,8FAAA;IACA,8EAAA;IAEA,qCAAA;IACA,mCAAA;IACA,mEAAA;IACA,sCAAA;IAEAnE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEe,YAAY,CAAC;IAEzC,qBACE,6LAAC,GAAG;QAAC,SAAS,EAAC,wBAAwB,CAAC;;0BAEtC,6LAAC,GAAG;gBAAC,SAAS,EAAC,8CAA8C,CAAC;;kCAE5D,6LAAC,0KAAI;wBAAC,IAAI,EAAC,WAAW;wBAAC,SAAS,EAAC,iBAAiB;kCAAC;;;;;;kCAGnD,uZAAC,eAAY;wBAAC,SAAS,EAAC,cAAc,GAAG;;;;;;kCACzC,6LAAC,IAAI,CAAC;kCAAC1B,OAAO,CAAC8E,IAAI,CAAC,EAAE,IAAI,CAAC;;;;;;kCAC3B,sZAAC,gBAAY;wBAAC,SAAS,EAAC,cAAc,GAAG;;;;;;kCACzC,6LAAC,IAAI,CAAC;kCAAC9E,OAAO,CAAC+E,KAAK,CAAC,EAAE,IAAI,CAAC;;;;;;kCAC5B,uZAAC,eAAY;wBAAC,SAAS,EAAC,cAAc,GAAG;;;;;;kCACzC,6LAAC,IAAI,CAAC;kCAACF,IAAI,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;;;0BAGrB,6LAAC,GAAG;gBAAC,SAAS,EAAC,uCAAuC,CAAC;;kCAErD,6LAAC,GAAG;wBAAC,SAAS,EAAC,yBAAyB,CAAC;;0CAEvC,qVAAC,iBAAc;gCACb,OAAO,CAAC,CAAC7E,OAAO,CAAC;gCACjB,MAAM,CAAC,CAAC2E,MAAM;;;;;;0CAMhB,6LAAC,GAAG;gCAAC,SAAS,EAAC,kCAAkC,CAAC;;kDAChD,6LAAC,GAAG;wCAAC,SAAS,EAAC,QAAQ,CAAC;;0DACtB,6LAAC,GAAG;gDAAC,SAAS,EAAC,yBAAyB,CAAC;;kEACvC,6LAAC,EAAE;wDAAC,SAAS,EAAC,oBAAoB,CAAC;;4DAChC3E,OAAO,CAAC8E,IAAI;4DAAC,CAAC;4DAAC9E,OAAO,CAAC+E,KAAK;4DAAC,CAAC;4DAAChG,YAAY;4DAAC,CAAC;4DAAC8F,IAAI,CAAC;;;;;;;kEAGtD,qVAAC,UAAc;wDACb,SAAS,CAAC,CAAC7E,OAAO,CAACD,EAAE,CAAC;wDACtB,IAAI,EAAC,IAAI;wDACT,OAAO,EAAC,SAAS;wDACjB,SAAS,EAAC,eAAe,GACzB;;;;;;;;;;;;0DAEJ,6LAAC,GAAG;gDAAC,SAAS,EAAC,wBAAwB,CAAC;;kEACtC,6LAAC,IAAI;wDAAC,SAAS,EAAC,eAAe,CAAC;;4DAACb,OAAO,CAAC8F,cAAc,CAAC,CAAC;4DAAC,GAAG,EAAE,IAAI,CAAC;;;;;;;kEACpE,6LAAC,IAAI;wDAAC,SAAS,EAAC,MAAM;kEAAC,CAAC,EAAE,IAAI,CAAC;;;;;;kEAC/B,6LAAC,IAAI;wDAAC,SAAS,EAAC,eAAe,CAAC;kEAAC1F,QAAQ,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;kDAGrD,6LAAC,GAAG;wCAAC,SAAS,EAAC,YAAY,CAAC;;0DAC1B,6LAAC,GAAG;gDAAC,SAAS,EAAC,uBAAuB;0DAAC,YAAY,EAAE,GAAG,CAAC;;;;;;0DACzD,6LAAC,GAAG;gDAAC,SAAS,EAAC,oBAAoB;;oDAAC,CAAC;oDAACU,OAAO,CAAC2C,KAAK,CAACqC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;;;;;;;;;;;;;;;;;;;0CAK/E,6LAAC,GAAG;gCAAC,SAAS,EAAC,MAAM,CAAC;;kDACpB,6LAAC,EAAE;wCAAC,SAAS,EAAC,wBAAwB;kDAAC,sBAAsB,EAAE,EAAE,CAAC;;;;;;kDAClE,6LAAC,GAAG;wCAAC,SAAS,EAAC,wBAAwB,CAAC;kDACrChF,OAAO,CAAC4E,QAAQ,IAAIK,MAAM,CAACC,OAAO,CAAClF,OAAO,CAAC4E,QAAQ,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,iBACjF,6LAAC,GAAG,CAAC,GAAG,CAAC;gDAAM,SAAS,EAAC,2BAA2B,CAAC;;kEAEnD,6LAAC,GAAG;wDAAC,SAAS,EAAC,kCAAkC,CAAC;kEAACzG,aAAa,CAAC,IAAI,CAAC,CAACwG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;;;;;;kEAClF,6LAAC,GAAG;wDAAC,SAAS,EAAC,aAAa,CAAC;kEAACE,MAAM,CAACD,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;;;;;;;+CAH1CD,GAAG,CAAC;;;;;;;;;;;;;;;;0CAUpB,6LAAC,GAAG;gCAAC,SAAS,EAAC,MAAM,CAAC;;kDACpB,6LAAC,EAAE;wCAAC,SAAS,EAAC,wBAAwB;kDAAC,2BAA2B,EAAE,EAAE,CAAC;;;;;;kDAEvE,iUAAC,OAAI;wCAAC,YAAY,EAAC,SAAS,CAAC;;0DAC3B,iUAAC,WAAQ;gDAAC,SAAS,EAAC,sBAAsB,CAAC;;kEACzC,6LAAC,kJAAW;wDAAC,KAAK,EAAC,SAAS;kEAAC,OAAO,EAAE,WAAW,CAAC;;;;;;kEAClD,iUAAC,cAAW;wDAAC,KAAK,EAAC,UAAU;kEAAC,QAAQ,EAAE,WAAW,CAAC;;;;;;kEACpD,iUAAC,cAAW;wDAAC,KAAK,EAAC,cAAc;kEAAC,YAAY,EAAE,WAAW,CAAC;;;;;;kEAC5D,gUAAC,eAAW;wDAAC,KAAK,EAAC,WAAW;kEAAC,SAAS,EAAE,WAAW,CAAC;;;;;;kEACtD,iUAAC,cAAW;wDAAC,KAAK,EAAC,UAAU;kEAAC,QAAQ,EAAE,WAAW,CAAC;;;;;;;;;;;;0DAGtD,iUAAC,cAAW;gDAAC,KAAK,EAAC,SAAS;gDAAC,SAAS,EAAC,WAAW,CAAC;wEACjD,6LAAC,GAAG;oDAAC,SAAS,EAAC,uCAAuC,CAAC;8DACpDrF,OAAO,CAAC4E,QAAQ,IAAIK,MAAM,CAACC,OAAO,CAAClF,OAAO,CAAC4E,QAAQ,CAAC,CAACQ,GAAG,CAAC,CAAC,CAACC,KAAG,EAAEC,OAAK,CAAC,KAAK;wDAC1E,gDAAA;wDACA,MAAME,KAAK,GAAG3G,aAAa,CAAC,IAAI,CAAC,CAACwG,KAAG,CAAC,IAAIA,KAAG;wDAE7C,qBACE,6LAAC,GAAG,CAAC,GAAG,CAAC;4DAAM,SAAS,EAAC,2BAA2B,CAAC;;8EACnD,6LAAC,GAAG;oEAAC,SAAS,EAAC,uCAAuC,CAAC;8EAACG,KAAK,CAAC,EAAE,GAAG,CAAC;;;;;;8EACpE,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa,CAAC;8EAC1B,OAAOF,OAAK,KAAK,QAAQ,IAAIA,OAAK,CAACG,MAAM,GAAG,EAAE,GAC1C,GAAEH,OAAK,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAE,CAAA,GAAA,CAAI,GAC9BH,MAAM,CAACD,OAAK,CAAC,CAAC;;;;;;;2DALZD,KAAG,CAAC;;;;;oDASlB,CAAC,CAAC,CAAC;;;;;;;;;;;0DAIP,iUAAC,cAAW;gDAAC,KAAK,EAAC,UAAU;gDAAC,SAAS,EAAC,WAAW,CAAC;0DAClD,2MAAC,GAAG;oDAAC,SAAS,EAAC,uCAAuC,CAAC;;sEACrD,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,kBAAkB,EAAE,GAAG,CAAC;;;;;;8EACpE,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,GAAG,EAAE,GAAG,CAAC;;;;;;;;;;;;sEAExC,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,KAAK,EAAE,GAAG,CAAC;;;;;;8EACvD,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa,CAAC;8EAACrF,OAAO,CAAC2F,KAAK,CAAC,EAAE,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;0DAKxD,iUAAC,cAAW;gDAAC,KAAK,EAAC,cAAc;gDAAC,SAAS,EAAC,WAAW,CAAC;wEACtD,6LAAC,GAAG;oDAAC,SAAS,EAAC,uCAAuC,CAAC;;sEACrD,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,kBAAkB,EAAE,GAAG,CAAC;;;;;;8EACpE,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,EAAE,EAAE,GAAG,CAAC;;;;;;;;;;;;sEAEvC,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,SAAS,EAAE,GAAG,CAAC;;;;;;8EAC3D,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,EAAE,EAAE,GAAG,CAAC;;;;;;;;;;;;sEAEvC,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,cAAc,EAAE,GAAG,CAAC;;;;;;8EAChE,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,EAAE,EAAE,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;0DAK3C,iUAAC,cAAW;gDAAC,KAAK,EAAC,WAAW;gDAAC,SAAS,EAAC,WAAW,CAAC;wEACnD,6LAAC,GAAG;oDAAC,SAAS,EAAC,uCAAuC,CAAC;;sEACrD,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,OAAO,EAAE,GAAG,CAAC;;;;;;8EACzD,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,CAAC,EAAE,GAAG,CAAC;;;;;;;;;;;;sEAEtC,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,sBAAsB,EAAE,GAAG,CAAC;;;;;;8EACxE,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,EAAE,EAAE,GAAG,CAAC;;;;;;;;;;;;sEAEvC,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,UAAU,EAAE,GAAG,CAAC;;;;;;8EAC5D,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,EAAE,EAAE,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;0DAK3C,iUAAC,cAAW;gDAAC,KAAK,EAAC,UAAU;gDAAC,SAAS,EAAC,WAAW,CAAC;wEAClD,6LAAC,GAAG;oDAAC,SAAS,EAAC,uCAAuC,CAAC;;sEACrD,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,SAAS,EAAE,GAAG,CAAC;;;;;;8EAC3D,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,IAAI,EAAE,GAAG,CAAC;;;;;;;;;;;;sEAEzC,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,mBAAmB,EAAE,GAAG,CAAC;;;;;;8EACrE,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,EAAE,EAAE,GAAG,CAAC;;;;;;;;;;;;sEAEvC,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,4BAA4B;8EAAC,iBAAiB,EAAE,GAAG,CAAC;;;;;;8EACnE,6LAAC,GAAG;oEAAC,SAAS,EAAC,aAAa;8EAAC,EAAE,EAAE,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjD,6LAAC,GAAG,CAAC;gDACH,6LAAC,GAAG;4BAAC,SAAS,EAAC,wBAAwB,CAAC;;8CAEtC,iUAAC,OAAI;oCAAC,SAAS,EAAC,EAAE,CAAC;8CACjB,2MAAC,kJAAW;wCAAC,SAAS,EAAC,KAAK,CAAC;gEAC3B,6LAAC,GAAG;4CAAC,SAAS,EAAC,WAAW,CAAC;;8DACzB,6LAAC,GAAG,CAAC;4EACH,6LAAC,EAAE;wDAAC,SAAS,EAAC,oBAAoB;;4DAAC,CAAC;4DAAC3F,OAAO,CAAC2C,KAAK,CAACqC,cAAc,CAAC,CAAC;4DAAC;0EAAC,6LAAC,IAAI;gEAAC,SAAS,EAAC,uBAAuB;0EAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;;;;;;;;;;;;;;;;;8DAGhI,6LAAC,qJAAS,GAAG;;;;;8DAEb,6LAAC,GAAG;oDAAC,SAAS,EAAC,MAAM,CAAC;;sEACpB,6LAAC,GAAG;4DAAC,SAAS,EAAC,MAAM,CAAC;;8EACpB,6LAAC,KAAK;oEAAC,SAAS,EAAC,gCAAgC;8EAAC,qBAAqB,EAAE,KAAK,CAAC;;;;;;8EAC/E,yVAAC,UAAc;oEACb,gBAAgB,CAAC,CAAC/D,gBAAgB,CAAC;oEACnC,QAAQ,CAAC,CAAC+C,gBAAgB,CAAC;oEAC3B,gBAAgB,CAAC,CAAC/D,SAAS,CAAC;oEAC5B,mBAAmB,CAAC,CAACyB,YAAY,CAACC,mBAAmB,CAAC;oEACtD,mBAAmB,CAAC,CAACD,YAAY,CAACE,mBAAmB,CAAC,GACtD;;;;;;;;;;;;sEAKJ,6LAAC,GAAG;4DAAC,SAAS,EAAC,6CAA6C,CAAC;;8EAC3D,6LAAC,IAAI;8EAAC,KAAK,EAAE,IAAI,CAAC;;;;;;8EAClB,6LAAC,IAAI;oEAAC,SAAS,EAAC,SAAS;;wEAAC,CAAC;wEAACtB,KAAK,CAAC0E,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;;;;sEAI5D,6LAAC,GAAG;4DAAC,SAAS,EAAC,sCAAsC,CAAC;;8EACpD,6LAAC,GAAG;oEAAC,SAAS,EAAC,sBAAsB,CAAC;;sFACpC,6LAAC,IAAI;sFAAC,eAAe,EAAE,IAAI,CAAC;;;;;;sFAC5B,6LAAC,IAAI;;gFAAC,CAAC;gFAAChF,OAAO,CAAC2C,KAAK,CAACqC,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;;;;gEAE/C/E,SAAS,kBACR,6LAAC,GAAG;oEAAC,SAAS,EAAC,sBAAsB,CAAC;;sFACpC,6LAAC,IAAI;sFAAC,qBAAqB,EAAE,IAAI,CAAC;;;;;;sFAClC,6LAAC,IAAI,CAAC;6OAACtB,mBAAAA,AAAgB,EAACsB,SAAS,CAACI,OAAO,EAAEJ,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;;;8EAG3E,6LAAC,GAAG;oEAAC,SAAS,EAAC,sBAAsB,CAAC;;sFACpC,6LAAC,IAAI;sFAAC,MAAM,EAAE,IAAI,CAAC;;;;;;sFACnB,6LAAC,IAAI;;gFAAC,CAAC;gFAACG,KAAK,CAAC0E,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;;;;8EAExC,6LAAC,GAAG;oEAAC,SAAS,EAAC,sBAAsB,CAAC;;sFACpC,6LAAC,IAAI;sFAAC,2BAA2B,EAAE,IAAI,CAAC;;;;;;sFACxC,6LAAC,IAAI,CAAC;sFAACtD,YAAY,CAACC,mBAAmB,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;;;8EAEjD,6LAAC,GAAG;oEAAC,SAAS,EAAC,sBAAsB,CAAC;;sFACpC,6LAAC,IAAI;sFAAC,2BAA2B,EAAE,IAAI,CAAC;;;;;;sFACxC,6LAAC,IAAI,CAAC;sFAACD,YAAY,CAACE,mBAAmB,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8DAMrD,mUAAC,SAAM;oDACL,SAAS,EAAC,wCAAwC;oDAClD,OAAO,CAAC,CAACuC,kBAAkB,CAAC,CAC7B;4EACC,6LAAC,IAAI;wDAAC,SAAS,EAAC,aAAa;kEAAC,QAAQ,EAAE,IAAI,CAAC;;;;;;;;;;;8DAG/C,6LAAC,CAAC;oDAAC,SAAS,EAAC,2CAA2C;8DAAC;;;;;;8DAIzD,6LAAC,GAAG;oDAAC,SAAS,EAAC,gBAAgB,CAAC;;sEAC9B,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,6BAA6B;oEAAC,IAAI,EAAC,MAAM;oEAAC,OAAO,EAAC,WAAW;oEAAC,MAAM,EAAC,cAAc,CAAC;4FACjG,6LAAC,IAAI;wEAAC,aAAa,EAAC,OAAO;wEAAC,cAAc,EAAC,OAAO;wEAAC,WAAW,CAAC,CAAC,CAAC,CAAC;wEAAC,CAAC,EAAC,gBAAgB,GAAG;;;;;;;;;;;8EAE1F,6LAAC,IAAI;8EAAC,2BAA2B,EAAE,IAAI,CAAC;;;;;;;;;;;;sEAG1C,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B,CAAC;;8EACzC,6LAAC,GAAG;oEAAC,SAAS,EAAC,6BAA6B;oEAAC,IAAI,EAAC,MAAM;oEAAC,OAAO,EAAC,WAAW;oEAAC,MAAM,EAAC,cAAc,CAAC;4FACjG,6LAAC,IAAI;wEAAC,aAAa,EAAC,OAAO;wEAAC,cAAc,EAAC,OAAO;wEAAC,WAAW,CAAC,CAAC,CAAC,CAAC;wEAAC,CAAC,EAAC,gBAAgB,GAAG;;;;;;;;;;;8EAE1F,6LAAC,IAAI;8EAAC,0BAA0B,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOjD,6LAAC,GAAG;oCAAC,SAAS,EAAC,uCAAuC,CAAC;;sDACrD,6LAAC,GAAG;4CAAC,SAAS,EAAC,8BAA8B,CAAC;;8DAC5C,6LAAC,GAAG;oDAAC,SAAS,EAAC,6DAA6D,CAAC;4EAC3E,8TAAC,UAAK;wDACJ,GAAG,CAAC,CAACnE,OAAO,CAAC4F,IAAI,CAACC,KAAK,IAAI,qCAAqC,CAAC;wDACjE,GAAG,EAAC,MAAM;wDACV,IAAI;wDACJ,SAAS,EAAC,cAAc,GACxB;;;;;;;;;;;8DAEJ,6LAAC,GAAG,CAAC;;sEACH,6LAAC,EAAE;4DAAC,SAAS,EAAC,aAAa,CAAC;sEAAC7F,OAAO,CAAC4F,IAAI,CAACE,IAAI,CAAC,EAAE,EAAE,CAAC;;;;;;sEACpD,6LAAC,CAAC;4DAAC,SAAS,EAAC,uBAAuB;;gEAAC,cAAc;gEAACvB,SAAS,CAACvE,OAAO,CAAC4F,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;;;;;;;;;;;;;;;;;;;sDAI9F,mUAAC,SAAM;4CAAC,OAAO,EAAC,SAAS;4CAAC,SAAS,EAAC,QAAQ,CAAC;oEAC3C,6LAAC,IAAI;gDAAC,SAAS,EAAC,aAAa;0DAAC,YAAY,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,mUAAC,SAAM;gBAAC,IAAI,CAAC,CAACnF,cAAc,CAAC;gBAAC,YAAY,CAAC,CAACC,iBAAiB,CAAC,CAAE;wCAC9D,mUAAC,gBAAa;oBAAC,SAAS,EAAC,cAAc,CAAC;;sCACxC,6LAAC,oJAAW;4BAAC,SAAS,EAAC,SAAS,CAAC;sCAC9BC,QAAQ,KAAK,OAAO,GAAG,8BAA8B,GAAG,gCAAgC,CAAC;;;;;;sCAE1F,6LAAC,GAAG;4BAAC,SAAS,EAAC,MAAM,CAAC;sCACnBA,QAAQ,KAAK,OAAO,iBACnB,4UAAC,YAAS;gCACR,SAAS,CAAC,CAACwD,iBAAiB,CAAC;gCAC7B,eAAe,CAAC,CAACD,cAAc,CAAC;gCAChC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GACzB;;;;;qDAEF,+UAAC,eAAY;gCACX,SAAS,CAAC,CAACC,iBAAiB,CAAC;gCAC7B,YAAY,CAAC,CAACD,cAAc,CAAC;gCAC7B,qBAAqB,CAAC,CAAC,IAAI,CAAC,GAE/B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;;;0JA5kB0B,CAAC,CAAC;8IACTpG;uLAgBkBD,WAAQ;uLAURA,WAAQ", "debugId": null}}]}