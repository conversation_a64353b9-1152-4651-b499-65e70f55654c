{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/C%3A/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": ["c", "_c", "React", "AvatarPrimitive", "cn", "Avatar", "t0", "$", "$i", "Symbol", "for", "className", "props", "t1", "t2", "AvatarImage", "AvatarFallback"], "mappings": ";;;;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAGZ,OAAO,KAAKE,eAAe,MAAM,wBAAwB;AAEzD,SAASC,EAAE,QAAQ,aAAa;AALhC,YAAY;;;;;AAOZ,gBAAAE,EAAA;IAAA,MAAAC,CAAA,mLAAAN,IAAAA,AAAA,EAAA;IAAA,IAAAM,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAI,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAD,EAAA,EAAA;QAAgB,CAAA,EAAAK,SAAA,EAAA,GAAAC,OAAA,GAAAN,EAGoC;QAAAC,CAAA,CAAA,EAAA,GAAAD,EAAA;QAAAC,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAJ,CAAA,CAAA,EAAA;QAAAK,KAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAI,SAAA,EAAA;QAInCE,EAAA,8HAAAT,KAAAA,AAAA,EACT,4DAA4D,EAC5DO,SACF,CAAC;QAAAJ,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,KAAA,IAAAL,CAAA,CAAA,EAAA,KAAAM,EAAA,EAAA;QALHC,EAAA,iBAAA,6LAAA,qKAAA,CAAA,OAAA;YACY,SAAQ,IAAR,QAAQ;YACP,SAGV,CAHU,CAAAD,EAGV;YAAA,GACGD,KAAK,IACT;;;;;;QAAAL,CAAA,CAAA,EAAA,GAAAK,KAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,EAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,OAPFO,EAOE;AAAA;KAZNT;AAgBA,qBAAAC,EAAA;IAAA,MAAAC,CAAA,GAAAN,oLAAAA,AAAA,EAAA;IAAA,IAAAM,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAI,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAD,EAAA,EAAA;QAAqB,CAAA,EAAAK,SAAA,EAAA,GAAAC,OAAA,GAAAN,EAGgC;QAAAC,CAAA,CAAA,EAAA,GAAAD,EAAA;QAAAC,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAJ,CAAA,CAAA,EAAA;QAAAK,KAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAI,SAAA,EAAA;QAIpCE,EAAA,8HAAAT,KAAAA,AAAA,EAAG,yBAAyB,EAAEO,SAAS,CAAC;QAAAJ,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,KAAA,IAAAL,CAAA,CAAA,EAAA,KAAAM,EAAA,EAAA;QAFrDC,EAAA,iBAAA,6LAAA,qKAAA,CAAA,QAAA;YACY,SAAc,IAAd,cAAc;YACb,SAAwC,CAAxC,CAAAD,EAAwC;YAAA,GAC/CD,KAAK,IACT;;;;;;QAAAL,CAAA,CAAA,EAAA,GAAAK,KAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,EAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,OAJFO,EAIE;AAAA;MATNC;AAaA,wBAAAT,EAAA;IAAA,MAAAC,CAAA,mLAAAN,IAAAA,AAAA,EAAA;IAAA,IAAAM,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAI,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAD,EAAA,EAAA;QAAwB,CAAA,EAAAK,SAAA,EAAA,GAAAC,OAAA,GAAAN,EAGgC;QAAAC,CAAA,CAAA,EAAA,GAAAD,EAAA;QAAAC,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAJ,CAAA,CAAA,EAAA;QAAAK,KAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAI,SAAA,EAAA;QAIvCE,EAAA,GAAAT,gIAAAA,AAAA,EACT,kEAAkE,EAClEO,SACF,CAAC;QAAAJ,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,KAAA,IAAAL,CAAA,CAAA,EAAA,KAAAM,EAAA,EAAA;QALHC,EAAA,iBAAA,6LAAA,qKAAA,CAAA,WAAA;YACY,SAAiB,IAAjB,iBAAiB;YAChB,SAGV,CAHU,CAAAD,EAGV;YAAA,GACGD,KAAK,IACT;;;;;;QAAAL,CAAA,CAAA,EAAA,GAAAK,KAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,EAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,OAPFO,EAOE;AAAA;MAZNE", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/node_modules/%40radix-ui/react-avatar/src/avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ElementRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps.referrerPolicy);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ElementRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useImageLoadingStatus(src?: string, referrerPolicy?: React.HTMLAttributeReferrerPolicy) {\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n\n  useLayoutEffect(() => {\n    if (!src) {\n      setLoadingStatus('error');\n      return;\n    }\n\n    let isMounted = true;\n    const image = new window.Image();\n\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      if (!isMounted) return;\n      setLoadingStatus(status);\n    };\n\n    setLoadingStatus('loading');\n    image.onload = updateStatus('loaded');\n    image.onerror = updateStatus('error');\n    image.src = src;\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n\n    return () => {\n      isMounted = false;\n    };\n  }, [src, referrerPolicy]);\n\n  return loadingStatus;\n}\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAoClB;;;;;;;;AA5BR,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAM9F,IAAM,2KAAe,aAAA,EACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EAAE,aAAA,EAAe,GAAG,YAAY,CAAA,GAAI;IAC1C,MAAM,CAAC,oBAAoB,qBAAqB,CAAA,qKAAU,WAAA,EAA6B,MAAM;IAC7F,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA,4BAA4B;QAE5B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;YAAgB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA;AAG1D;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,gLAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAA,EAAK,wBAAwB,KAAO,CAAD,AAAC,EAAG,GAAG,WAAW,CAAA,GAAI;IAChF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,qBAAqB,sBAAsB,KAAK,WAAW,cAAc;IAC/E,MAAM,sNAA4B,iBAAA;iEAAe,CAAC,WAA+B;YAC/E,sBAAsB,MAAM;YAC5B,QAAQ,0BAAA,CAA2B,MAAM;QAC3C,CAAC;;IAED,CAAA,GAAA,sLAAA,CAAA,kBAAA;uCAAgB,MAAM;YACpB,IAAI,uBAAuB,QAAQ;gBACjC,0BAA0B,kBAAkB;YAC9C;QACF;sCAAG;QAAC;QAAoB,yBAAyB;KAAC;IAElD,OAAO,uBAAuB,WAC5B,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;QAAc;IAAA,CAAU,IAC1D;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,mLAAuB,aAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,OAAA,EAAS,GAAG,cAAc,CAAA,GAAI;IACrD,MAAM,UAAU,iBAAiB,eAAe,aAAa;IAC7D,MAAM,CAAC,WAAW,YAAY,CAAA,qKAAU,WAAA,EAAS,YAAY,KAAA,CAAS;QAEhE,0KAAA;oCAAU,MAAM;YACpB,IAAI,YAAY,KAAA,GAAW;gBACzB,MAAM,UAAU,OAAO,UAAA;wDAAW,IAAM,aAAa,IAAI;uDAAG,OAAO;gBACnE;gDAAO,IAAM,OAAO,YAAA,CAAa,OAAO;;YAC1C;QACF;mCAAG;QAAC,OAAO;KAAC;IAEZ,OAAO,aAAa,QAAQ,kBAAA,KAAuB,WACjD,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;QAAgB,GAAG,aAAA;QAAe,KAAK;IAAA,CAAc,IACpD;AACN;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,sBAAsB,GAAA,EAAc,cAAA,EAAoD;IAC/F,MAAM,CAAC,eAAe,gBAAgB,CAAA,IAAU,4KAAA,EAA6B,MAAM;IAEnF,CAAA,GAAA,sLAAA,CAAA,kBAAA;iDAAgB,MAAM;YACpB,IAAI,CAAC,KAAK;gBACR,iBAAiB,OAAO;gBACxB;YACF;YAEA,IAAI,YAAY;YAChB,MAAM,QAAQ,IAAI,OAAO,KAAA,CAAM;YAE/B,MAAM;sEAAe,CAAC;8EAA+B,MAAM;4BACzD,IAAI,CAAC,UAAW,CAAA;4BAChB,iBAAiB,MAAM;wBACzB;;;YAEA,iBAAiB,SAAS;YAC1B,MAAM,MAAA,GAAS,aAAa,QAAQ;YACpC,MAAM,OAAA,GAAU,aAAa,OAAO;YACpC,MAAM,GAAA,GAAM;YACZ,IAAI,gBAAgB;gBAClB,MAAM,cAAA,GAAiB;YACzB;YAEA;yDAAO,MAAM;oBACX,YAAY;gBACd;;QACF;gDAAG;QAAC;QAAK,cAAc;KAAC;IAExB,OAAO;AACT;AACA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW", "ignoreList": [0], "debugId": null}}]}