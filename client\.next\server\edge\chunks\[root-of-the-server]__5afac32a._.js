(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__5afac32a._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/constants/index.ts [app-edge-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BEARER_COOKIE_NAME": (()=>BEARER_COOKIE_NAME),
    "PENDING_INVITATION_COOKIE": (()=>PENDING_INVITATION_COOKIE),
    "PENDING_INVITATION_EMAIL_COOKIE": (()=>PENDING_INVITATION_EMAIL_COOKIE),
    "PENDING_INVITATION_ORG_ID_COOKIE": (()=>PENDING_INVITATION_ORG_ID_COOKIE)
});
const BEARER_COOKIE_NAME = "bearer_token";
const PENDING_INVITATION_COOKIE = 'pending_invitation';
const PENDING_INVITATION_EMAIL_COOKIE = 'pending_invitation_email';
const PENDING_INVITATION_ORG_ID_COOKIE = 'pending_invitation_org_id';
}}),
"[project]/src/app/api/set-private-token/route.ts [app-edge-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST),
    "runtime": (()=>runtime)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/index.ts [app-edge-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [app-edge-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [app-edge-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$headers$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/headers.js [app-edge-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/request/cookies.js [app-edge-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$server$2f$index$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cookies-next/lib/server/index.js [app-edge-route] (ecmascript)");
;
;
;
;
const runtime = 'edge';
async function POST(request) {
    // console.log('request: ', request);
    const body = await request.json();
    console.log('body: ', body);
    const url = body.url;
    const token = body.token;
    if (token) {
        // const res = new Response();
        // res.headers.set('Set-Cookie', `better-auth.session_token=${token}; Path=/; HttpOnly; Secure; SameSite=None`);
        // // set the BEARER_COOKIE_NAME
        // res.headers.set('Set-Cookie', `bearer_token=${token}; Path=/; Secure; SameSite=None`);
        // return res;
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$server$2f$index$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["setCookie"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["BEARER_COOKIE_NAME"], token, {
            cookies: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["cookies"],
            httpOnly: false,
            secure: true,
            sameSite: 'none',
            path: '/'
        });
        const newUrlWithUrlQuery = new URL(url);
        // newUrlWithUrlQuery.searchParams.set('url', url);
        newUrlWithUrlQuery.searchParams.set('token', token);
        newUrlWithUrlQuery.searchParams.set('tcl', token);
        const redirectUrl = newUrlWithUrlQuery.toString();
        console.log('redirectUrl: ', redirectUrl);
        // return NextResponse.redirect(redirectUrl, {
        //   headers: {
        //     'Set-Cookie': `${BEARER_COOKIE_NAME}=${token}`
        //   }
        // });
        // const enviorments = process.env;
        // console.log('enviorments: ', enviorments);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$server$2f$index$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["setCookie"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["BEARER_COOKIE_NAME"], token, {
            cookies: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["cookies"],
            httpOnly: false,
            secure: true,
            sameSite: 'none',
            path: '/'
        });
        console.log('BEARER_COOKIE_NAME: ', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["BEARER_COOKIE_NAME"]);
        // return NextResponse.json({ message: 'Token set' });
        // tcl
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(redirectUrl, {
            headers: {
                'Set-Cookie': `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["BEARER_COOKIE_NAME"]}=${token}`
            }
        });
    }
    return new Response('No token provided', {
        status: 400
    });
}
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__5afac32a._.js.map