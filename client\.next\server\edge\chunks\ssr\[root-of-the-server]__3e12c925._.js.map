{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\nimport { DateTime } from \"luxon\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n/**\r\n * Normaliza una fecha ISO a un objeto Date de JavaScript establecido a medianoche en la zona horaria local\r\n * @param dateStr Fecha en formato ISO string\r\n * @returns Objeto Date normalizado\r\n */\r\nexport function normalizeDate(dateStr: string): Date {\r\n  return DateTime.fromISO(dateStr)\r\n    .setZone('local')\r\n    .startOf('day')\r\n    .toJSDate();\r\n}\r\n\r\n/**\r\n * Convierte un array de fechas ISO a objetos Date normalizados\r\n * @param dateStrings Array de fechas en formato ISO string\r\n * @returns Array de objetos Date normalizados\r\n */\r\nexport function normalizeDates(dateStrings: string[]): Date[] {\r\n  if (!dateStrings || !Array.isArray(dateStrings)) return [];\r\n  return dateStrings.map(normalizeDate);\r\n}\r\n\r\n/**\r\n * Convierte un ISO a un string con formato \"dd de MMM de yyyy\"\r\n * @param date Fecha en formato ISO string\r\n * @returns String formateado\r\n */\r\nexport function formatISODate(date: string): string {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMM \\'de\\' yyyy', { locale: 'es' });\r\n}\r\n\r\n/**\r\n * Verifica si una fecha está en un array de fechas deshabilitadas\r\n * @param date Fecha a verificar\r\n * @param disabledDates Array de fechas deshabilitadas\r\n * @returns true si la fecha está deshabilitada, false en caso contrario\r\n */\r\nexport function isDateDisabled(date: Date, disabledDates: Date[]): boolean {\r\n  const luxonDate = DateTime.fromJSDate(date)\r\n    .setZone('local')\r\n    .startOf('day');\r\n\r\n  return disabledDates.some(disabledDate => {\r\n    const luxonDisabledDate = DateTime.fromJSDate(disabledDate);\r\n    return luxonDate.hasSame(luxonDisabledDate, 'day');\r\n  });\r\n}\r\n\r\n/**\r\n * Formatea una fecha a un string en formato: \"dd de MMM de yyyy\"\r\n * @param date Fecha a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatDate(date: string) {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMMM, yyyy', { locale: 'es' });\r\n}\r\n\r\n/** \r\n * Formatea un número a un string en formato de moneda local, por default MXN\r\n * @param value Número a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatCurrency(value: number, currency: string = 'MXN') {\r\n  return new Intl.NumberFormat('es-MX', { style: 'currency', currency }).format(value);\r\n\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,mKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAOzE,SAAS,cAAc,OAAe;IAC3C,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SACrB,OAAO,CAAC,SACR,OAAO,CAAC,OACR,QAAQ;AACb;AAOO,SAAS,eAAe,WAAqB;IAClD,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO,CAAC,cAAc,OAAO,EAAE;IAC1D,OAAO,YAAY,GAAG,CAAC;AACzB;AAOO,SAAS,cAAc,IAAY;IACxC,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,4BAA4B;QAAE,QAAQ;IAAK;AACzD;AAQO,SAAS,eAAe,IAAU,EAAE,aAAqB;IAC9D,MAAM,YAAY,uLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,MACnC,OAAO,CAAC,SACR,OAAO,CAAC;IAEX,OAAO,cAAc,IAAI,CAAC,CAAA;QACxB,MAAM,oBAAoB,uLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC9C,OAAO,UAAU,OAAO,CAAC,mBAAmB;IAC9C;AACF;AAOO,SAAS,WAAW,IAAY;IACrC,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,uBAAuB;QAAE,QAAQ;IAAK;AACpD;AAOO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY;IAAS,GAAG,MAAM,CAAC;AAEhF"}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,kMAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium  group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 !select-text cursor-text\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,kMAAC,yKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qOACA;QAED,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Slider({\r\n  className,\r\n  defaultValue,\r\n  value,\r\n  min = 0,\r\n  max = 100,\r\n  ...props\r\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\r\n  const _values = React.useMemo(\r\n    () =>\r\n      Array.isArray(value)\r\n        ? value\r\n        : Array.isArray(defaultValue)\r\n          ? defaultValue\r\n          : [min, max],\r\n    [value, defaultValue, min, max]\r\n  )\r\n\r\n  return (\r\n    <SliderPrimitive.Root\r\n      data-slot=\"slider\"\r\n      defaultValue={defaultValue}\r\n      value={value}\r\n      min={min}\r\n      max={max}\r\n      className={cn(\r\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SliderPrimitive.Track\r\n        data-slot=\"slider-track\"\r\n        className={cn(\r\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\r\n        )}\r\n      >\r\n        <SliderPrimitive.Range\r\n          data-slot=\"slider-range\"\r\n          className={cn(\r\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\r\n          )}\r\n        />\r\n      </SliderPrimitive.Track>\r\n      {Array.from({ length: _values.length }, (_, index) => (\r\n        <SliderPrimitive.Thumb\r\n          data-slot=\"slider-thumb\"\r\n          key={index}\r\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\r\n        />\r\n      ))}\r\n    </SliderPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Slider }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;IAClD,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;YAAC;YAAK;SAAI,EAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,kMAAC,0KAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,kMAAC,0KAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,kMAAC,0KAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,kMAAC,0KAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf"}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,kMAAC,4KAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,kMAAC,4KAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,kMAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B"}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: ButtonProps) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,wKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACS;IACZ,MAAM,OAAO,UAAU,wKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/env.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const envSchema = z.object({\r\n  NEXT_PUBLIC_API_URL: z.string().url(),\r\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\r\n  IS_DEV: z.string().optional().transform((val) => val === 'true'),\r\n});\r\n\r\nexport type Env = z.infer<typeof envSchema>;\r\n\r\n// create the same object but with process.env\r\nconst env = envSchema.parse({\r\n  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n  NODE_ENV: process.env.NODE_ENV,\r\n  IS_DEV: process.env.IS_DEV,\r\n});\r\n\r\nexport default env;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,YAAY,4IAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,qBAAqB,4IAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IACnC,UAAU,4IAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAChE,QAAQ,4IAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,MAAQ,QAAQ;AAC3D;AAIA,8CAA8C;AAC9C,MAAM,MAAM,UAAU,KAAK,CAAC;IAC1B,mBAAmB;IACnB,QAAQ;IACR,QAAQ,QAAQ,GAAG,CAAC,MAAM;AAC5B;uCAEe"}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/cookies.ts"], "sourcesContent": ["'use server';\nimport { cookies } from \"next/headers\";\nimport { getCookie, setCookie, deleteCookie } from \"cookies-next/server\";\n\n\nexport const getCookieServerByName = async ({\n  name,\n}: {\n  name: string;\n}) => {\n  const cookie = getCookie(name, { cookies });\n  return cookie;\n};\n\nexport const setCookieServerByName = async ({\n  name,\n  value\n}: {\n  name: string;\n  value: string;\n}) => {\n  console.log('setCookieServerByName', name, value);\n  await setCookie(name, value, { cookies });\n};\n\nexport const deleteCookieServerByName = async ({\n  name\n}: {\n  name: string;\n}) => {\n  await deleteCookie(name, { cookies });\n};"], "names": [], "mappings": ";;;;;;;;;IAKa,wBAAA,WAAA,GAAA,CAAA,GAAA,qPAAA,CAAA,wBAAA,EAAA,8CAAA,8KAAA,CAAA,aAAA,EAAA,KAAA,GAAA,4LAAA,CAAA,mBAAA,EAAA"}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/cookies.ts"], "sourcesContent": ["'use server';\nimport { cookies } from \"next/headers\";\nimport { getCookie, setCookie, deleteCookie } from \"cookies-next/server\";\n\n\nexport const getCookieServerByName = async ({\n  name,\n}: {\n  name: string;\n}) => {\n  const cookie = getCookie(name, { cookies });\n  return cookie;\n};\n\nexport const setCookieServerByName = async ({\n  name,\n  value\n}: {\n  name: string;\n  value: string;\n}) => {\n  console.log('setCookieServerByName', name, value);\n  await setCookie(name, value, { cookies });\n};\n\nexport const deleteCookieServerByName = async ({\n  name\n}: {\n  name: string;\n}) => {\n  await deleteCookie(name, { cookies });\n};"], "names": [], "mappings": ";;;;;;;;;IAca,wBAAA,WAAA,GAAA,CAAA,GAAA,qPAAA,CAAA,wBAAA,EAAA,8CAAA,8KAAA,CAAA,aAAA,EAAA,KAAA,GAAA,4LAAA,CAAA,mBAAA,EAAA"}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/cookies.ts"], "sourcesContent": ["'use server';\nimport { cookies } from \"next/headers\";\nimport { getCookie, setCookie, deleteCookie } from \"cookies-next/server\";\n\n\nexport const getCookieServerByName = async ({\n  name,\n}: {\n  name: string;\n}) => {\n  const cookie = getCookie(name, { cookies });\n  return cookie;\n};\n\nexport const setCookieServerByName = async ({\n  name,\n  value\n}: {\n  name: string;\n  value: string;\n}) => {\n  console.log('setCookieServerByName', name, value);\n  await setCookie(name, value, { cookies });\n};\n\nexport const deleteCookieServerByName = async ({\n  name\n}: {\n  name: string;\n}) => {\n  await deleteCookie(name, { cookies });\n};"], "names": [], "mappings": ";;;;;;;;;IAyBa,2BAAA,WAAA,GAAA,CAAA,GAAA,qPAAA,CAAA,wBAAA,EAAA,8CAAA,8KAAA,CAAA,aAAA,EAAA,KAAA,GAAA,4LAAA,CAAA,mBAAA,EAAA"}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\r\nimport env from \"../constants/env\";\r\nimport { BEARER_COOKIE_NAME } from '@/constants';\r\nimport { getCookie } from 'cookies-next/client';\r\n// import { sendLogToLogflare } from '@/lib/log-requests';\r\n\r\nfunction checkIfIsClient() {\r\n    return typeof window !== 'undefined';\r\n}\r\n\r\ntype ApiResponse<T> =\r\n    {\r\n        success: true;\r\n        data: T; status:\r\n        number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: InternalAxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: undefined\r\n    }\r\n    |\r\n    {\r\n        success: false;\r\n        data: undefined;\r\n        status: number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: AxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: any\r\n    };\r\n\r\ntype ErrorHandler = (error: any) => void;\r\n\r\nconst baseURL = env.NEXT_PUBLIC_API_URL;\r\n\r\nclass ApiService {\r\n    private axiosInstance: AxiosInstance;\r\n    baseURL: string = '';\r\n\r\n    /** \r\n     * @param version - The version of the API to use. Defaults to 'v1'.\r\n     */\r\n    constructor(\r\n        {\r\n            version,\r\n            prefix\r\n        }:\r\n            {\r\n                version?: 'v1' | 'v2',\r\n                prefix?: 'api' | 'dash-utils'\r\n            } =\r\n            {\r\n                version: 'v1',\r\n                prefix: 'api'\r\n            }) {\r\n\r\n\r\n        // Version is only available for api prefix so if prefix is dash-utils, version is not used\r\n        this.baseURL = `${baseURL}/${prefix}${prefix === 'api' ? `/${version}` : ''}`;\r\n\r\n        this.axiosInstance = axios.create({\r\n            baseURL: this.baseURL,\r\n            withCredentials: true\r\n        });\r\n    }\r\n\r\n    private async setHeaders() {\r\n        const isClient = checkIfIsClient();\r\n        if (isClient) {\r\n            return {};\r\n\r\n        };\r\n        const headers = (await import('next/headers')).headers;\r\n        const rawHeaders = await headers();\r\n        const headersObj: Record<string, string> = {};\r\n        rawHeaders.forEach((value, key) => {\r\n            headersObj[key] = value;\r\n        });\r\n        return headersObj;\r\n    }\r\n\r\n    private async request<T>(\r\n        method: 'get' | 'post' | 'patch' | 'put' | 'delete',\r\n        path: string,\r\n        config?: AxiosRequestConfig,\r\n        onError?: ErrorHandler\r\n    ): Promise<ApiResponse<T>> {\r\n        try {\r\n            const isClient = checkIfIsClient();\r\n            const headers = await this.setHeaders();\r\n            let token = ''\r\n\r\n            if (isClient) {\r\n                token = getCookie(BEARER_COOKIE_NAME) as string;\r\n            } else {\r\n                const getCookieServerByName = (await import('@/actions/cookies')).getCookieServerByName;\r\n                token = await getCookieServerByName({ name: BEARER_COOKIE_NAME }) as string;\r\n                if (path.includes('/files/download')) {\r\n                    console.log('Token on request for /api/v1/files/download: ', token);\r\n                }\r\n            }\r\n\r\n            const requestHeaders = {\r\n                    cookie: isClient ? undefined : headers.cookie,\r\n                    'x-dashboard-call': 'true',\r\n                    Authorization: `Bearer ${token}`,\r\n                    ...config?.headers\r\n                }\r\n\r\n            if (path.includes('/files/download')) {\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n                console.log('Headers: ', headers);\r\n                console.log('Request on /api/v1/files/download: ', requestHeaders);\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n            }\r\n\r\n            const response = await this.axiosInstance({\r\n                method,\r\n                url: path,\r\n                ...config,\r\n                headers: requestHeaders,\r\n            });\r\n            // Simplificamos el objeto de respuesta para evitar problemas de serialización\r\n            return {\r\n                success: true,\r\n                data: response.data,\r\n                status: response.status,\r\n                statusText: response.statusText,\r\n                headers: response.headers,\r\n                config: response.config,\r\n                request: response.request,\r\n                error: undefined\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error in request:', error?.config?.url);\r\n\r\n            if (onError) onError(error);\r\n            await this.handleError(error);\r\n            return {\r\n                success: false,\r\n                data: undefined,\r\n                status: error.response?.status || 500,\r\n                statusText: error.response?.statusText || 'Unknown Error',\r\n                headers: error.response?.headers || {},\r\n                config: error.config || {},\r\n                request: error.request || {},\r\n                error: error?.response?.data || error.message,\r\n            };\r\n        }\r\n    }\r\n\r\n    public async get<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('get', path, config, onError);\r\n    }\r\n\r\n    public async post<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('post', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async patch<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('patch', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async put<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('put', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async delete<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('delete', path, config, onError);\r\n    }\r\n\r\n    private async handleError(error: any) {\r\n        console.error('API Error:', {\r\n            message: error.message,\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n        });\r\n    }\r\n}\r\n\r\nexport const apiService = new ApiService();\r\n\r\nexport const dashUtilsService = new ApiService({ prefix: 'dash-utils' });\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACA,0DAA0D;AAE1D,SAAS;IACL,OAAO,gBAAkB;AAC7B;AA2BA,MAAM,UAAU,+HAAA,CAAA,UAAG,CAAC,mBAAmB;AAEvC,MAAM;IACM,cAA6B;IACrC,UAAkB,GAAG;IAErB;;KAEC,GACD,YACI,EACI,OAAO,EACP,MAAM,EAKL,GACD;QACI,SAAS;QACT,QAAQ;IACZ,CAAC,CAAE;QAGP,2FAA2F;QAC3F,IAAI,CAAC,OAAO,GAAG,GAAG,QAAQ,CAAC,EAAE,SAAS,WAAW,QAAQ,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI;QAE7E,IAAI,CAAC,aAAa,GAAG,6IAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC9B,SAAS,IAAI,CAAC,OAAO;YACrB,iBAAiB;QACrB;IACJ;IAEA,MAAc,aAAa;QACvB,MAAM,WAAW;QACjB,uCAAc;;QAGd;;QACA,MAAM,UAAU,CAAC,4IAA4B,EAAE,OAAO;QACtD,MAAM,aAAa,MAAM;QACzB,MAAM,aAAqC,CAAC;QAC5C,WAAW,OAAO,CAAC,CAAC,OAAO;YACvB,UAAU,CAAC,IAAI,GAAG;QACtB;QACA,OAAO;IACX;IAEA,MAAc,QACV,MAAmD,EACnD,IAAY,EACZ,MAA2B,EAC3B,OAAsB,EACC;QACvB,IAAI;YACA,MAAM,WAAW;YACjB,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;YACrC,IAAI,QAAQ;YAEZ,uCAAc;;YAEd,OAAO;gBACH,MAAM,wBAAwB,CAAC,yHAAiC,EAAE,qBAAqB;gBACvF,QAAQ,MAAM,sBAAsB;oBAAE,MAAM,iIAAA,CAAA,qBAAkB;gBAAC;gBAC/D,IAAI,KAAK,QAAQ,CAAC,oBAAoB;oBAClC,QAAQ,GAAG,CAAC,iDAAiD;gBACjE;YACJ;YAEA,MAAM,iBAAiB;gBACf,QAAQ,6EAAuB,QAAQ,MAAM;gBAC7C,oBAAoB;gBACpB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,GAAG,QAAQ,OAAO;YACtB;YAEJ,IAAI,KAAK,QAAQ,CAAC,oBAAoB;gBAClC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBAEZ,QAAQ,GAAG,CAAC,aAAa;gBACzB,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;YAEhB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;gBACtC;gBACA,KAAK;gBACL,GAAG,MAAM;gBACT,SAAS;YACb;YACA,8EAA8E;YAC9E,OAAO;gBACH,SAAS;gBACT,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,QAAQ,SAAS,MAAM;gBACvB,SAAS,SAAS,OAAO;gBACzB,OAAO;YACX;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,qBAAqB,OAAO,QAAQ;YAElD,IAAI,SAAS,QAAQ;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,OAAO;gBACH,SAAS;gBACT,MAAM;gBACN,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,YAAY,MAAM,QAAQ,EAAE,cAAc;gBAC1C,SAAS,MAAM,QAAQ,EAAE,WAAW,CAAC;gBACrC,QAAQ,MAAM,MAAM,IAAI,CAAC;gBACzB,SAAS,MAAM,OAAO,IAAI,CAAC;gBAC3B,OAAO,OAAO,UAAU,QAAQ,MAAM,OAAO;YACjD;QACJ;IACJ;IAEA,MAAa,IAAO,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC5G,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,QAAQ;IAC7C;IAEA,MAAa,KAAQ,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACzH,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC3D;IAEA,MAAa,MAAS,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC1H,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC5D;IAEA,MAAa,IAAO,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACxH,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC1D;IAEA,MAAa,OAAU,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC/G,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM,QAAQ;IAChD;IAEA,MAAc,YAAY,KAAU,EAAE;QAClC,QAAQ,KAAK,CAAC,cAAc;YACxB,SAAS,MAAM,OAAO;YACtB,QAAQ,MAAM,QAAQ,EAAE;YACxB,MAAM,MAAM,QAAQ,EAAE;QAC1B;IACJ;AACJ;AAEO,MAAM,aAAa,IAAI;AAEvB,MAAM,mBAAmB,IAAI,WAAW;IAAE,QAAQ;AAAa"}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/api/favorites.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\nimport { Vehicle } from './vehicles.api';\n\nexport interface FavoriteResponse {\n  success: boolean;\n  message?: string;\n  data?: Vehicle[];\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nexport interface FavoriteStatusResponse {\n  success: boolean;\n  message?: string;\n  isFavorite: boolean;\n}\n\nexport interface MultipleFavoriteStatusResponse {\n  success: boolean;\n  message?: string;\n  data: Record<string, boolean>;\n}\n\nexport interface AddFavoriteResponse {\n  success: boolean;\n  message?: string;\n  data?: any;\n}\n\nexport const favoritesApi = {\n  // Obtener favoritos del usuario con paginación\n  getFavorites: async (params: { page: number; limit: number }) => {\n    const result = await apiService.get<FavoriteResponse>('/client/favorites', { params });\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Agregar vehículo a favoritos\n  addToFavorites: async (vehicleId: string) => {\n    const result = await apiService.post<AddFavoriteResponse>(`/client/favorites/${vehicleId}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Remover vehículo de favoritos\n  removeFromFavorites: async (vehicleId: string) => {\n    const result = await apiService.delete<{ success: boolean; message?: string }>(`/client/favorites/${vehicleId}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Verificar si un vehículo está en favoritos\n  isFavorite: async (vehicleId: string) => {\n    const result = await apiService.get<FavoriteStatusResponse>(`/client/favorites/${vehicleId}/status`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Obtener estados de favoritos para múltiples vehículos\n  getFavoritesStatus: async (vehicleIds: string[]) => {\n    const result = await apiService.post<MultipleFavoriteStatusResponse>('/client/favorites/status', {\n      vehicleIds\n    });\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Toggle favorito (agregar si no está, remover si está)\n  toggleFavorite: async (vehicleId: string) => {\n    try {\n      // Primero verificar el estado actual\n      const statusResult = await favoritesApi.isFavorite(vehicleId);\n      \n      if (statusResult.isFavorite) {\n        // Si está en favoritos, remover\n        return await favoritesApi.removeFromFavorites(vehicleId);\n      } else {\n        // Si no está en favoritos, agregar\n        return await favoritesApi.addToFavorites(vehicleId);\n      }\n    } catch (error: any) {\n      throw new Error(error.message || 'Error al cambiar estado de favorito');\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AAmCO,MAAM,eAAe;IAC1B,+CAA+C;IAC/C,cAAc,OAAO;QACnB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAmB,qBAAqB;YAAE;QAAO;QACpF,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,+BAA+B;IAC/B,gBAAgB,OAAO;QACrB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAsB,CAAC,kBAAkB,EAAE,WAAW;QAC1F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,gCAAgC;IAChC,qBAAqB,OAAO;QAC1B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,MAAM,CAAyC,CAAC,kBAAkB,EAAE,WAAW;QAC/G,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6CAA6C;IAC7C,YAAY,OAAO;QACjB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAyB,CAAC,kBAAkB,EAAE,UAAU,OAAO,CAAC;QACnG,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,wDAAwD;IACxD,oBAAoB,OAAO;QACzB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAiC,4BAA4B;YAC/F;QACF;QACA,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,wDAAwD;IACxD,gBAAgB,OAAO;QACrB,IAAI;YACF,qCAAqC;YACrC,MAAM,eAAe,MAAM,aAAa,UAAU,CAAC;YAEnD,IAAI,aAAa,UAAU,EAAE;gBAC3B,gCAAgC;gBAChC,OAAO,MAAM,aAAa,mBAAmB,CAAC;YAChD,OAAO;gBACL,mCAAmC;gBACnC,OAAO,MAAM,aAAa,cAAc,CAAC;YAC3C;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACnC;IACF;AACF"}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/context/user-context.tsx"], "sourcesContent": ["'use client';\r\nimport { Session } from 'better-auth/types';\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\n\r\n\r\ninterface UserContextType {\r\n  user: User;\r\n  setUser: React.Dispatch<React.SetStateAction<User>>;\r\n  session: Session;\r\n  setSession: React.Dispatch<React.SetStateAction<any>>;\r\n}\r\n\r\nexport const UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children, session: sessionData }: { children: React.ReactNode; session: { user: User; session: Session } }) {\r\n\r\n  // console.log('sessionData: ', sessionData.user);\r\n  const [session, setSession] = useState(sessionData?.session);\r\n  const [user, setUser] = useState(sessionData?.user);\r\n\r\n  useEffect(() => {\r\n    if (sessionData) {\r\n      setUser(sessionData.user);\r\n      setSession(sessionData.session);\r\n    }\r\n  }, [sessionData]);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser, session, setSession }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error('useUser must be used within a UserProvider');\r\n  }\r\n  return context;\r\n}"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAYO,MAAM,4BAAc,CAAA,GAAA,kKAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,WAAW,EAA4E;IAEvI,kDAAkD;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAE9C,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,QAAQ,YAAY,IAAI;YACxB,WAAW,YAAY,OAAO;QAChC;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,kMAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;YAAS;QAAW;kBAC/D;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 799, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/vehicles/favorite-button.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useContext } from \"react\"\nimport { Heart } from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\nimport { favoritesApi } from \"@/lib/api/favorites.api\"\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\"\nimport { UserContext } from \"@/context/user-context\"\nimport toast from \"react-hot-toast\"\nimport { cn } from \"@/lib/utils\"\n\n// Hook personalizado que no falla si no hay UserProvider\nfunction useSafeUser() {\n  const context = useContext(UserContext)\n  return context || { user: null, setUser: () => { }, session: null, setSession: () => { } }\n}\n\ninterface FavoriteButtonProps {\n  vehicleId: string\n  className?: string\n  size?: \"sm\" | \"default\" | \"lg\" | \"icon\"\n  variant?: \"default\" | \"secondary\" | \"ghost\" | \"outline\"\n  showToast?: boolean\n}\n\nexport default function FavoriteButton({\n  vehicleId,\n  className,\n  size = \"icon\",\n  variant = \"secondary\",\n  showToast = true\n}: FavoriteButtonProps) {\n  const { user } = useSafeUser()\n  const queryClient = useQueryClient()\n  const [isOptimistic, setIsOptimistic] = useState(false)\n\n  // Solo mostrar el botón si el usuario está autenticado y es de tipo client\n  const shouldShow = Boolean(user && user.userType === 'client')\n\n  // Query para verificar si el vehículo está en favoritos\n  const { data: favoriteStatus, isLoading } = useQuery({\n    queryKey: ['favorite-status', vehicleId],\n    queryFn: () => favoritesApi.isFavorite(vehicleId),\n    enabled: shouldShow,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n\n  // Mutation para toggle favorito\n  const toggleMutation = useMutation({\n    mutationFn: () => favoritesApi.toggleFavorite(vehicleId),\n    onMutate: async () => {\n      // Optimistic update\n      setIsOptimistic(true)\n      await queryClient.cancelQueries({ queryKey: ['favorite-status', vehicleId] })\n\n      const previousStatus = queryClient.getQueryData(['favorite-status', vehicleId])\n\n      // Actualizar optimísticamente\n      queryClient.setQueryData(['favorite-status', vehicleId], (old: any) => ({\n        ...old,\n        isFavorite: !old?.isFavorite\n      }))\n\n      return { previousStatus }\n    },\n    onError: (err, variables, context) => {\n      // Revertir en caso de error\n      if (context?.previousStatus) {\n        queryClient.setQueryData(['favorite-status', vehicleId], context.previousStatus)\n      }\n      if (showToast) {\n        toast.error('Error al actualizar favorito')\n      }\n      console.error('Error toggling favorite:', err)\n    },\n    onSuccess: (data) => {\n      // Invalidar queries relacionadas\n      queryClient.invalidateQueries({ queryKey: ['favorite-status', vehicleId] })\n      queryClient.invalidateQueries({ queryKey: ['favorites'] })\n\n      if (showToast && data?.success) {\n        toast.success(data.message || 'Favorito actualizado')\n      }\n    },\n    onSettled: () => {\n      setIsOptimistic(false)\n    }\n  })\n\n  const handleToggle = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n\n    if (!shouldShow) {\n      if (showToast) {\n        toast.error('Debes iniciar sesión como cliente para usar favoritos')\n      }\n      return\n    }\n\n    toggleMutation.mutate()\n  }\n\n  // No mostrar el botón si el usuario no está autenticado o no es client\n  if (!shouldShow) {\n    return null\n  }\n\n  const isFavorite = favoriteStatus?.isFavorite || false\n  const isProcessing = isLoading || toggleMutation.isPending || isOptimistic\n\n  return (\n    <Button\n      size={size}\n      variant={variant}\n      className={cn(\n        \"relative transition-all duration-200\",\n        isFavorite && \"bg-red-50 hover:bg-red-100 border-red-200\",\n        className\n      )}\n      onClick={handleToggle}\n      disabled={isProcessing}\n    >\n      <Heart\n        className={cn(\n          \"transition-all duration-200\",\n          size === \"sm\" ? \"h-3 w-3\" : size === \"lg\" ? \"h-6 w-6\" : \"h-4 w-4\",\n          isFavorite ? \"fill-red-500 text-red-500\" : \"text-gray-600\",\n          isProcessing && \"animate-pulse\"\n        )}\n      />\n      {isProcessing && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n      ASSAFSA\n    </Button>\n  )\n}\n\n// Hook personalizado para usar en otros componentes\nexport function useFavoriteStatus(vehicleId: string) {\n  const { user } = useSafeUser()\n  const shouldFetch = Boolean(user && user.userType === 'client')\n\n  return useQuery({\n    queryKey: ['favorite-status', vehicleId],\n    queryFn: () => favoritesApi.isFavorite(vehicleId),\n    enabled: shouldFetch,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n}\n\n// Hook para obtener múltiples estados de favoritos\nexport function useFavoritesStatus(vehicleIds: string[]) {\n  const { user } = useSafeUser()\n  const shouldFetch = Boolean(user && user.userType === 'client' && vehicleIds.length > 0)\n\n  return useQuery({\n    queryKey: ['favorites-status', vehicleIds],\n    queryFn: () => favoritesApi.getFavoritesStatus(vehicleIds),\n    enabled: shouldFetch,\n    staleTime: 1000 * 60 * 5, // 5 minutos\n  })\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,yDAAyD;AACzD,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,0IAAA,CAAA,cAAW;IACtC,OAAO,WAAW;QAAE,MAAM;QAAM,SAAS,KAAQ;QAAG,SAAS;QAAM,YAAY,KAAQ;IAAE;AAC3F;AAUe,SAAS,eAAe,EACrC,SAAS,EACT,SAAS,EACT,OAAO,MAAM,EACb,UAAU,WAAW,EACrB,YAAY,IAAI,EACI;IACpB,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,cAAc,CAAA,GAAA,8LAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,2EAA2E;IAC3E,MAAM,aAAa,QAAQ,QAAQ,KAAK,QAAQ,KAAK;IAErD,wDAAwD;IACxD,MAAM,EAAE,MAAM,cAAc,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,UAAU;YAAC;YAAmB;SAAU;QACxC,SAAS,IAAM,6IAAA,CAAA,eAAY,CAAC,UAAU,CAAC;QACvC,SAAS;QACT,WAAW,OAAO,KAAK;IACzB;IAEA,gCAAgC;IAChC,MAAM,iBAAiB,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD,EAAE;QACjC,YAAY,IAAM,6IAAA,CAAA,eAAY,CAAC,cAAc,CAAC;QAC9C,UAAU;YACR,oBAAoB;YACpB,gBAAgB;YAChB,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAmB;iBAAU;YAAC;YAE3E,MAAM,iBAAiB,YAAY,YAAY,CAAC;gBAAC;gBAAmB;aAAU;YAE9E,8BAA8B;YAC9B,YAAY,YAAY,CAAC;gBAAC;gBAAmB;aAAU,EAAE,CAAC,MAAa,CAAC;oBACtE,GAAG,GAAG;oBACN,YAAY,CAAC,KAAK;gBACpB,CAAC;YAED,OAAO;gBAAE;YAAe;QAC1B;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,4BAA4B;YAC5B,IAAI,SAAS,gBAAgB;gBAC3B,YAAY,YAAY,CAAC;oBAAC;oBAAmB;iBAAU,EAAE,QAAQ,cAAc;YACjF;YACA,IAAI,WAAW;gBACb,+JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;YACA,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;QACA,WAAW,CAAC;YACV,iCAAiC;YACjC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAmB;iBAAU;YAAC;YACzE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YAExD,IAAI,aAAa,MAAM,SAAS;gBAC9B,+JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,KAAK,OAAO,IAAI;YAChC;QACF;QACA,WAAW;YACT,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,CAAC,YAAY;YACf,IAAI,WAAW;gBACb,+JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;YACA;QACF;QAEA,eAAe,MAAM;IACvB;IAEA,uEAAuE;IACvE,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,MAAM,aAAa,gBAAgB,cAAc;IACjD,MAAM,eAAe,aAAa,eAAe,SAAS,IAAI;IAE9D,qBACE,kMAAC,0IAAA,CAAA,SAAM;QACL,MAAM;QACN,SAAS;QACT,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,wCACA,cAAc,6CACd;QAEF,SAAS;QACT,UAAU;;0BAEV,kMAAC,4MAAA,CAAA,QAAK;gBACJ,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,+BACA,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WACxD,aAAa,8BAA8B,iBAC3C,gBAAgB;;;;;;YAGnB,8BACC,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAI,WAAU;;;;;;;;;;;YAEjB;;;;;;;AAIR;AAGO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,cAAc,QAAQ,QAAQ,KAAK,QAAQ,KAAK;IAEtD,OAAO,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAmB;SAAU;QACxC,SAAS,IAAM,6IAAA,CAAA,eAAY,CAAC,UAAU,CAAC;QACvC,SAAS;QACT,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,SAAS,mBAAmB,UAAoB;IACrD,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,cAAc,QAAQ,QAAQ,KAAK,QAAQ,KAAK,YAAY,WAAW,MAAM,GAAG;IAEtF,OAAO,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAoB;SAAW;QAC1C,SAAS,IAAM,6IAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC;QAC/C,SAAS;QACT,WAAW,OAAO,KAAK;IACzB;AACF"}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/vehicles/vehicle-card.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\"\r\nimport { Heart, MapPin } from \"lucide-react\"\r\nimport Image from \"next/image\"\r\nimport { DateTime } from 'luxon';\r\nimport FavoriteButton from \"./favorite-button\"\r\n// import Link from \"next/link\"\r\n\r\n// Mapeo de tipos de carrocería en español\r\nconst bodyTypeLabels: Record<string, string> = {\r\n  sedan: \"Sedan\",\r\n  suv: \"SUV\",\r\n  hatchback: \"Hatchback\",\r\n  pickup: \"Pickup\",\r\n  coupe: \"Coupe\",\r\n  convertible: \"Convertible\",\r\n  wagon: \"Wagon\",\r\n  van: \"Van\",\r\n  minivan: \"Minivan\",\r\n  targa: \"Targa\",\r\n  doublecab: \"Doble Cabina\",\r\n  truck: \"Camioneta\"\r\n};\r\n\r\ninterface VehicleCardProps {\r\n  id: string\r\n  make: string\r\n  model: string\r\n  price: number\r\n  rating: number\r\n  reviews: number\r\n  images: string[] | any\r\n  engineSize: number\r\n  transmission: string\r\n  trim: string\r\n  bodyType: string\r\n  features: {\r\n    fuelType: string\r\n    seats: number\r\n    mileage: number\r\n    registrationNumber: string\r\n    insurancePolicy: string\r\n    rules: string\r\n    location: string\r\n    weeklyRate: number\r\n    monthlyRate: number\r\n  }\r\n  year: number\r\n  createdAt: string\r\n}\r\n\r\nexport default function VehicleCard({\r\n  id,\r\n  make,\r\n  model,\r\n  price,\r\n  // rating,\r\n  // reviews,\r\n  images,\r\n  features,\r\n  year,\r\n  engineSize,\r\n  transmission,\r\n  trim,\r\n  bodyType,\r\n  createdAt\r\n}: VehicleCardProps) {\r\n  // Obtener valores clave para mostrar en la tarjeta\r\n  const transmissionLabel = transmission === 'automatic' ? 'Automático' : 'Manual';\r\n  const mileage = features?.mileage ? `${features.mileage.toLocaleString()} km` : '';\r\n  const location = features?.location || '';\r\n  const bodyTypeLabel = bodyTypeLabels[bodyType] || bodyType;\r\n\r\n  const isRecent = (createdAt: string) => {\r\n    const createdDate = DateTime.fromISO(createdAt);\r\n    const sevenDaysAgo = DateTime.now().minus({ days: 7 });\r\n    return createdDate > sevenDaysAgo;\r\n  }\r\n\r\n  return (\r\n    <Card className=\"overflow-hidden hover:shadow-md pt-0 transition-shadow h-full\">\r\n      <div className=\"relative h-64 sm:h-80 md:h-48 xl:h-64\">\r\n        {isRecent(createdAt) && (\r\n          <div className=\"absolute top-2 left-2 z-10\">\r\n            <span className=\"bg-blue-500 text-white text-xs px-2 py-1 rounded-md\">\r\n              Recién publicado\r\n            </span>\r\n          </div>\r\n        )}\r\n          <Image\r\n            src={Array.isArray(images) && images.length > 0 ? images[0] : \"/placeholder.svg\"}\r\n            alt={`${make} ${model}`}\r\n          width={300}\r\n          height={200}\r\n          className=\"w-full h-64 sm:h-80 md:h-48 xl:h-64 object-cover\"\r\n        />\r\n        <div className=\"absolute top-2 right-2 z-10\">\r\n          <FavoriteButton vehicleId={id} className=\"bg-white hover:bg-gray-50\" />\r\n        </div>\r\n      </div>\r\n      <CardContent className=\"px-4\">\r\n        <div className=\"mb-2\">\r\n            <h3 className=\"font-semibold text-lg hover:text-blue-600 transition-colors\">\r\n              {make} {model}\r\n          </h3>\r\n          <div className=\"text-sm text-gray-600\">\r\n            {engineSize}L {trim} {bodyTypeLabel}\r\n          </div>\r\n          <div className=\"text-sm text-gray-600\">\r\n            {year && `${year} • `}{mileage}\r\n            {transmission && ` • ${transmissionLabel}`}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-4\">\r\n          <div className=\"text-sm text-gray-600\">Precio desde</div>\r\n          <div className=\"flex items-baseline\">\r\n            <span className=\"font-bold text-xl\">${price.toLocaleString()}</span>\r\n            <span className=\"text-gray-500 text-sm ml-1\">\r\n              / día\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        {location && (\r\n          <div className=\"flex items-center mt-3 pt-3 border-t border-gray-100\">\r\n            <MapPin className=\"h-4 w-4 text-gray-400 mr-1\" />\r\n            <span className=\"text-sm text-gray-600\">{location}</span>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;;;;;AACA,+BAA+B;AAE/B,0CAA0C;AAC1C,MAAM,iBAAyC;IAC7C,OAAO;IACP,KAAK;IACL,WAAW;IACX,QAAQ;IACR,OAAO;IACP,aAAa;IACb,OAAO;IACP,KAAK;IACL,SAAS;IACT,OAAO;IACP,WAAW;IACX,OAAO;AACT;AA6Be,SAAS,YAAY,EAClC,EAAE,EACF,IAAI,EACJ,KAAK,EACL,KAAK,EACL,UAAU;AACV,WAAW;AACX,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,QAAQ,EACR,SAAS,EACQ;IACjB,mDAAmD;IACnD,MAAM,oBAAoB,iBAAiB,cAAc,eAAe;IACxE,MAAM,UAAU,UAAU,UAAU,GAAG,SAAS,OAAO,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG;IAChF,MAAM,WAAW,UAAU,YAAY;IACvC,MAAM,gBAAgB,cAAc,CAAC,SAAS,IAAI;IAElD,MAAM,WAAW,CAAC;QAChB,MAAM,cAAc,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC;QACrC,MAAM,eAAe,uLAAA,CAAA,WAAQ,CAAC,GAAG,GAAG,KAAK,CAAC;YAAE,MAAM;QAAE;QACpD,OAAO,cAAc;IACvB;IAEA,qBACE,kMAAC,wIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,kMAAC;gBAAI,WAAU;;oBACZ,SAAS,4BACR,kMAAC;wBAAI,WAAU;kCACb,cAAA,kMAAC;4BAAK,WAAU;sCAAsD;;;;;;;;;;;kCAKxE,kMAAC,iLAAA,CAAA,UAAK;wBACJ,KAAK,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,GAAG;wBAC9D,KAAK,GAAG,KAAK,CAAC,EAAE,OAAO;wBACzB,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,kMAAC;wBAAI,WAAU;kCACb,cAAA,kMAAC,4JAAA,CAAA,UAAc;4BAAC,WAAW;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAG7C,kMAAC,wIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,kMAAC;wBAAI,WAAU;;0CACX,kMAAC;gCAAG,WAAU;;oCACX;oCAAK;oCAAE;;;;;;;0CAEZ,kMAAC;gCAAI,WAAU;;oCACZ;oCAAW;oCAAG;oCAAK;oCAAE;;;;;;;0CAExB,kMAAC;gCAAI,WAAU;;oCACZ,QAAQ,GAAG,KAAK,GAAG,CAAC;oCAAE;oCACtB,gBAAgB,CAAC,GAAG,EAAE,mBAAmB;;;;;;;;;;;;;kCAI9C,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAI,WAAU;0CAAwB;;;;;;0CACvC,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAK,WAAU;;4CAAoB;4CAAE,MAAM,cAAc;;;;;;;kDAC1D,kMAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;oBAMhD,0BACC,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,kNAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,kMAAC;gCAAK,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;;;;;;;AAMrD"}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/api/vehicles.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\r\nimport { cache } from 'react';\r\n\r\nexport interface VehicleFormData {\r\n  make: string;\r\n  model: string;\r\n  year: number;\r\n  color: string;\r\n  vin: string;\r\n  plate: string;\r\n  state_code: string;\r\n  country_code: string;\r\n  price: number;\r\n  description: string;\r\n\r\n  // Nuevos campos estructurados\r\n  engineSize: number;\r\n  transmission: string;\r\n  trim: string;\r\n  bodyType: string;\r\n\r\n  // Campos existentes\r\n  features: any;\r\n  amenities: string[];\r\n  // images?: string[];\r\n  status?: string;\r\n  images?: File[];\r\n  // vinDocument?: File;\r\n  // plateDocument?: File;\r\n  // registrationDocument?: File;\r\n  // insurancePolicyDocument?: File;\r\n  // titleDocument?: File;\r\n  vinDocument: File[];\r\n  plateDocument: File[];\r\n  registrationDocument: File[];\r\n  insurancePolicyDocument: File[];\r\n}\r\n\r\nexport interface Host {\r\n  id: string;\r\n  name: string;\r\n  image?: string;\r\n  email: string;\r\n  phone: string;\r\n  status: string;\r\n  isVerified: boolean;\r\n  isBlocked: boolean;\r\n\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface Vehicle {\r\n  id: string;\r\n  make: string;\r\n  model: string;\r\n  year: number;\r\n  color: string;\r\n  vin?: string;\r\n  // licensePlate?: string;\r\n  plate?: string;\r\n  state_code?: string;\r\n  country_code?: string;\r\n  price: number;\r\n  rating: number;\r\n  reviews: number;\r\n\r\n  // Nuevos campos estructurados\r\n  engineSize: number;\r\n  transmission: string;\r\n  trim: string;\r\n  bodyType: string;\r\n\r\n  approvalHistory: {\r\n    action: string;\r\n    date: string;\r\n    reason?: string;\r\n    userId?: string;\r\n    user?: {\r\n      id: string;\r\n      name: string;\r\n      email: string;\r\n    };\r\n  }[];\r\n\r\n  // Campos existentes\r\n  images: string[];\r\n  features: {\r\n    fuelType: string;\r\n    seats: number;\r\n    mileage: number;\r\n    registrationNumber: string;\r\n    insurancePolicy: string;\r\n    rules: string;\r\n    location: string;\r\n    weeklyRate: number;\r\n    monthlyRate: number;\r\n  };\r\n  description: string;\r\n  amenities: string[];\r\n  host: Host;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface VehicleResponse {\r\n  data: Vehicle[];\r\n  pagination: Pagination;\r\n}\r\n\r\nexport interface TimeSlot {\r\n  startTime: string;\r\n  endTime: string;\r\n}\r\n\r\nexport interface AvailabilitySettings {\r\n  defaultCheckInTime: string;\r\n  defaultCheckOutTime: string;\r\n  minimumRentalNights: number;\r\n  maximumRentalNights: number;\r\n  // bufferTimeBetweenRentals: number; // horas\r\n  advanceBookingPeriod: number; // días\r\n  instantBooking: boolean;\r\n  allowSameDayBooking: boolean;\r\n  cancellationPolicy: \"flexible\" | \"moderate\" | \"strict\";\r\n\r\n  mondayAvailable: boolean;\r\n  tuesdayAvailable: boolean;\r\n  wednesdayAvailable: boolean;\r\n  thursdayAvailable: boolean;\r\n  fridayAvailable: boolean;\r\n  saturdayAvailable: boolean;\r\n  sundayAvailable: boolean;\r\n  blockedDates: any[];\r\n}\r\n\r\nexport interface VehicleDocuments {\r\n  vinDocument: string;\r\n  plateDocument: string;\r\n  registrationDocument: string;\r\n  insurancePolicyDocument: string;\r\n}\r\n\r\n// Agregar esta interfaz para las estadísticas\r\nexport interface VehicleStats {\r\n  stats: {\r\n    total: number;\r\n    active: number;\r\n    rented: number;\r\n    maintenance: number;\r\n    pending: number;\r\n    totalReservations?: number;\r\n    averageRating?: number;\r\n    averageIncome?: number;\r\n  }\r\n}\r\n\r\nexport const vehiclesApi = {\r\n  // Obtener todos los vehículos (público)\r\n  getAll: async (params: { page: number; limit: number }) => {\r\n    const result = await apiService.get<VehicleResponse>('/vehicles', { params });\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener un vehículo por ID (público)\r\n  getById: async (id: string) => {\r\n    const result = await apiService.get<Vehicle>(`/vehicles/${id}`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener todos los vehículos (admin)\r\n  getAllForAdmin: async () => {\r\n    const result = await apiService.get<VehicleResponse>('/admin/vehicles');\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Obtener vehículos del host actual\r\n  getMyVehicles: async (): Promise<VehicleResponse[]> => {\r\n    const result = await apiService.get<VehicleResponse[]>('/host/vehicles');\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Añadir método para obtener un vehículo específico del host\r\n  getMyVehicleById: async (id: string): Promise<VehicleResponse> => {\r\n    const result = await apiService.get<VehicleResponse>(`/host/vehicles/${id}`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  getAvailabilitySettings: async (vehicleId: string): Promise<AvailabilitySettings> => {\r\n    const result = await apiService.get<AvailabilitySettings>(`/vehicles/${vehicleId}/availability`);\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n\r\n\r\n  host: {\r\n    getAll: async (params: { page?: number; limit?: number }) => {\r\n      const result = await apiService.get<VehicleResponse>('/host/vehicles', { params });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    getById: async (id: string) => {\r\n      const result = await apiService.get<Vehicle>(`/host/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    create: async (data: VehicleFormData) => {\r\n      const result = await apiService.post<Vehicle>('/host/vehicles', data);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        // console.error('Error creating vehicle:', result.error);\r\n        throw new Error(result.error.message);\r\n      }\r\n    },\r\n\r\n    uploadFiles: async (vehicleId: string, data: Partial<VehicleFormData>) => {\r\n      const formData = new FormData();\r\n\r\n      // Agregar archivos al formData para los campos images, vinDocument, plateDocument, registrationDocument, insurancePolicyDocument\r\n      if (data.images) {\r\n        for (const file of data.images) {\r\n          formData.append('images', file);\r\n        }\r\n      }\r\n      if (data.vinDocument) {\r\n        for (const file of data.vinDocument) {\r\n          formData.append('vinDocument', file);\r\n        }\r\n      }\r\n      if (data.plateDocument) {\r\n        for (const file of data.plateDocument) {\r\n          formData.append('plateDocument', file);\r\n        }\r\n      }\r\n      if (data.registrationDocument) {\r\n        for (const file of data.registrationDocument) {\r\n          formData.append('registrationDocument', file);\r\n        }\r\n      }\r\n      if (data.insurancePolicyDocument) {\r\n        for (const file of data.insurancePolicyDocument) {\r\n          formData.append('insurancePolicyDocument', file);\r\n        }\r\n      }\r\n\r\n      const result = await apiService.patch<Vehicle>(`/host/vehicles/${vehicleId}/upload-files`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Añadir método para actualizar archivos\r\n    updateFiles: async (vehicleId: string, data: Partial<VehicleFormData>, imagesToRemove: string[] = []) => {\r\n\r\n      const formData = new FormData();\r\n\r\n      // Agregar archivos al formData solo si existen y tienen elementos\r\n      if (data.images && data.images.length > 0) {\r\n        for (const file of data.images) {\r\n          formData.append('images', file);\r\n        }\r\n      }\r\n\r\n      if (data.vinDocument && data.vinDocument.length > 0) {\r\n        for (const file of data.vinDocument) {\r\n          formData.append('vinDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.plateDocument && data.plateDocument.length > 0) {\r\n        for (const file of data.plateDocument) {\r\n          formData.append('plateDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.registrationDocument && data.registrationDocument.length > 0) {\r\n        for (const file of data.registrationDocument) {\r\n          formData.append('registrationDocument', file);\r\n        }\r\n      }\r\n\r\n      if (data.insurancePolicyDocument && data.insurancePolicyDocument.length > 0) {\r\n        for (const file of data.insurancePolicyDocument) {\r\n          formData.append('insurancePolicyDocument', file);\r\n        }\r\n      }\r\n\r\n      // Agregar imágenes a eliminar\r\n      if (imagesToRemove.length > 0) {\r\n        formData.append('imagesToRemove', JSON.stringify(imagesToRemove));\r\n      }\r\n\r\n      const result = await apiService.patch<Vehicle>(`/host/vehicles/${vehicleId}/update-files`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      console.log('Result:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error?.message || 'Ha ocurrido un error actualizando los archivos del vehículo.');\r\n      }\r\n    },\r\n\r\n    update: async (id: string, data: Partial<VehicleFormData>) => {\r\n      const result = await apiService.put<Vehicle>(`/host/vehicles/${id}`, data);\r\n      console.log('Result of update vehicle:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        console.error('Error updating vehicle:', result.error);\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Eliminar un vehículo (host)\r\n    delete: async (id: string) => {\r\n      const result = await apiService.delete(`/host/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    blockDates: async (vehicleId: string, data: {\r\n      startDate: string;\r\n      endDate: string;\r\n      reason?: string;\r\n    }): Promise<any> => {\r\n\r\n      if (!data.startDate || !data.endDate) {\r\n        throw new Error(\"Se requieren fechas válidas para bloquear\");\r\n      }\r\n\r\n      const result = await apiService.post('/host/reservations/block-dates', {\r\n        vehicleId,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        reason: data.reason || \"No disponible\"\r\n      });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Actualizar configuración de disponibilidad\r\n    updateAvailabilitySettings: async (vehicleId: string, data: Partial<AvailabilitySettings>): Promise<AvailabilitySettings> => {\r\n      const result = await apiService.put<AvailabilitySettings>(`/host/vehicles/${vehicleId}/availability`, data);\r\n      console.log('error: ', result.error);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n    }\r\n  },\r\n    getStats: async (): Promise<any> => {\r\n      const result = await apiService.get('/host/vehicles/stats');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Obtener documentos de un vehículo\r\n    getDocuments: async (vehicleId: string) => {\r\n      const result = await apiService.get<VehicleDocuments>(`/host/vehicles/${vehicleId}/documents`);\r\n      console.log('Result of get documents:', result);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    requestReview: async (vehicleId: string) => {\r\n      const result = await apiService.post(`/host/vehicles/${vehicleId}/request-review`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  // Actualizar estado de un vehículo (host)\r\n  updateStatus: async (id: string, status: string): Promise<VehicleResponse> => {\r\n    const result = await apiService.patch<VehicleResponse>(`/host/vehicles/${id}/status`, { status });\r\n    if (result.success) {\r\n      return result.data;\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n\r\n  // Funciones específicas para administradores\r\n  admin: {\r\n\r\n    getById: async (id: string) => {\r\n      const result = await apiService.get<Vehicle>(`/admin/vehicles/${id}`);\r\n      if (result.success) {\r\n        console.log('vehicle from api', result.data);\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Obtener vehículos pendientes de aprobación\r\n    getPendingVehicles: async () => {\r\n      const result = await apiService.get<Vehicle[]>('/admin/vehicles/pending');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error.message);\r\n      }\r\n    },\r\n\r\n    // Aprobar un vehículo\r\n    approveVehicle: async (id: string) => {\r\n      const result = await apiService.post<Vehicle>(`/admin/vehicles/${id}/approve`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Rechazar un vehículo\r\n    rejectVehicle: async (id: string, reason: string): Promise<any> => {\r\n      const result = await apiService.post(`/admin/vehicles/${id}/reject`, { reason });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Actualizar un vehículo (admin)\r\n    update: async (id: string, data: Partial<VehicleFormData>) => {\r\n      const result = await apiService.put<Vehicle>(`/admin/vehicles/${id}`, data);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Eliminar un vehículo (admin)\r\n    delete: async (id: string): Promise<any> => {\r\n      const result = await apiService.delete(`/admin/vehicles/${id}`);\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n\r\n    // Actualizar estado de un vehículo (admin)\r\n    updateStatus: async (id: string, status: string) => {\r\n      const result = await apiService.patch<Vehicle>(`/admin/vehicles/${id}/status`, { status });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Obtener todos los vehículos para el administrador\r\n    getAll: async (params: { page: number; limit: number }) => {\r\n      const result = await apiService.get<VehicleResponse>('/admin/vehicles', { params });\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n    // Nuevo método para obtener estadísticas\r\n    getStats: async (): Promise<VehicleStats> => {\r\n      const result = await apiService.get<VehicleStats>('/admin/vehicles/stats');\r\n      if (result.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    },\r\n  },\r\n\r\n  // Obtener fechas no disponibles para un vehículo\r\n  getUnavailableDates: async (vehicleId: string): Promise<string[]> => {\r\n    const result = await apiService.get</* string[] */\r\n      { date: string, by: string, reason?: string }[]\r\n    >(`/reservations/unavailable-dates/${vehicleId}`);\r\n    if (result.success) {\r\n      // return { data: result.data };\r\n      // return { data: result.data.map((item) => item.date) };\r\n      return result.data.map((item) => item.date);\r\n    } else {\r\n      throw new Error(result.error);\r\n    }\r\n  },\r\n};\r\n\r\nexport const getVehicleById = cache(vehiclesApi.getById);\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AA6JO,MAAM,cAAc;IACzB,wCAAwC;IACxC,QAAQ,OAAO;QACb,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,aAAa;YAAE;QAAO;QAC3E,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,uCAAuC;IACvC,SAAS,OAAO;QACd,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI;QAC9D,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,sCAAsC;IACtC,gBAAgB;QACd,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAkB;QACrD,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,oCAAoC;IACpC,eAAe;QACb,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAoB;QACvD,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6DAA6D;IAC7D,kBAAkB,OAAO;QACvB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,CAAC,eAAe,EAAE,IAAI;QAC3E,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,yBAAyB,OAAO;QAC9B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAuB,CAAC,UAAU,EAAE,UAAU,aAAa,CAAC;QAC/F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAIA,MAAM;QACJ,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,kBAAkB;gBAAE;YAAO;YAChF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,SAAS,OAAO;YACd,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,eAAe,EAAE,IAAI;YACnE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAU,kBAAkB;YAChE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,0DAA0D;gBAC1D,MAAM,IAAI,MAAM,OAAO,KAAK,CAAC,OAAO;YACtC;QACF;QAEA,aAAa,OAAO,WAAmB;YACrC,MAAM,WAAW,IAAI;YAErB,iIAAiI;YACjI,IAAI,KAAK,MAAM,EAAE;gBACf,KAAK,MAAM,QAAQ,KAAK,MAAM,CAAE;oBAC9B,SAAS,MAAM,CAAC,UAAU;gBAC5B;YACF;YACA,IAAI,KAAK,WAAW,EAAE;gBACpB,KAAK,MAAM,QAAQ,KAAK,WAAW,CAAE;oBACnC,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YACA,IAAI,KAAK,aAAa,EAAE;gBACtB,KAAK,MAAM,QAAQ,KAAK,aAAa,CAAE;oBACrC,SAAS,MAAM,CAAC,iBAAiB;gBACnC;YACF;YACA,IAAI,KAAK,oBAAoB,EAAE;gBAC7B,KAAK,MAAM,QAAQ,KAAK,oBAAoB,CAAE;oBAC5C,SAAS,MAAM,CAAC,wBAAwB;gBAC1C;YACF;YACA,IAAI,KAAK,uBAAuB,EAAE;gBAChC,KAAK,MAAM,QAAQ,KAAK,uBAAuB,CAAE;oBAC/C,SAAS,MAAM,CAAC,2BAA2B;gBAC7C;YACF;YAEA,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE,UAAU;gBACnG,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,yCAAyC;QACzC,aAAa,OAAO,WAAmB,MAAgC,iBAA2B,EAAE;YAElG,MAAM,WAAW,IAAI;YAErB,kEAAkE;YAClE,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;gBACzC,KAAK,MAAM,QAAQ,KAAK,MAAM,CAAE;oBAC9B,SAAS,MAAM,CAAC,UAAU;gBAC5B;YACF;YAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,MAAM,QAAQ,KAAK,WAAW,CAAE;oBACnC,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,MAAM,GAAG,GAAG;gBACvD,KAAK,MAAM,QAAQ,KAAK,aAAa,CAAE;oBACrC,SAAS,MAAM,CAAC,iBAAiB;gBACnC;YACF;YAEA,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,CAAC,MAAM,GAAG,GAAG;gBACrE,KAAK,MAAM,QAAQ,KAAK,oBAAoB,CAAE;oBAC5C,SAAS,MAAM,CAAC,wBAAwB;gBAC1C;YACF;YAEA,IAAI,KAAK,uBAAuB,IAAI,KAAK,uBAAuB,CAAC,MAAM,GAAG,GAAG;gBAC3E,KAAK,MAAM,QAAQ,KAAK,uBAAuB,CAAE;oBAC/C,SAAS,MAAM,CAAC,2BAA2B;gBAC7C;YACF;YAEA,8BAA8B;YAC9B,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,SAAS,MAAM,CAAC,kBAAkB,KAAK,SAAS,CAAC;YACnD;YAEA,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE,UAAU;gBACnG,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,WAAW;YACvB,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;YAC3C;QACF;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,eAAe,EAAE,IAAI,EAAE;YACrE,QAAQ,GAAG,CAAC,6BAA6B;YACzC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,OAAO,KAAK;gBACrD,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,8BAA8B;QAC9B,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;YAC7D,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,YAAY,OAAO,WAAmB;YAMpC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,OAAO,EAAE;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,kCAAkC;gBACrE;gBACA,WAAW,KAAK,SAAS;gBACzB,SAAS,KAAK,OAAO;gBACrB,QAAQ,KAAK,MAAM,IAAI;YACzB;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,6CAA6C;QAC7C,4BAA4B,OAAO,WAAmB;YACpD,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAuB,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,EAAE;YACtG,QAAQ,GAAG,CAAC,WAAW,OAAO,KAAK;YACnC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAChC;QACF;QACE,UAAU;YACR,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAC;YACpC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,oCAAoC;QACpC,cAAc,OAAO;YACnB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAmB,CAAC,eAAe,EAAE,UAAU,UAAU,CAAC;YAC7F,QAAQ,GAAG,CAAC,4BAA4B;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,eAAe,OAAO;YACpB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,UAAU,eAAe,CAAC;YACjF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;IACF;IAIA,0CAA0C;IAC1C,cAAc,OAAO,IAAY;QAC/B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAkB,CAAC,eAAe,EAAE,GAAG,OAAO,CAAC,EAAE;YAAE;QAAO;QAC/F,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,6CAA6C;IAC7C,OAAO;QAEL,SAAS,OAAO;YACd,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,gBAAgB,EAAE,IAAI;YACpE,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,GAAG,CAAC,oBAAoB,OAAO,IAAI;gBAC3C,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,6CAA6C;QAC7C,oBAAoB;YAClB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAY;YAC/C,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,CAAC,OAAO;YACtC;QACF;QAEA,sBAAsB;QACtB,gBAAgB,OAAO;YACrB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAU,CAAC,gBAAgB,EAAE,GAAG,QAAQ,CAAC;YAC7E,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,uBAAuB;QACvB,eAAe,OAAO,IAAY;YAChC,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE;YAAO;YAC9E,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,iCAAiC;QACjC,QAAQ,OAAO,IAAY;YACzB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAU,CAAC,gBAAgB,EAAE,IAAI,EAAE;YACtE,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,+BAA+B;QAC/B,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,IAAI;YAC9D,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QAEA,2CAA2C;QAC3C,cAAc,OAAO,IAAY;YAC/B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAU,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE;YAAO;YACxF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,oDAAoD;QACpD,QAAQ,OAAO;YACb,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAkB,mBAAmB;gBAAE;YAAO;YACjF,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;QACA,yCAAyC;QACzC,UAAU;YACR,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAe;YAClD,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF;IACF;IAEA,iDAAiD;IACjD,qBAAqB,OAAO;QAC1B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAEjC,CAAC,gCAAgC,EAAE,WAAW;QAChD,IAAI,OAAO,OAAO,EAAE;YAClB,gCAAgC;YAChC,yDAAyD;YACzD,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;QAC5C,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;AACF;AAEO,MAAM,iBAAiB,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,OAAO"}}, {"offset": {"line": 1605, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { ButtonProps, buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\r\n  <nav\r\n    role=\"navigation\"\r\n    aria-label=\"pagination\"\r\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n    {...props}\r\n  />\r\n)\r\nPagination.displayName = \"Pagination\"\r\n\r\nconst PaginationContent = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    className={cn(\"flex flex-row items-center gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nPaginationContent.displayName = \"PaginationContent\"\r\n\r\nconst PaginationItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li ref={ref} className={cn(\"\", className)} {...props} />\r\n))\r\nPaginationItem.displayName = \"PaginationItem\"\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<ButtonProps, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nconst PaginationLink = ({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) => (\r\n  <a\r\n    aria-current={isActive ? \"page\" : undefined}\r\n    className={cn(\r\n      buttonVariants({\r\n        variant: isActive ? \"outline\" : \"ghost\",\r\n        size,\r\n      }),\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nPaginationLink.displayName = \"PaginationLink\"\r\n\r\nconst PaginationPrevious = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to previous page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pl-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronLeft className=\"h-4 w-4\" />\r\n    <span>Previous</span>\r\n  </PaginationLink>\r\n)\r\nPaginationPrevious.displayName = \"PaginationPrevious\"\r\n\r\nconst PaginationNext = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to next page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pr-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <span>Next</span>\r\n    <ChevronRight className=\"h-4 w-4\" />\r\n  </PaginationLink>\r\n)\r\nPaginationNext.displayName = \"PaginationNext\"\r\n\r\nconst PaginationEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"h-4 w-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n)\r\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AAAA;AAEA;AACA;;;;;;AAEA,MAAM,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,OAAoC,iBACtE,kMAAC;QACC,MAAK;QACL,cAAW;QACX,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,kCAAoB,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,+BAAiB,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;;;;;;AAEvD,eAAe,WAAW,GAAG;AAO7B,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB,iBACpB,kMAAC;QACC,gBAAc,WAAW,SAAS;QAClC,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACyC,iBAC5C,kMAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;0BAET,kMAAC,4NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,kMAAC;0BAAK;;;;;;;;;;;;AAGV,mBAAmB,WAAW,GAAG;AAEjC,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,GAAG,OACyC,iBAC5C,kMAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;0BAET,kMAAC;0BAAK;;;;;;0BACN,kMAAC,8NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,kMAAC;QACC,aAAW;QACX,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,kMAAC,wNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,kMAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG"}}, {"offset": {"line": 1758, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/pagination-control.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { usePathname, useSearchParams } from \"next/navigation\"\nimport {\n  Pagination,\n  PaginationContent,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n  PaginationEllipsis\n} from \"@/components/ui/pagination\"\n\ninterface PaginationControlProps {\n  currentPage: number\n  totalPages: number\n  baseUrl?: string\n  className?: string\n  onPageChange?: (page: number) => void\n}\n\nexport function PaginationControl({\n  currentPage,\n  totalPages,\n  baseUrl,\n  className = \"\",\n  onPageChange\n}: PaginationControlProps) {\n  const pathname = usePathname()\n  const searchParams = useSearchParams()\n  const [windowWidth, setWindowWidth] = useState(\n    typeof window !== 'undefined' ? window.innerWidth : 0\n  )\n\n  // Actualizar el ancho de la ventana cuando cambia el tamaño\n  useEffect(() => {\n    const handleResize = () => setWindowWidth(window.innerWidth)\n    if (typeof window !== 'undefined') {\n      window.addEventListener('resize', handleResize)\n      return () => window.removeEventListener('resize', handleResize)\n    }\n  }, [])\n\n  // Función para crear una nueva URL con parámetros de búsqueda actualizados\n  const createPageUrl = (pageNumber: number) => {\n    if (onPageChange) return \"#\" // Si se usa onPageChange, el href es solo un placeholder\n\n    const params = new URLSearchParams(searchParams.toString())\n    params.set('page', pageNumber.toString())\n\n    if (baseUrl) {\n      return `${baseUrl}?${params.toString()}`\n    }\n\n    return `${pathname}?${params.toString()}`\n  }\n\n  // Calcular cuántos números de página mostrar basado en el ancho de la ventana\n  const getVisiblePageNumbers = () => {\n    if (totalPages <= 1) return [1]\n\n    let maxVisible = 1 // Mínimo siempre mostrar al menos 1\n\n    if (windowWidth > 640) maxVisible = 3 // sm\n    if (windowWidth > 768) maxVisible = 5 // md\n    if (windowWidth > 1024) maxVisible = 7 // lg\n\n    // Asegurarse de no mostrar más páginas de las que existen\n    maxVisible = Math.min(maxVisible, totalPages)\n\n    // Calcular el rango de páginas a mostrar\n    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2))\n    const endPage = Math.min(totalPages, startPage + maxVisible - 1)\n\n    // Ajustar si estamos cerca del final\n    if (endPage - startPage + 1 < maxVisible) {\n      startPage = Math.max(1, endPage - maxVisible + 1)\n    }\n\n    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i)\n  }\n\n  const handleClick = (pageNumber: number, e: React.MouseEvent) => {\n    if (onPageChange) {\n      e.preventDefault()\n      onPageChange(pageNumber)\n    }\n  }\n\n  const visiblePages = getVisiblePageNumbers()\n  const showLeftEllipsis = totalPages > 0 && visiblePages[0] > 1\n  const showRightEllipsis = totalPages > 0 && visiblePages[visiblePages.length - 1] < totalPages\n\n  if (totalPages <= 1) {\n    return null\n  }\n\n  return (\n    <Pagination className={className}>\n      <PaginationContent>\n        {currentPage > 1 && (\n          <PaginationItem>\n            <PaginationPrevious\n              href={createPageUrl(currentPage - 1)}\n              onClick={(e) => handleClick(currentPage - 1, e)}\n            />\n          </PaginationItem>\n        )}\n\n        {showLeftEllipsis && (\n          <>\n            <PaginationItem>\n              <PaginationLink\n                href={createPageUrl(1)}\n                onClick={(e) => handleClick(1, e)}\n              >\n                1\n              </PaginationLink>\n            </PaginationItem>\n            <PaginationItem>\n              <PaginationEllipsis />\n            </PaginationItem>\n          </>\n        )}\n\n        {visiblePages.map((pageNumber) => (\n          <PaginationItem key={pageNumber}>\n            <PaginationLink\n              href={createPageUrl(pageNumber)}\n              isActive={pageNumber === currentPage}\n              onClick={(e) => handleClick(pageNumber, e)}\n            >\n              {pageNumber}\n            </PaginationLink>\n          </PaginationItem>\n        ))}\n\n        {showRightEllipsis && (\n          <>\n            <PaginationItem>\n              <PaginationEllipsis />\n            </PaginationItem>\n            <PaginationItem>\n              <PaginationLink\n                href={createPageUrl(totalPages)}\n                onClick={(e) => handleClick(totalPages, e)}\n              >\n                {totalPages}\n              </PaginationLink>\n            </PaginationItem>\n          </>\n        )}\n\n        {currentPage < totalPages && (\n          <PaginationItem>\n            <PaginationNext\n              href={createPageUrl(currentPage + 1)}\n              onClick={(e) => handleClick(currentPage + 1, e)}\n            />\n          </PaginationItem>\n        )}\n      </PaginationContent>\n    </Pagination>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAsBO,SAAS,kBAAkB,EAChC,WAAW,EACX,UAAU,EACV,OAAO,EACP,YAAY,EAAE,EACd,YAAY,EACW;IACvB,MAAM,WAAW,CAAA,GAAA,iMAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,iMAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAC3C,6EAAoD;IAGtD,4DAA4D;IAC5D,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,IAAM,eAAe,OAAO,UAAU;QAC3D,uCAAmC;;QAGnC;IACF,GAAG,EAAE;IAEL,2EAA2E;IAC3E,MAAM,gBAAgB,CAAC;QACrB,IAAI,cAAc,OAAO,IAAI,yDAAyD;;QAEtF,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;QACxD,OAAO,GAAG,CAAC,QAAQ,WAAW,QAAQ;QAEtC,IAAI,SAAS;YACX,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;QAC1C;QAEA,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC3C;IAEA,8EAA8E;IAC9E,MAAM,wBAAwB;QAC5B,IAAI,cAAc,GAAG,OAAO;YAAC;SAAE;QAE/B,IAAI,aAAa,EAAE,oCAAoC;;QAEvD,IAAI,cAAc,KAAK,aAAa,EAAE,KAAK;;QAC3C,IAAI,cAAc,KAAK,aAAa,EAAE,KAAK;;QAC3C,IAAI,cAAc,MAAM,aAAa,EAAE,KAAK;;QAE5C,0DAA0D;QAC1D,aAAa,KAAK,GAAG,CAAC,YAAY;QAElC,yCAAyC;QACzC,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,aAAa;QAClE,MAAM,UAAU,KAAK,GAAG,CAAC,YAAY,YAAY,aAAa;QAE9D,qCAAqC;QACrC,IAAI,UAAU,YAAY,IAAI,YAAY;YACxC,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,aAAa;QACjD;QAEA,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU,YAAY;QAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAC/E;IAEA,MAAM,cAAc,CAAC,YAAoB;QACvC,IAAI,cAAc;YAChB,EAAE,cAAc;YAChB,aAAa;QACf;IACF;IAEA,MAAM,eAAe;IACrB,MAAM,mBAAmB,aAAa,KAAK,YAAY,CAAC,EAAE,GAAG;IAC7D,MAAM,oBAAoB,aAAa,KAAK,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,GAAG;IAEpF,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IAEA,qBACE,kMAAC,8IAAA,CAAA,aAAU;QAAC,WAAW;kBACrB,cAAA,kMAAC,8IAAA,CAAA,oBAAiB;;gBACf,cAAc,mBACb,kMAAC,8IAAA,CAAA,iBAAc;8BACb,cAAA,kMAAC,8IAAA,CAAA,qBAAkB;wBACjB,MAAM,cAAc,cAAc;wBAClC,SAAS,CAAC,IAAM,YAAY,cAAc,GAAG;;;;;;;;;;;gBAKlD,kCACC;;sCACE,kMAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,kMAAC,8IAAA,CAAA,iBAAc;gCACb,MAAM,cAAc;gCACpB,SAAS,CAAC,IAAM,YAAY,GAAG;0CAChC;;;;;;;;;;;sCAIH,kMAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,kMAAC,8IAAA,CAAA,qBAAkB;;;;;;;;;;;;gBAKxB,aAAa,GAAG,CAAC,CAAC,2BACjB,kMAAC,8IAAA,CAAA,iBAAc;kCACb,cAAA,kMAAC,8IAAA,CAAA,iBAAc;4BACb,MAAM,cAAc;4BACpB,UAAU,eAAe;4BACzB,SAAS,CAAC,IAAM,YAAY,YAAY;sCAEvC;;;;;;uBANgB;;;;;gBAWtB,mCACC;;sCACE,kMAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,kMAAC,8IAAA,CAAA,qBAAkB;;;;;;;;;;sCAErB,kMAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,kMAAC,8IAAA,CAAA,iBAAc;gCACb,MAAM,cAAc;gCACpB,SAAS,CAAC,IAAM,YAAY,YAAY;0CAEvC;;;;;;;;;;;;;gBAMR,cAAc,4BACb,kMAAC,8IAAA,CAAA,iBAAc;8BACb,cAAA,kMAAC,8IAAA,CAAA,iBAAc;wBACb,MAAM,cAAc,cAAc;wBAClC,SAAS,CAAC,IAAM,YAAY,cAAc,GAAG;;;;;;;;;;;;;;;;;;;;;;AAO3D"}}, {"offset": {"line": 1957, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(root)/vehicles/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Link from \"next/link\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { Slider } from \"@/components/ui/slider\"\r\nimport { Checkbox } from \"@/components/ui/checkbox\"\r\nimport VehicleCard from \"@/components/vehicles/vehicle-card\"\r\nimport { vehiclesApi } from \"@/lib/api/vehicles.api\"\r\nimport { useQuery } from \"@tanstack/react-query\"\r\nimport { useSearchParams } from 'next/navigation'\r\nimport { PaginationControl } from \"@/components/ui/pagination-control\"\r\n\r\nexport default function VehiclesPage() {\r\n  const searchParams = useSearchParams()\r\n  const page = Number(searchParams.get('page') || 1)\r\n  const limit = Number(searchParams.get('limit') || 10)\r\n\r\n  const { data, isLoading, error } = useQuery({\r\n    queryKey: ['vehicles', page, limit],\r\n    queryFn: () => vehiclesApi.getAll({ page, limit }),\r\n    staleTime: 60 * 1000, // 1 minuto\r\n  })\r\n\r\n  const vehicles = data?.data\r\n  const pagination = data?.pagination\r\n  console.log('pagination', pagination)\r\n\r\n  return (\r\n    <div className=\"bg-background\">\r\n      <div className=\"container mx-auto py-10\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n          {/* Filtros */}\r\n          <div className=\"space-y-6\">\r\n            <div>\r\n              <h2 className=\"text-xl font-semibold mb-4\">Filtros</h2>\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <Label htmlFor=\"location\">Ubicación</Label>\r\n                  <Input id=\"location\" placeholder=\"Ciudad, estado o código postal\" />\r\n                </div>\r\n\r\n                <div>\r\n                  <Label>Rango de Precio</Label>\r\n                  <div className=\"pt-2\">\r\n                    <Slider defaultValue={[0, 500]} max={1000} step={10} />\r\n                  </div>\r\n                  <div className=\"flex justify-between text-xs text-muted-foreground mt-1\">\r\n                    <span>$0</span>\r\n                    <span>$1000+</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label className=\"mb-2 block\">Tipo de Vehículo</Label>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"sedan\" />\r\n                      <label htmlFor=\"sedan\" className=\"text-sm\">Sedán</label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"suv\" />\r\n                      <label htmlFor=\"suv\" className=\"text-sm\">SUV</label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"luxury\" />\r\n                      <label htmlFor=\"luxury\" className=\"text-sm\">Lujo</label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"hatchback\" />\r\n                      <label htmlFor=\"hatchback\" className=\"text-sm\">Hatchback</label>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label className=\"mb-2 block\">Características</Label>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"airConditioning\" />\r\n                      <label htmlFor=\"airConditioning\" className=\"text-sm\">Aire Acondicionado</label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"sunroof\" />\r\n                      <label htmlFor=\"sunroof\" className=\"text-sm\">Techo Corredizo</label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"gps\" />\r\n                      <label htmlFor=\"gps\" className=\"text-sm\">GPS</label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"navigation\" />\r\n                      <label htmlFor=\"navigation\" className=\"text-sm\">Navegación</label>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label className=\"mb-2 block\">Disponibilidad</Label>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"available\" />\r\n                      <label htmlFor=\"available\" className=\"text-sm\">Disponible</label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Checkbox id=\"reserved\" />\r\n                      <label htmlFor=\"reserved\" className=\"text-sm\">Reservado</label>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Lista de Vehículos */}\r\n          <div className=\"md:col-span-3\">\r\n            {isLoading ? (\r\n              <div className=\"flex justify-center items-center h-64\">\r\n                <div className=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary\"></div>\r\n              </div>\r\n            ) : error ? (\r\n                <div className=\"text-center text-red-500\">No se pudieron cargar los vehículos. Por favor, intenta de nuevo más tarde.</div>\r\n              ) : !vehicles || vehicles.length === 0 ? (\r\n              <div className=\"text-center\">No se encontraron vehículos.</div>\r\n            ) : (\r\n                    <>\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                        {vehicles.map((vehicle) => (\r\n                          <Link key={vehicle.id} href={`/vehicles/${vehicle.id}`} prefetch={false}>\r\n                            <VehicleCard\r\n                              id={vehicle.id}\r\n                              make={vehicle.make}\r\n                              model={vehicle.model}\r\n                              price={vehicle.price}\r\n                              rating={vehicle.rating}\r\n                              reviews={vehicle.reviews}\r\n                              images={vehicle.images}\r\n                              features={vehicle.features}\r\n                              year={vehicle.year}\r\n                              engineSize={vehicle.engineSize}\r\n                              transmission={vehicle.transmission}\r\n                              trim={vehicle.trim}\r\n                              bodyType={vehicle.bodyType}\r\n                              createdAt={vehicle.createdAt}\r\n                            />\r\n                          </Link>\r\n                        ))}\r\n                      </div>\r\n\r\n                      {pagination && (\r\n                        <PaginationControl\r\n                          currentPage={page}\r\n                          totalPages={pagination.totalPages}\r\n                          className=\"mt-8\"\r\n                        />\r\n                      )}\r\n                    </>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,iMAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,OAAO,OAAO,aAAa,GAAG,CAAC,WAAW;IAChD,MAAM,QAAQ,OAAO,aAAa,GAAG,CAAC,YAAY;IAElD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE;QAC1C,UAAU;YAAC;YAAY;YAAM;SAAM;QACnC,SAAS,IAAM,4IAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBAAE;gBAAM;YAAM;QAChD,WAAW,KAAK;IAClB;IAEA,MAAM,WAAW,MAAM;IACvB,MAAM,aAAa,MAAM;IACzB,QAAQ,GAAG,CAAC,cAAc;IAE1B,qBACE,kMAAC;QAAI,WAAU;kBACb,cAAA,kMAAC;YAAI,WAAU;sBACb,cAAA,kMAAC;gBAAI,WAAU;;kCAEb,kMAAC;wBAAI,WAAU;kCACb,cAAA,kMAAC;;8CACC,kMAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;;8DACC,kMAAC,yIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,kMAAC,yIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAW,aAAY;;;;;;;;;;;;sDAGnC,kMAAC;;8DACC,kMAAC,yIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,kMAAC;oDAAI,WAAU;8DACb,cAAA,kMAAC,0IAAA,CAAA,SAAM;wDAAC,cAAc;4DAAC;4DAAG;yDAAI;wDAAE,KAAK;wDAAM,MAAM;;;;;;;;;;;8DAEnD,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;sEAAK;;;;;;sEACN,kMAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAIV,kMAAC;;8DACC,kMAAC,yIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAa;;;;;;8DAC9B,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAQ,WAAU;8EAAU;;;;;;;;;;;;sEAE7C,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAM,WAAU;8EAAU;;;;;;;;;;;;sEAE3C,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAS,WAAU;8EAAU;;;;;;;;;;;;sEAE9C,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAY,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAKrD,kMAAC;;8DACC,kMAAC,yIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAa;;;;;;8DAC9B,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAkB,WAAU;8EAAU;;;;;;;;;;;;sEAEvD,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAU,WAAU;8EAAU;;;;;;;;;;;;sEAE/C,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAM,WAAU;8EAAU;;;;;;;;;;;;sEAE3C,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAa,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAKtD,kMAAC;;8DACC,kMAAC,yIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAa;;;;;;8DAC9B,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAY,WAAU;8EAAU;;;;;;;;;;;;sEAEjD,kMAAC;4DAAI,WAAU;;8EACb,kMAAC,4IAAA,CAAA,WAAQ;oEAAC,IAAG;;;;;;8EACb,kMAAC;oEAAM,SAAQ;oEAAW,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1D,kMAAC;wBAAI,WAAU;kCACZ,0BACC,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,sBACA,kMAAC;4BAAI,WAAU;sCAA2B;;;;;mCACxC,CAAC,YAAY,SAAS,MAAM,KAAK,kBACrC,kMAAC;4BAAI,WAAU;sCAAc;;;;;iDAEvB;;8CACE,kMAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,kMAAC,2KAAA,CAAA,UAAI;4CAAkB,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;4CAAE,UAAU;sDAChE,cAAA,kMAAC,yJAAA,CAAA,UAAW;gDACV,IAAI,QAAQ,EAAE;gDACd,MAAM,QAAQ,IAAI;gDAClB,OAAO,QAAQ,KAAK;gDACpB,OAAO,QAAQ,KAAK;gDACpB,QAAQ,QAAQ,MAAM;gDACtB,SAAS,QAAQ,OAAO;gDACxB,QAAQ,QAAQ,MAAM;gDACtB,UAAU,QAAQ,QAAQ;gDAC1B,MAAM,QAAQ,IAAI;gDAClB,YAAY,QAAQ,UAAU;gDAC9B,cAAc,QAAQ,YAAY;gDAClC,MAAM,QAAQ,IAAI;gDAClB,UAAU,QAAQ,QAAQ;gDAC1B,WAAW,QAAQ,SAAS;;;;;;2CAfrB,QAAQ,EAAE;;;;;;;;;;gCAqBxB,4BACC,kMAAC,yJAAA,CAAA,oBAAiB;oCAChB,aAAa;oCACb,YAAY,WAAW,UAAU;oCACjC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpC"}}]}