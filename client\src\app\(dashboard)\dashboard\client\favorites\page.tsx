"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Heart, Search, Star, MapPin, Grid, List, Loader2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Image from 'next/image'
import Link from 'next/link'
import { useQuery } from "@tanstack/react-query"
import { favoritesApi } from "@/lib/api/favorites.api"
import { useUser } from "@/context/user-context"
import FavoriteButton from "@/components/vehicles/favorite-button"
import { PaginationControl } from "@/components/ui/pagination-control"
import { useRouter, useSearchParams } from "next/navigation"

// Mapeo de tipos de carrocería en español
const bodyTypeLabels: Record<string, string> = {
  sedan: "Sedan",
  suv: "SUV",
  hatchback: "Hatchback",
  pickup: "Pickup",
  coupe: "Coupe",
  convertible: "Convertible",
  wagon: "Wagon",
  van: "Van",
  minivan: "Minivan",
  targa: "Targa",
  doublecab: "Doble Cabina",
  truck: "Camioneta"
};

export default function FavoritesPage() {
  const { user } = useUser()
  const router = useRouter()
  const searchParams = useSearchParams()

  // Verificar que el usuario sea de tipo client ANTES de cualquier hook
  if (!user || user.userType !== 'client') {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
        <Heart className="h-16 w-16 text-gray-300 mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Acceso restringido</h2>
        <p className="text-gray-600 mb-4">
          Solo los usuarios de tipo cliente pueden acceder a la página de favoritos.
        </p>
        <Button onClick={() => router.push('/dashboard')}>
          Volver al dashboard
        </Button>
      </div>
    )
  }

  // Estados para filtros y vista
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedAvailability, setSelectedAvailability] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  // Paginación
  const currentPage = parseInt(searchParams.get('page') || '1')
  const limit = 12

  // Query para obtener favoritos
  const { data: favoritesData, isLoading, error } = useQuery({
    queryKey: ['favorites', currentPage, limit],
    queryFn: () => favoritesApi.getFavorites({ page: currentPage, limit }),
    staleTime: 1000 * 60 * 5, // 5 minutos
  })

  const vehicles = favoritesData?.data || []
  const pagination = favoritesData?.pagination

  // Filtrar vehículos localmente
  const filteredVehicles = vehicles.filter((vehicle: any) => {
    const matchesSearch = 
      vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.features?.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.host?.name?.toLowerCase().includes(searchTerm.toLowerCase())

    const vehicleCategory = bodyTypeLabels[vehicle.bodyType] || vehicle.bodyType
    const matchesCategory = selectedCategory === "all" || vehicleCategory === selectedCategory

    const isAvailable = vehicle.status === "active"
    const matchesAvailability = selectedAvailability === "all" ||
      (selectedAvailability === "available" && isAvailable) ||
      (selectedAvailability === "unavailable" && !isAvailable)

    return matchesSearch && matchesCategory && matchesAvailability
  })

  const VehicleCard = ({ vehicle, isListView = false }: { vehicle: any; isListView?: boolean }) => (
    <Link href={`/vehicles/${vehicle.id}`} key={vehicle.id}>
      <Card className={`overflow-hidden hover:shadow-lg transition-shadow cursor-pointer ${isListView ? "flex" : ""}`}>
        <div className={`relative ${isListView ? "w-48 flex-shrink-0" : ""}`}>
          <Image
            src={vehicle.images?.[0] || "/placeholder.svg"}
            alt={`${vehicle.make} ${vehicle.model}`}
            width={300}
            height={200}
            className={`object-cover ${isListView ? "h-full w-full" : "w-full h-48"}`}
            unoptimized={true}
          />
          <div className="absolute top-2 right-2 z-10" onClick={(e) => e.preventDefault()}>
            <FavoriteButton vehicleId={vehicle.id} className="bg-white hover:bg-gray-50" />
          </div>
          {vehicle.status !== "active" && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <Badge variant="secondary" className="bg-white text-black">
                No disponible
              </Badge>
            </div>
          )}
        </div>
        <CardContent className={`p-4 ${isListView ? "flex-1" : ""}`}>
          <div className="flex justify-between items-start mb-2">
            <h3 className="font-semibold text-lg">
              {vehicle.make} {vehicle.model} {vehicle.year}
            </h3>
            <div className="text-right">
              <div className="text-xl font-bold">${vehicle.price}</div>
              <div className="text-sm text-gray-500">por día</div>
            </div>
          </div>

          <div className="flex items-center mb-2">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
            <span className="text-sm font-medium">{vehicle.rating || 0}</span>
            <span className="text-sm text-gray-500 ml-1">({vehicle.reviews || 0} reseñas)</span>
          </div>

          <div className="flex items-center text-sm text-gray-600 mb-3">
            <MapPin className="h-4 w-4 mr-1" />
            {vehicle.features?.location || 'Ubicación no especificada'}
          </div>

          <div className="text-sm text-gray-600 mb-3">
            {vehicle.engineSize}L • {vehicle.transmission === 'automatic' ? 'Automático' : 'Manual'} • {bodyTypeLabels[vehicle.bodyType] || vehicle.bodyType}
          </div>

          <div className="flex flex-wrap gap-1 mb-3">
            {vehicle.amenities?.slice(0, 3).map((amenity: string, index: number) => (
              <Badge key={index} variant="outline" className="text-xs">
                {amenity}
              </Badge>
            ))}
            {vehicle.amenities?.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{vehicle.amenities.length - 3} más
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  )

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
        <Heart className="h-16 w-16 text-gray-300 mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Error al cargar favoritos</h2>
        <p className="text-gray-600 mb-4">
          No se pudieron cargar tus vehículos favoritos. Por favor, intenta de nuevo.
        </p>
        <Button onClick={() => window.location.reload()}>
          Reintentar
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Favoritos</h1>
        <p className="text-muted-foreground">
          Tus vehículos guardados para futuras reservas ({pagination?.total || 0} vehículos)
        </p>
      </div>

      {/* Filtros y controles */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por marca, modelo, ubicación..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Categoría" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas las categorías</SelectItem>
                {Object.entries(bodyTypeLabels).map(([key, label]) => (
                  <SelectItem key={key} value={label}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedAvailability} onValueChange={setSelectedAvailability}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Disponibilidad" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="available">Disponibles</SelectItem>
                <SelectItem value="unavailable">No disponibles</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de vehículos */}
      <div>
        {filteredVehicles.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Heart className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No se encontraron favoritos</h3>
              <p className="text-muted-foreground text-center">
                {vehicles.length === 0
                  ? "Aún no tienes vehículos favoritos. Explora nuestro catálogo y guarda los que más te gusten."
                  : "No tienes vehículos favoritos que coincidan con los filtros seleccionados."
                }
              </p>
              {vehicles.length === 0 && (
                <Button className="mt-4" onClick={() => router.push('/vehicles')}>
                  Explorar vehículos
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
            {filteredVehicles.map((vehicle) => (
              <VehicleCard key={vehicle.id} vehicle={vehicle} isListView={viewMode === "list"} />
            ))}
          </div>
        )}
      </div>

      {/* Paginación */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center">
          <PaginationControl
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            baseUrl="/dashboard/client/favorites"
          />
        </div>
      )}
    </div>
  )
}
